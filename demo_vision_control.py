#!/usr/bin/env python3
"""
视觉控制系统演示脚本

模拟视觉检测和控制流程，无需实际摄像头
"""

import time
import random
import sys
import os
from typing import Dict, Any

# 添加Control目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'Control'))

try:
    from Control.local_robot_controller import get_controller
    CONTROLLER_AVAILABLE = True
except ImportError:
    CONTROLLER_AVAILABLE = False
    print("❌ 无法导入控制器模块")

class VisionControlDemo:
    """视觉控制演示类"""
    
    def __init__(self):
        self.controller = None
        self.target_class = "redcar"
        self.detection_threshold = 20
        self.consecutive_detections = 0
        self.last_control_time = 0
        self.control_cooldown = 2.0
        
        if CONTROLLER_AVAILABLE:
            self.controller = get_controller()
            print("✅ 控制器初始化成功")
        else:
            print("❌ 控制器不可用")
    
    def simulate_detection(self) -> Dict[str, Any]:
        """模拟目标检测"""
        # 随机生成检测结果
        detected = random.random() > 0.3  # 70%概率检测到目标
        
        if detected:
            # 模拟目标信息
            distance = random.uniform(0.5, 4.0)  # 0.5-4.0米
            azimuth = random.uniform(-30, 30)    # -30到30度
            confidence = random.uniform(0.7, 0.95)  # 70-95%置信度
            
            return {
                'detected': True,
                'class_name': self.target_class,
                'confidence': confidence,
                'distance': distance,
                'azimuth': azimuth,
                'bbox': (100, 100, 200, 200),  # 模拟边界框
                'center': (150, 150)
            }
        else:
            return {'detected': False}
    
    def process_detection(self, detection_result: Dict[str, Any]) -> bool:
        """处理检测结果"""
        current_time = time.time()
        
        if detection_result['detected']:
            self.consecutive_detections += 1
            print(f"🎯 检测到 {self.target_class} (连续 {self.consecutive_detections} 帧) - "
                  f"置信度: {detection_result['confidence']:.2f}, "
                  f"距离: {detection_result['distance']:.2f}m, "
                  f"方位角: {detection_result['azimuth']:.1f}°")
        else:
            if self.consecutive_detections > 0:
                print("❌ 目标丢失")
            self.consecutive_detections = 0
        
        # 检查是否达到触发条件
        if (self.consecutive_detections >= self.detection_threshold and 
            current_time - self.last_control_time > self.control_cooldown):
            
            self.trigger_control_action(detection_result)
            self.last_control_time = current_time
            self.consecutive_detections = 0
            return True
        
        return False
    
    def trigger_control_action(self, target_info: Dict[str, Any]):
        """触发控制动作"""
        if not self.controller:
            print("❌ 控制器不可用，无法执行控制动作")
            return
        
        print(f"\n🚗 触发控制动作！")
        
        distance = target_info['distance']
        azimuth = target_info['azimuth']
        
        # 决定控制策略
        if azimuth > 15:
            command = 'move_forward_right'
            params = {'speed': 0.25, 'duration': 0.8, 'angular_speed': 0.5}
            print(f"🔄 目标在右侧 ({azimuth:.1f}°)，执行右前方移动")
        elif azimuth < -15:
            command = 'move_forward_left'
            params = {'speed': 0.25, 'duration': 0.8, 'angular_speed': 0.5}
            print(f"🔄 目标在左侧 ({azimuth:.1f}°)，执行左前方移动")
        else:
            command = 'move_forward'
            params = {'speed': 0.25, 'duration': 1.0}
            print(f"⬆️ 目标在正前方 ({azimuth:.1f}°)，执行直线前进")
        
        # 根据距离调整参数
        if distance < 1.0:
            params['speed'] = 0.15
            params['duration'] = 0.5
            print(f"🐌 目标较近 ({distance:.2f}m)，减速前进")
        elif distance > 3.0:
            params['speed'] = 0.35
            params['duration'] = 1.5
            print(f"🚀 目标较远 ({distance:.2f}m)，加速前进")
        else:
            print(f"🎯 目标距离适中 ({distance:.2f}m)，正常速度")
        
        # 发送控制命令
        try:
            self.controller.send_command(command, params)
            print(f"✅ 控制命令已发送: {command} - {params}")
        except Exception as e:
            print(f"❌ 发送控制命令失败: {e}")
        
        print()  # 空行分隔
    
    def run_demo(self, duration: int = 60):
        """运行演示"""
        print(f"🎬 开始视觉控制演示 (持续 {duration} 秒)")
        print(f"📋 配置: 目标类别={self.target_class}, 检测阈值={self.detection_threshold}帧")
        print("=" * 60)
        
        start_time = time.time()
        frame_count = 0
        control_actions = 0
        
        try:
            while time.time() - start_time < duration:
                frame_count += 1
                
                # 模拟帧率 (约10 FPS)
                time.sleep(0.1)
                
                # 模拟检测
                detection_result = self.simulate_detection()
                
                # 处理检测结果
                action_triggered = self.process_detection(detection_result)
                if action_triggered:
                    control_actions += 1
                
                # 每50帧显示一次统计
                if frame_count % 50 == 0:
                    elapsed = time.time() - start_time
                    print(f"📊 统计 - 帧数: {frame_count}, 时间: {elapsed:.1f}s, "
                          f"控制动作: {control_actions}, 连续检测: {self.consecutive_detections}")
        
        except KeyboardInterrupt:
            print("\n⏹️ 演示被用户中断")
        
        # 最终统计
        total_time = time.time() - start_time
        print("\n" + "=" * 60)
        print("📊 演示结果统计:")
        print(f"   总时间: {total_time:.1f} 秒")
        print(f"   总帧数: {frame_count}")
        print(f"   平均帧率: {frame_count/total_time:.1f} FPS")
        print(f"   控制动作次数: {control_actions}")
        print(f"   控制频率: {control_actions/total_time*60:.1f} 次/分钟")
        
        if self.controller:
            status = self.controller.get_status()
            print(f"   控制器状态: {status}")

def interactive_demo():
    """交互式演示"""
    print("🎮 交互式控制演示")
    print("=" * 40)
    
    if not CONTROLLER_AVAILABLE:
        print("❌ 控制器不可用，无法运行交互式演示")
        return
    
    controller = get_controller()
    
    commands = {
        'w': ('move_forward', {'speed': 0.25, 'duration': 0.5}),
        's': ('move_backward', {'speed': 0.25, 'duration': 0.5}),
        'a': ('turn_left', {'angular_speed': 1.0, 'duration': 0.5}),
        'd': ('turn_right', {'angular_speed': 1.0, 'duration': 0.5}),
        'q': ('move_forward_left', {'speed': 0.25, 'duration': 0.5}),
        'e': ('move_forward_right', {'speed': 0.25, 'duration': 0.5}),
        ' ': ('stop', {}),
        'x': ('emergency_stop', {})
    }
    
    print("控制说明:")
    print("  W - 前进    S - 后退")
    print("  A - 左转    D - 右转")
    print("  Q - 左前    E - 右前")
    print("  空格 - 停止  X - 紧急停止")
    print("  ESC 或 Ctrl+C - 退出")
    print("\n按任意键开始...")
    
    try:
        while True:
            key = input("输入命令: ").lower().strip()
            
            if key == 'esc' or key == '':
                break
            
            if key in commands:
                command, params = commands[key]
                print(f"执行: {command} - {params}")
                controller.send_command(command, params)
                
                if command == 'emergency_stop':
                    break
            else:
                print("无效命令，请重新输入")
    
    except KeyboardInterrupt:
        print("\n⏹️ 交互式演示结束")

def main():
    """主函数"""
    print("🚀 视觉控制系统演示")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == 'interactive':
        interactive_demo()
        return
    
    # 自动演示
    demo = VisionControlDemo()
    
    if not CONTROLLER_AVAILABLE:
        print("⚠️ 控制器不可用，将只演示检测逻辑")
    
    # 询问演示时长
    try:
        duration = int(input("请输入演示时长（秒，默认30）: ") or "30")
    except ValueError:
        duration = 30
    
    demo.run_demo(duration)

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ 演示被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 演示过程中发生错误: {e}")
        sys.exit(1)
