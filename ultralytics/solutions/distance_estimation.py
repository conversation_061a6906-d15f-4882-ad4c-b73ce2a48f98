# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

import math
import numpy as np
from collections import defaultdict
from typing import Dict, List, Tuple, Optional, Any


def create_camera_matrix(f_x=362.4946, f_y=362.7852, c_x=331.2797, c_y=156.1371):
    """创建相机内参矩阵 - 使用实际标定的摄像头参数"""
    return np.array([
        [f_x, 0, c_x],
        [0, f_y, c_y],
        [0, 0, 1]
    ])


def calculate_azimuth_planar(x_pixel, camera_params, debug=False):
    """
    基于平面几何的方位角计算 - 针对同平面多机器人场景优化
    
    核心思想：
    1. 所有目标在同一水平面，摄像头固定高度
    2. 图像Y坐标基本固定，主要变化是X坐标  
    3. 水平方位角直接由X轴偏移决定
    
    :param x_pixel: 目标在图像中的X坐标
    :param camera_params: 相机参数字典 {'fx': fx, 'cx': cx}
    :param debug: 是否输出调试信息
    :return: 方位角（度，相对于相机光轴）
    """
    
    fx = camera_params.get('fx', 362.4946)
    cx = camera_params.get('cx', 331.2797)
    
    # 🔧 核心算法：基于X轴偏移的方位角计算
    # 水平偏移角 = atan((像素偏移) / 焦距)
    pixel_offset = x_pixel - cx  # 相对于图像中心的X偏移
    angle_rad = math.atan(pixel_offset / fx)  # 转换为角度（弧度）
    angle_deg = math.degrees(angle_rad)  # 转换为度
    
    # 转换到相机坐标系定义：
    # 0° = 正前方，正值 = 右侧，负值 = 左侧
    # 标准化到[0, 360)范围
    if angle_deg < 0:
        azimuth = 360 + angle_deg  # 负角度转换为270-360度范围
    else:
        azimuth = angle_deg
    
    if debug:
        print(f"🔍 平面几何方位角计算:")
        print(f"   X像素坐标: {x_pixel:.1f}")
        print(f"   图像中心CX: {cx}")
        print(f"   像素偏移: {pixel_offset:.1f}")
        print(f"   焦距FX: {fx:.1f}")
        print(f"   计算角度: atan({pixel_offset:.1f}/{fx:.1f}) = {angle_deg:.2f}°")
        print(f"   标准化方位角: {azimuth:.2f}°")
        
        # 解释方向
        if -10 <= angle_deg <= 10:
            direction = "正前方"
        elif angle_deg > 10:
            direction = "右前方"
        else:
            direction = "左前方"
        print(f"   目标方向: {direction}")
    
    return azimuth


def calculate_distance_planar(detected_width, real_width, fx, debug=False):
    """
    基于检测框宽度的距离计算（简化版本）
    
    基本原理：distance = (real_width × fx) / pixel_width
    这个公式在小角度近似下是准确的
    
    :param detected_width: YOLO检测框的像素宽度
    :param real_width: 目标的真实宽度（米）
    :param fx: 相机焦距
    :param debug: 是否输出调试信息
    :return: 距离（米）
    """
    
    if detected_width <= 0:
        return float('inf')  # 避免除零错误
    
    distance = (real_width * fx) / detected_width
    
    if debug:
        print(f"🔍 平面几何距离计算:")
        print(f"   真实宽度: {real_width}m")
        print(f"   像素宽度: {detected_width:.1f}px")
        print(f"   焦距: {fx:.1f}px")
        print(f"   计算距离: ({real_width} × {fx:.1f}) / {detected_width:.1f} = {distance:.2f}m")
    
    return distance


def calculate_position_vector_planar(detected_width, x_box, y_box, camera_matrix_inv, real_width, debug=False):
    """
    基于平面几何的位置向量计算 - 专为同平面多机器人优化
    
    优化特点：
    1. 简化的方位角计算，直接基于X轴偏移
    2. 简化的距离计算，基于YOLO框宽度  
    3. 针对水平面场景的position_vector计算
    
    :param detected_width: 检测框的宽度（像素）
    :param x_box: 检测框中心的X坐标（像素）
    :param y_box: 检测框中心的Y坐标（像素）
    :param camera_matrix_inv: 相机矩阵的逆（为了兼容性保留）
    :param real_width: 目标的实际宽度（米）
    :param debug: 是否输出调试信息
    :return: (position_vector, distance, theta, azimuth, elevation)
    """
    
    # 提取相机参数
    fx = 1.0 / camera_matrix_inv[0][0]  # 从逆矩阵恢复fx
    cx = -camera_matrix_inv[0][2] * fx   # 从逆矩阵恢复cx
    
    camera_params = {'fx': fx, 'cx': cx}
    
    if debug:
        print(f"\n{'='*50}")
        print(f"🚗 平面几何位置计算 - 检测框({x_box:.1f}, {y_box:.1f})")
        print(f"{'='*50}")
    
    # 🔧 步骤1：计算方位角（核心优化）
    azimuth = calculate_azimuth_planar(x_box, camera_params, debug)
    
    # 🔧 步骤2：计算距离（简化算法）
    distance = calculate_distance_planar(detected_width, real_width, fx, debug)
    
    # 🔧 步骤3：计算position_vector（基于极坐标）
    # 在水平面上，根据距离和方位角计算相对位置
    azimuth_rad = math.radians(azimuth)
    
    # 相机坐标系：X右，Y下，Z前
    # 目标相对位置：
    pos_x = distance * math.sin(azimuth_rad)  # 水平右方距离
    pos_y = 0.0  # 垂直位置差异忽略（同平面假设）
    pos_z = distance * math.cos(azimuth_rad)  # 前方距离
    
    position_vector = np.array([pos_x, pos_y, pos_z])
    
    # theta和elevation为兼容性保留（2D场景下不重要）
    theta = 0.0  # 角度展开，2D场景下简化
    elevation = 0.0  # 俯仰角，同平面场景下为0
    
    if debug:
        print(f"🎯 最终结果:")
        print(f"   方位角: {azimuth:.2f}° ({'右前方' if 0 < azimuth < 90 else '左前方' if 270 < azimuth < 360 else '前方'})")
        print(f"   距离: {distance:.2f}m") 
        print(f"   相机坐标系位置: [{pos_x:.2f}, {pos_y:.2f}, {pos_z:.2f}]")
        print(f"   水平偏移: {pos_x:.2f}m ({'右' if pos_x > 0 else '左'})")
        print(f"   前方距离: {pos_z:.2f}m")
        print(f"{'='*50}\n")
    
    return position_vector, distance, theta, azimuth, elevation


class DistanceEstimator:
    """距离估算器类，用于实时计算目标距离和位置"""
    
    def __init__(self, real_width=0.31, camera_matrix=None, debug=False):
        """
        初始化距离估算器
        
        :param real_width: 目标真实宽度（米）
        :param camera_matrix: 相机内参矩阵
        :param debug: 是否启用调试模式
        """
        self.real_width = real_width
        self.debug = debug
        
        if camera_matrix is None:
            self.camera_matrix = create_camera_matrix()
        else:
            self.camera_matrix = camera_matrix
            
        self.camera_matrix_inv = np.linalg.inv(self.camera_matrix)
        
        # 存储跟踪目标的历史数据
        self.track_history: Dict[int, List[Dict]] = defaultdict(list)
        
    def update_camera_params(self, fx, fy, cx, cy):
        """更新相机参数"""
        self.camera_matrix = create_camera_matrix(fx, fy, cx, cy)
        self.camera_matrix_inv = np.linalg.inv(self.camera_matrix)
        
    def estimate_distance_from_detection(self, detection_box, track_id=None):
        """
        从YOLO检测结果估算距离
        
        :param detection_box: YOLO检测框 (x1, y1, x2, y2) 或 (x_center, y_center, width, height)
        :param track_id: 跟踪ID（可选）
        :return: (distance, azimuth, position_vector) 或 None
        """
        try:
            # 解析检测框格式
            if len(detection_box) == 4:
                # 假设是 (x_center, y_center, width, height) 格式
                x_center, y_center, width, height = detection_box
                detected_width = width
                x_box = x_center
                y_box = y_center
            else:
                return None
                
            # 计算位置向量和距离
            position_vector, distance, theta, azimuth, elevation = calculate_position_vector_planar(
                detected_width, x_box, y_box, self.camera_matrix_inv, self.real_width, self.debug
            )
            
            # 如果有跟踪ID，存储历史数据
            if track_id is not None:
                detection_data = {
                    'distance': distance,
                    'azimuth': azimuth,
                    'position_vector': position_vector,
                    'box': detection_box
                }
                self.track_history[track_id].append(detection_data)
                
                # 保持历史数据在合理范围内
                if len(self.track_history[track_id]) > 10:
                    self.track_history[track_id].pop(0)
            
            return distance, azimuth, position_vector
            
        except Exception as e:
            if self.debug:
                print(f"距离估算错误: {e}")
            return None
            
    def get_smoothed_distance(self, track_id, window_size=5):
        """
        获取平滑后的距离估算（基于历史数据）
        
        :param track_id: 跟踪ID
        :param window_size: 平滑窗口大小
        :return: (avg_distance, avg_azimuth) 或 None
        """
        if track_id not in self.track_history or len(self.track_history[track_id]) == 0:
            return None
            
        history = self.track_history[track_id]
        recent_data = history[-window_size:] if len(history) >= window_size else history
        
        distances = [data['distance'] for data in recent_data]
        azimuths = [data['azimuth'] for data in recent_data]
        
        avg_distance = np.mean(distances)
        avg_azimuth = np.mean(azimuths)
        
        return avg_distance, avg_azimuth
        
    def format_distance_info(self, distance, azimuth):
        """
        格式化距离信息为可读字符串
        
        :param distance: 距离（米）
        :param azimuth: 方位角（度）
        :return: 格式化的字符串
        """
        # 方向描述
        if -10 <= azimuth <= 10 or 350 <= azimuth <= 360:
            direction = "正前方"
        elif 10 < azimuth < 90:
            direction = "右前方"
        elif 90 <= azimuth <= 180:
            direction = "右侧"
        elif 180 < azimuth < 270:
            direction = "右后方"
        elif 270 <= azimuth < 350:
            direction = "左前方"
        else:
            direction = "未知方向"
            
        return f"距离: {distance:.2f}m | 方向: {direction} ({azimuth:.1f}°)"
