# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

import io
import os
import threading
import time
import math
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Tuple
from collections import defaultdict

import cv2
import numpy as np
import torch

from ultralytics import YOLO
from ultralytics.utils import LOGGER
from ultralytics.utils.checks import check_requirements

# 距离估算功能 - 直接集成
DISTANCE_ESTIMATION_AVAILABLE = True

def create_camera_matrix(f_x=362.4946, f_y=362.7852, c_x=331.2797, c_y=156.1371):
    """创建相机内参矩阵 - 使用实际标定的摄像头参数"""
    return np.array([
        [f_x, 0, c_x],
        [0, f_y, c_y],
        [0, 0, 1]
    ])


def calculate_azimuth_planar(x_pixel, camera_params, debug=False):
    """
    基于平面几何的方位角计算 - 针对同平面多机器人场景优化

    核心思想：
    1. 所有目标在同一水平面，摄像头固定高度
    2. 图像Y坐标基本固定，主要变化是X坐标
    3. 水平方位角直接由X轴偏移决定

    :param x_pixel: 目标在图像中的X坐标
    :param camera_params: 相机参数字典 {'fx': fx, 'cx': cx}
    :param debug: 是否输出调试信息
    :return: 方位角（度，相对于相机光轴）
    """

    fx = camera_params.get('fx', 362.4946)
    cx = camera_params.get('cx', 331.2797)

    # 🔧 核心算法：基于X轴偏移的方位角计算
    # 水平偏移角 = atan((像素偏移) / 焦距)
    pixel_offset = x_pixel - cx  # 相对于图像中心的X偏移
    angle_rad = math.atan(pixel_offset / fx)  # 转换为角度（弧度）
    angle_deg = math.degrees(angle_rad)  # 转换为度

    # 转换到相机坐标系定义然后：
    # 0° = 正前方，正值 = 右侧，负值 = 左侧
    # 标准化到[0, 360)范围
    if angle_deg < 0:
        azimuth = 360 + angle_deg  # 负角度转换为270-360度范围
    else:
        azimuth = angle_deg

    if debug:
        print(f"🔍 平面几何方位角计算:")
        print(f"   X像素坐标: {x_pixel:.1f}")
        print(f"   图像中心CX: {cx}")
        print(f"   像素偏移: {pixel_offset:.1f}")
        print(f"   焦距FX: {fx:.1f}")
        print(f"   计算角度: atan({pixel_offset:.1f}/{fx:.1f}) = {angle_deg:.2f}°")
        print(f"   标准化方位角: {azimuth:.2f}°")

        # 解释方向
        if -10 <= angle_deg <= 10:
            direction = "正前方"
        elif angle_deg > 10:
            direction = "右前方"
        else:
            direction = "左前方"
        print(f"   目标方向: {direction}")

    return azimuth


def calculate_distance_planar(detected_width, real_width, fx, debug=False):
    """
    基于检测框宽度的距离计算（简化版本）

    基本原理：distance = (real_width × fx) / pixel_width
    这个公式在小角度近似下是准确的

    :param detected_width: YOLO检测框的像素宽度
    :param real_width: 目标的真实宽度（米）
    :param fx: 相机焦距
    :param debug: 是否输出调试信息
    :return: 距离（米）
    """

    if detected_width <= 0:
        return float('inf')  # 避免除零错误

    distance = (real_width * fx) / detected_width

    if debug:
        print(f"🔍 平面几何距离计算:")
        print(f"   真实宽度: {real_width}m")
        print(f"   像素宽度: {detected_width:.1f}px")
        print(f"   焦距: {fx:.1f}px")
        print(f"   计算距离: ({real_width} × {fx:.1f}) / {detected_width:.1f} = {distance:.2f}m")

    return distance


class DistanceEstimator:
    """距离估算器类，用于实时计算目标距离和位置"""

    def __init__(self, real_width=0.31, camera_matrix=None, debug=False):
        """
        初始化距离估算器

        :param real_width: 目标真实宽度（米）
        :param camera_matrix: 相机内参矩阵
        :param debug: 是否启用调试模式
        """
        self.real_width = real_width
        self.debug = debug

        if camera_matrix is None:
            self.camera_matrix = create_camera_matrix()
        else:
            self.camera_matrix = camera_matrix

        self.camera_matrix_inv = np.linalg.inv(self.camera_matrix)

        # 存储跟踪目标的历史数据
        self.track_history = defaultdict(list)

    def update_camera_params(self, fx, fy, cx, cy):
        """更新相机参数"""
        self.camera_matrix = create_camera_matrix(fx, fy, cx, cy)
        self.camera_matrix_inv = np.linalg.inv(self.camera_matrix)

    def estimate_distance_from_detection(self, detection_box, track_id=None):
        """
        从YOLO检测结果估算距离

        :param detection_box: YOLO检测框 (x_center, y_center, width, height)
        :param track_id: 跟踪ID（可选）
        :return: (distance, azimuth, position_vector) 或 None
        """
        try:
            # 解析检测框格式
            if len(detection_box) == 4:
                # 假设是 (x_center, y_center, width, height) 格式
                x_center, y_center, width, height = detection_box
                detected_width = width
                x_box = x_center
                y_box = y_center
            else:
                return None

            # 提取相机参数
            fx = 1.0 / self.camera_matrix_inv[0][0]  # 从逆矩阵恢复fx
            cx = -self.camera_matrix_inv[0][2] * fx   # 从逆矩阵恢复cx

            camera_params = {'fx': fx, 'cx': cx}

            # 计算方位角
            azimuth = calculate_azimuth_planar(x_box, camera_params, self.debug)

            # 计算距离
            distance = calculate_distance_planar(detected_width, self.real_width, fx, self.debug)

            # 计算位置向量（基于极坐标）
            azimuth_rad = math.radians(azimuth)
            pos_x = distance * math.sin(azimuth_rad)  # 水平右方距离
            pos_y = 0.0  # 垂直位置差异忽略（同平面假设）
            pos_z = distance * math.cos(azimuth_rad)  # 前方距离
            position_vector = np.array([pos_x, pos_y, pos_z])

            # 如果有跟踪ID，存储历史数据
            if track_id is not None:
                detection_data = {
                    'distance': distance,
                    'azimuth': azimuth,
                    'position_vector': position_vector,
                    'box': detection_box
                }
                self.track_history[track_id].append(detection_data)

                # 保持历史数据在合理范围内
                if len(self.track_history[track_id]) > 10:
                    self.track_history[track_id].pop(0)

            return distance, azimuth, position_vector

        except Exception as e:
            if self.debug:
                print(f"距离估算错误: {e}")
            return None

    def format_distance_info(self, distance, azimuth):
        """
        格式化距离信息为可读字符串 - 只显示距离

        :param distance: 距离（米）
        :param azimuth: 方位角（度）
        :return: 格式化的字符串
        """
        # 只返回距离信息，方位角在终端输出
        return f"{distance:.2f}m"

    def get_direction_description(self, azimuth):
        """
        获取方向描述 - 用于终端输出

        :param azimuth: 方位角（度）
        :return: 方向描述
        """
        if -10 <= azimuth <= 10 or 350 <= azimuth <= 360:
            return "Front"
        elif 10 < azimuth < 90:
            return "Front-Right"
        elif 90 <= azimuth <= 180:
            return "Right"
        elif 180 < azimuth < 270:
            return "Rear-Right"
        elif 270 <= azimuth < 350:
            return "Front-Left"
        else:
            return "Unknown"

torch.classes.__path__ = []  # Torch module __path__._path issue: https://github.com/datalab-to/marker/issues/442


class Inference:
    """
    A class to perform object detection, image classification, image segmentation and pose estimation inference.

    This class provides functionalities for loading models, configuring settings, uploading video files, and performing
    real-time inference using Streamlit and Ultralytics YOLO models. Enhanced with multi-camera support and custom resolution.

    Attributes:
        st (module): Streamlit module for UI creation.
        temp_dict (dict): Temporary dictionary to store the model path and other configuration.
        model_path (str): Path to the loaded model.
        model (YOLO): The YOLO model instance.
        source (str): Selected video source (webcam, multi-webcam, or video file).
        enable_trk (bool): Enable tracking option.
        conf (float): Confidence threshold for detection.
        iou (float): IoU threshold for non-maximum suppression.
        org_frame (Any): Container for the original frame to be displayed.
        ann_frame (Any): Container for the annotated frame to be displayed.
        vid_file_name (str | int): Name of the uploaded video file or webcam index.
        selected_ind (List[int]): List of selected class indices for detection.
        num_cameras (int): Number of cameras to use for multi-camera mode.
        camera_indices (List[int]): List of camera indices to use.
        frame_width (int): Custom frame width for cameras.
        frame_height (int): Custom frame height for cameras.
        camera_caps (List[cv2.VideoCapture]): List of camera capture objects.
        camera_threads (List[threading.Thread]): List of camera threads.
        camera_frames (Dict[int, np.ndarray]): Dictionary to store latest frames from each camera.
        camera_running (bool): Flag to control camera threads.

    Methods:
        web_ui: Set up the Streamlit web interface with custom HTML elements.
        sidebar: Configure the Streamlit sidebar for model and inference settings.
        source_upload: Handle video file uploads through the Streamlit interface.
        configure: Configure the model and load selected classes for inference.
        setup_cameras: Initialize multiple cameras with custom resolution.
        camera_thread: Thread function to capture frames from a single camera.
        multi_camera_inference: Perform inference on multiple cameras simultaneously.
        inference: Perform real-time object detection inference.

    Examples:
        Create an Inference instance with a custom model and 4 cameras
        >>> inf = Inference(model="path/to/model.pt")
        >>> inf.inference()

        Create an Inference instance with default settings
        >>> inf = Inference()
        >>> inf.inference()
    """

    def __init__(self, **kwargs: Any) -> None:
        """
        Initialize the Inference class, checking Streamlit requirements and setting up the model path.

        Args:
            **kwargs (Any): Additional keyword arguments for model configuration.
        """
        check_requirements("streamlit>=1.29.0")  # scope imports for faster ultralytics package load speeds
        import streamlit as st

        self.st = st  # Reference to the Streamlit module
        self.source = None  # Video source selection (webcam, multi-webcam, or video file)
        self.img_file_names = []  # List of image file names
        self.enable_trk = False  # Flag to toggle object tracking
        self.conf = 0.25  # Confidence threshold for detection
        self.iou = 0.45  # Intersection-over-Union (IoU) threshold for non-maximum suppression
        self.org_frame = None  # Container for the original frame display
        self.ann_frame = None  # Container for the annotated frame display
        self.vid_file_name = None  # Video file name or webcam index
        self.selected_ind: List[int] = []  # List of selected class indices for detection
        self.model = None  # YOLO model instance

        # Multi-camera support attributes - focused on Multi-camera with optimized defaults
        self.num_cameras = 4  # Default number of cameras (optimized for multi-camera setup)
        self.camera_indices = [0, 2, 4, 6]  # Default camera indices for 4 cameras (correct USB camera numbering)
        self.frame_width = 640  # Default frame width (optimized for performance)
        self.frame_height = 360  # Default frame height (optimized for performance)
        self.camera_caps: List[cv2.VideoCapture] = []  # Camera capture objects
        self.camera_threads: List[threading.Thread] = []  # Camera threads
        self.camera_frames: Dict[int, np.ndarray] = {}  # Latest frames from each camera
        self.camera_running = False  # Flag to control camera threads
        self.camera_containers: Dict[int, Tuple[Any, Any]] = {}  # UI containers for each camera

        # FPS monitoring attributes
        self.camera_fps: Dict[int, float] = {}  # FPS for each camera
        self.camera_frame_times: Dict[int, List[float]] = {}  # Frame timestamps for FPS calculation
        self.fps_containers: Dict[int, Any] = {}  # UI containers for FPS display
        self.overall_fps_container = None  # Container for overall FPS display

        # Detection result saving attributes
        self.save_detections = False  # Flag to enable/disable saving detections
        self.save_images = True  # Flag to save images with detections
        self.save_labels = True  # Flag to save label files
        self.save_directory = "./detection_results"  # Default save directory
        self.detection_count: Dict[int, int] = {}  # Count of detections per camera

        # LED闪烁检测统计
        self.class_detection_stats: Dict[int, Dict[str, int]] = {}  # 每个摄像头的类别检测统计
        self.last_detection_time: Dict[int, float] = {}  # 上次检测时间

        # Distance estimation attributes
        self.enable_distance = False  # Flag to enable distance estimation
        self.real_width = 0.31  # Default real width of target object (meters)
        self.distance_debug = False  # Debug mode for distance calculation
        self.distance_estimator = None  # Will be initialized when distance estimation is enabled

        self.temp_dict = {"model": None, **kwargs}
        self.model_path = None  # Model file path
        if self.temp_dict["model"] is not None:
            self.model_path = self.temp_dict["model"]

        LOGGER.info(f"Ultralytics Solutions: ✅ {self.temp_dict}")

    def web_ui(self) -> None:
        """Set up the Streamlit web interface with custom HTML elements."""
        menu_style_cfg = """<style>MainMenu {visibility: hidden;}</style>"""  # Hide main menu style

        # Main title of streamlit application - focused on Multi-camera
        main_title_cfg = """<div><h1 style="color:#111F68; text-align:center; font-size:40px; margin-top:-50px;
        font-family: 'Archivo', sans-serif; margin-bottom:20px;">Multi-Camera Car Detection System</h1></div>"""

        # Subtitle of streamlit application - focused on multi-camera functionality
        sub_title_cfg = """<div><h5 style="color:#042AFF; text-align:center; font-family: 'Archivo', sans-serif;
        margin-top:-15px; margin-bottom:50px;">Real-time car detection across multiple cameras with model inference size 736x1280 🚗📹�</h5></div>"""

        # Set html page configuration and append custom HTML
        self.st.set_page_config(page_title="Ultralytics Streamlit App", layout="wide")
        self.st.markdown(menu_style_cfg, unsafe_allow_html=True)
        self.st.markdown(main_title_cfg, unsafe_allow_html=True)
        self.st.markdown(sub_title_cfg, unsafe_allow_html=True)

    def sidebar(self) -> None:
        """Configure the Streamlit sidebar for model and inference settings."""
        with self.st.sidebar:  # Add Ultralytics LOGO
            logo = "https://raw.githubusercontent.com/ultralytics/assets/main/logo/Ultralytics_Logotype_Original.svg"
            self.st.image(logo, width=250)

        self.st.sidebar.title("User Configuration")  # Add elements to vertical setting menu
        # Focus on Multi-camera by default
        self.source = self.st.sidebar.selectbox(
            "Source",
            ("multi-webcam", "webcam", "video", "image"),
            index=0  # Default to multi-webcam
        )  # Add source selection dropdown

        # Camera configuration
        if self.source in ["webcam", "multi-webcam"]:
            self.st.sidebar.subheader("Camera Settings")

            if self.source == "multi-webcam":
                # Simplified camera configuration with optimized defaults
                self.st.sidebar.info("💡 **Multi-Camera Setup**: Optimized for 4 cameras with model inference size 736x1280 for best performance.")

                # Fixed to 4 cameras by default, but allow adjustment
                self.num_cameras = self.st.sidebar.slider("Number of Cameras", 1, 8, 4)

                # Simplified camera indices - default to correct USB camera numbering
                if self.num_cameras <= 4:
                    # Use the correct camera indices [0,2,4,6] for USB cameras
                    available_cameras = [0, 2, 4, 6]
                    self.camera_indices = available_cameras[:self.num_cameras]
                else:
                    # For more than 4 cameras, use the original logic
                    preset_configs = {
                        "Sequential (0,1,2,3,4,5,6,7)": ",".join(map(str, range(self.num_cameras))),
                        "Reliable (0,2,4,6)": "0,2,4,6",
                        "Custom": "custom"
                    }

                    selected_preset = self.st.sidebar.selectbox("Camera Configuration", list(preset_configs.keys()))

                    if selected_preset == "Custom":
                        camera_indices_str = self.st.sidebar.text_input(
                            "Camera Indices (comma-separated)",
                            ",".join(map(str, self.camera_indices[:self.num_cameras]))
                        )
                    else:
                        camera_indices_str = preset_configs[selected_preset]

                    try:
                        self.camera_indices = [int(x.strip()) for x in camera_indices_str.split(",")][:self.num_cameras]
                        self.num_cameras = len(self.camera_indices)  # Update num_cameras to match indices
                    except ValueError:
                        self.st.sidebar.error("Invalid camera indices format. Using default values.")
                        self.camera_indices = [0, 2, 4, 6]
                        self.num_cameras = 4

            # Simplified resolution settings - default to 640x360 for optimal performance
            if self.source == "multi-webcam":
                # Fixed resolution for multi-camera to ensure optimal performance
                self.frame_width = 640
                self.frame_height = 360
                self.st.sidebar.info(f"🎯 **Camera Resolution**: {self.frame_width}x{self.frame_height} | **Model Inference**: 736x1280 (optimized for performance)")
            else:
                # Allow resolution selection for single camera/video
                resolution_presets = {
                    "640x360": (640, 360),
                    "640x480": (640, 480),
                    "1280x720": (1280, 720),
                    "1920x1080": (1920, 1080),
                    "Custom": None
                }

                # Find current resolution in presets
                current_resolution = f"{self.frame_width}x{self.frame_height}"
                if current_resolution not in resolution_presets:
                    current_resolution = "Custom"

                selected_resolution = self.st.sidebar.selectbox(
                    "Resolution",
                    list(resolution_presets.keys()),
                    index=0  # Default to 640x360
                )

                if selected_resolution == "Custom":
                    new_width = self.st.sidebar.number_input("Width", min_value=320, max_value=3840, value=self.frame_width, key="custom_width")
                    new_height = self.st.sidebar.number_input("Height", min_value=240, max_value=2160, value=self.frame_height, key="custom_height")
                    self.frame_width = int(new_width)
                    self.frame_height = int(new_height)
                else:
                    self.frame_width, self.frame_height = resolution_presets[selected_resolution]

                # Display current resolution
                self.st.sidebar.info(f"Current resolution: {self.frame_width}x{self.frame_height}")

        if self.source in ["webcam", "multi-webcam", "video"]:
            self.enable_trk = self.st.sidebar.radio("Enable Tracking", ("Yes", "No")) == "Yes"  # Enable object tracking
        self.conf = float(
            self.st.sidebar.slider("Confidence Threshold", 0.0, 1.0, self.conf, 0.01)
        )  # Slider for confidence
        self.iou = float(self.st.sidebar.slider("IoU Threshold", 0.0, 1.0, self.iou, 0.01))  # Slider for NMS threshold

        # Distance estimation configuration
        self.st.sidebar.subheader("Distance Estimation")
        self.enable_distance = self.st.sidebar.checkbox("Enable Distance Estimation", value=self.enable_distance, key="enable_distance")

        if self.enable_distance:
            self.real_width = self.st.sidebar.number_input(
                "Target Real Width (meters)",
                min_value=0.1,
                max_value=5.0,
                value=self.real_width,
                step=0.01,
                help="Real width of the target object in meters (default: 0.31m for cars)",
                key="real_width"
            )

            # Camera parameters configuration (使用实际标定的摄像头参数)
            with self.st.sidebar.expander("Camera Parameters (Advanced)"):
                fx = self.st.sidebar.number_input("Focal Length X (fx)", value=362.4946, step=0.01, key="camera_fx")
                fy = self.st.sidebar.number_input("Focal Length Y (fy)", value=362.7852, step=0.01, key="camera_fy")
                cx = self.st.sidebar.number_input("Principal Point X (cx)", value=331.2797, step=0.1, key="camera_cx")
                cy = self.st.sidebar.number_input("Principal Point Y (cy)", value=156.1371, step=0.1, key="camera_cy")

            # 如果用户没有在高级设置中修改，使用默认参数
            if 'fx' not in locals():
                fx, fy, cx, cy = 362.4946, 362.7852, 331.2797, 156.1371

            # 总是初始化距离估算器（无论是否展开高级设置）
            camera_matrix = create_camera_matrix(fx, fy, cx, cy)
            self.distance_estimator = DistanceEstimator(
                real_width=self.real_width,
                camera_matrix=camera_matrix,
                debug=self.distance_debug
            )

            self.distance_debug = self.st.sidebar.checkbox("Debug Distance Calculation", value=self.distance_debug, key="distance_debug")

            # Update debug mode if estimator exists
            if self.distance_estimator:
                self.distance_estimator.debug = self.distance_debug





        # Detection saving configuration
        self.st.sidebar.subheader("Detection Saving")
        self.save_detections = self.st.sidebar.checkbox("Save Detection Results", value=self.save_detections, key="save_detections")

        if self.save_detections:
            self.save_directory = self.st.sidebar.text_input("Save Directory", value=self.save_directory)
            self.save_images = self.st.sidebar.checkbox("Save Images", value=self.save_images, key="save_images")
            self.save_labels = self.st.sidebar.checkbox("Save Labels (YOLO format)", value=self.save_labels, key="save_labels")

            # Create save directory if it doesn't exist
            if self.save_directory:
                Path(self.save_directory).mkdir(parents=True, exist_ok=True)
                self.st.sidebar.success(f"Save directory: {self.save_directory}")

        # Create UI layout based on source type
        if self.source == "multi-webcam":
            # Create overall FPS display
            self.overall_fps_container = self.st.empty()

            # Create grid layout for multiple cameras
            cols_per_row = 2 if self.num_cameras <= 4 else 3
            rows = (self.num_cameras + cols_per_row - 1) // cols_per_row

            for row in range(rows):
                cols = self.st.columns(cols_per_row)
                for col_idx in range(cols_per_row):
                    cam_idx = row * cols_per_row + col_idx
                    if cam_idx < self.num_cameras:
                        with cols[col_idx]:
                            self.st.markdown(f"**Camera {self.camera_indices[cam_idx]}**")
                            fps_container = self.st.empty()
                            org_container = self.st.empty()
                            ann_container = self.st.empty()
                            self.camera_containers[cam_idx] = (org_container, ann_container)
                            self.fps_containers[cam_idx] = fps_container
        elif self.source != "image":  # Single camera or video
            col1, col2 = self.st.columns(2)  # Create two columns for displaying frames
            self.org_frame = col1.empty()  # Container for original frame
            self.ann_frame = col2.empty()  # Container for annotated frame

    def source_upload(self) -> None:
        """Handle video file uploads through the Streamlit interface."""
        from ultralytics.data.utils import IMG_FORMATS, VID_FORMATS  # scope import

        self.vid_file_name = ""
        if self.source == "video":
            vid_file = self.st.sidebar.file_uploader("Upload Video File", type=VID_FORMATS)
            if vid_file is not None:
                g = io.BytesIO(vid_file.read())  # BytesIO Object
                with open("ultralytics.mp4", "wb") as out:  # Open temporary file as bytes
                    out.write(g.read())  # Read bytes into file
                self.vid_file_name = "ultralytics.mp4"
        elif self.source == "webcam":
            self.vid_file_name = 0  # Use webcam index 0
        elif self.source == "multi-webcam":
            # Multi-webcam mode doesn't use vid_file_name
            pass
        elif self.source == "image":
            import tempfile  # scope import

            imgfiles = self.st.sidebar.file_uploader("Upload Image Files", type=IMG_FORMATS, accept_multiple_files=True)
            if imgfiles:
                for imgfile in imgfiles:  # Save each uploaded image to a temporary file
                    with tempfile.NamedTemporaryFile(delete=False, suffix=f".{imgfile.name.split('.')[-1]}") as tf:
                        tf.write(imgfile.read())
                        self.img_file_names.append({"path": tf.name, "name": imgfile.name})

    def configure(self) -> None:
        """Configure the model and load selected classes for inference."""
        # Simplified model selection - focus on custom car detection model
        available_models = ["Custom Car Detection Model (best.engine)"]  # Default to custom model

        # Add custom model path as default
        custom_model_path = "/home/<USER>/BeeSwarm/ultralytics-yolo11-20241228/ultralytics-yolo11-main/runs/detect/0821Car/weights/best.engine"

        if self.model_path:  # Insert user provided custom model in available_models
            available_models.insert(0, self.model_path)

        selected_model = self.st.sidebar.selectbox("Model", available_models, index=0)  # Default to first model

        with self.st.spinner("Model is loading..."):
            # Handle model path selection - simplified
            if selected_model == "Custom Car Detection Model (best.engine)":
                model_path = custom_model_path
            elif (
                selected_model.endswith((".pt", ".onnx", ".torchscript", ".mlpackage", ".engine"))
                or "openvino_model" in selected_model
            ):
                model_path = selected_model
            else:
                model_path = custom_model_path  # Default to custom model

            self.model = YOLO(model_path)  # Load the YOLO model
            class_names = list(self.model.names.values())  # Convert dictionary to list of class names
        self.st.success("Model loaded successfully!")

        # Multiselect box with class names and get indices of selected classes - default to all classes
        selected_classes = self.st.sidebar.multiselect("Classes", class_names, default=class_names)
        self.selected_ind = [class_names.index(option) for option in selected_classes]

        if not isinstance(self.selected_ind, list):  # Ensure selected_options is a list
            self.selected_ind = list(self.selected_ind)

    def setup_cameras(self) -> bool:
        """
        Initialize multiple cameras with custom resolution.

        Returns:
            bool: True if at least one camera was successfully initialized, False otherwise.
        """
        self.camera_caps = []
        self.camera_frames = {}
        self.camera_fps = {}
        self.camera_frame_times = {}
        successful_cameras = 0

        # Initialize cameras one by one with delays to avoid resource conflicts
        for i, cam_idx in enumerate(self.camera_indices):
            try:
                # Try default backend first (more reliable for simultaneous access)
                cap = cv2.VideoCapture(cam_idx)

                if cap.isOpened():
                    # Set buffer size to reduce latency
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

                    # Force MJPG format to reduce bandwidth
                    cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))

                    # Set custom resolution
                    cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
                    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)

                    # Give camera time to adjust
                    time.sleep(0.1)

                    # Test frame capture multiple times to ensure stability
                    test_success = False
                    for attempt in range(3):
                        ret, test_frame = cap.read()
                        if ret and test_frame is not None and test_frame.size > 0:
                            test_success = True
                            break
                        time.sleep(0.05)

                    if test_success:
                        # Verify the resolution was set
                        actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

                        self.camera_caps.append(cap)
                        self.camera_frames[i] = np.zeros((actual_height, actual_width, 3), dtype=np.uint8)
                        self.camera_fps[i] = 0.0
                        self.camera_frame_times[i] = []
                        successful_cameras += 1

                        LOGGER.info(f"Camera {cam_idx} initialized successfully with resolution {actual_width}x{actual_height}")
                    else:
                        self.camera_caps.append(None)
                        cap.release()
                        LOGGER.warning(f"Camera {cam_idx} opened but cannot read frames reliably")
                else:
                    self.camera_caps.append(None)
                    LOGGER.warning(f"Failed to open camera {cam_idx}")

                # Small delay between camera initializations
                time.sleep(0.2)

            except Exception as e:
                self.camera_caps.append(None)
                LOGGER.error(f"Error initializing camera {cam_idx}: {e}")

        return successful_cameras > 0

    def camera_thread(self, camera_index: int, cap: cv2.VideoCapture) -> None:
        """
        Thread function to capture frames from a single camera.

        Args:
            camera_index (int): Index of the camera in the camera list.
            cap (cv2.VideoCapture): OpenCV VideoCapture object for the camera.
        """
        consecutive_failures = 0
        max_failures = 10

        while self.camera_running and cap is not None and cap.isOpened():
            try:
                frame_start_time = time.time()
                ret, frame = cap.read()
                if ret and frame is not None and frame.size > 0:
                    self.camera_frames[camera_index] = frame.copy()

                    # Update FPS calculation
                    current_time = time.time()
                    if camera_index in self.camera_frame_times:
                        self.camera_frame_times[camera_index].append(current_time)
                        # Keep only last 30 frame times for rolling average
                        if len(self.camera_frame_times[camera_index]) > 30:
                            self.camera_frame_times[camera_index].pop(0)

                        # Calculate FPS
                        if len(self.camera_frame_times[camera_index]) > 1:
                            time_diff = self.camera_frame_times[camera_index][-1] - self.camera_frame_times[camera_index][0]
                            if time_diff > 0:
                                self.camera_fps[camera_index] = (len(self.camera_frame_times[camera_index]) - 1) / time_diff

                    consecutive_failures = 0  # Reset failure counter on success
                else:
                    consecutive_failures += 1
                    if consecutive_failures >= max_failures:
                        LOGGER.error(f"Camera {camera_index} failed {max_failures} consecutive times, stopping thread")
                        break
                    elif consecutive_failures % 5 == 0:  # Log every 5 failures
                        LOGGER.warning(f"Camera {camera_index} failed to read frame ({consecutive_failures} consecutive failures)")

                time.sleep(0.01)  # ~100 FPS max, 提高频率以捕获LED快速变化
            except Exception as e:
                consecutive_failures += 1
                LOGGER.error(f"Error in camera thread {camera_index}: {e}")
                if consecutive_failures >= max_failures:
                    break
                time.sleep(0.1)  # Longer delay on exception

    def multi_camera_inference(self) -> None:
        """Perform inference on multiple cameras simultaneously."""
        self.st.info(f"🎥 Attempting to initialize {len(self.camera_indices)} cameras: {self.camera_indices}")

        if not self.setup_cameras():
            self.st.error("❌ Failed to initialize any cameras. Please check your camera connections.")
            self.st.info("💡 Try using the 'Reliable (0,2)' preset or test cameras individually.")
            return

        # Count successful cameras
        working_cameras = sum(1 for cap in self.camera_caps if cap is not None)
        total_cameras = len(self.camera_indices)

        if working_cameras < total_cameras:
            self.st.warning(f"⚠️ Only {working_cameras}/{total_cameras} cameras initialized successfully.")
            self.st.info("💡 This is normal due to USB bandwidth limitations. The working cameras will proceed.")
        else:
            self.st.success(f"✅ All {working_cameras} cameras initialized successfully!")

        # Start camera threads
        self.camera_running = True
        self.camera_threads = []

        for i, cap in enumerate(self.camera_caps):
            if cap is not None:
                thread = threading.Thread(target=self.camera_thread, args=(i, cap))
                thread.daemon = True
                thread.start()
                self.camera_threads.append(thread)

        stop_button = self.st.sidebar.button("Stop")

        try:
            inference_start_time = time.time()
            total_frames_processed = 0

            while self.camera_running:
                if stop_button:
                    break

                loop_start_time = time.time()
                frames_processed_this_loop = 0

                # Process frames from all cameras
                for cam_idx in range(len(self.camera_caps)):
                    if cam_idx in self.camera_frames and cam_idx in self.camera_containers:
                        frame = self.camera_frames[cam_idx].copy()

                        if frame is not None and frame.size > 0:
                            # Perform inference with fixed input size for consistency
                            if self.enable_trk:
                                results = self.model.track(
                                    frame, conf=self.conf, iou=self.iou, classes=self.selected_ind,
                                    persist=True, imgsz=(736, 1280)
                                )
                            else:
                                results = self.model(frame, conf=self.conf, iou=self.iou, classes=self.selected_ind, imgsz=(736, 1280))

                            annotated_frame = results[0].plot()

                            # Add distance estimation if enabled
                            if self.enable_distance and self.distance_estimator and results[0].boxes is not None:
                                annotated_frame = self.add_distance_annotations(annotated_frame, results[0])

                            # Save detection results if enabled
                            self.save_detection_results(results, frame, self.camera_indices[cam_idx])

                            # Update UI containers
                            org_container, ann_container = self.camera_containers[cam_idx]
                            org_container.image(frame, channels="BGR", caption=f"Camera {self.camera_indices[cam_idx]} - Original")
                            ann_container.image(annotated_frame, channels="BGR", caption=f"Camera {self.camera_indices[cam_idx]} - Predicted")

                            # Update FPS display for this camera
                            if cam_idx in self.fps_containers and cam_idx in self.camera_fps:
                                fps_value = self.camera_fps[cam_idx]
                                self.fps_containers[cam_idx].metric(
                                    f"Camera {self.camera_indices[cam_idx]} FPS",
                                    f"{fps_value:.1f}",
                                    delta=None
                                )

                            frames_processed_this_loop += 1
                            total_frames_processed += 1

                # Update overall FPS display
                if self.overall_fps_container:
                    elapsed_time = time.time() - inference_start_time
                    if elapsed_time > 0:
                        overall_fps = total_frames_processed / elapsed_time
                        avg_camera_fps = sum(self.camera_fps.values()) / len(self.camera_fps) if self.camera_fps else 0

                        self.overall_fps_container.markdown(f"""
                        ### 📊 Performance Metrics
                        - **Overall Processing FPS**: {overall_fps:.1f}
                        - **Average Camera FPS**: {avg_camera_fps:.1f}
                        - **Active Cameras**: {len([cap for cap in self.camera_caps if cap is not None])}
                        - **Total Frames Processed**: {total_frames_processed}
                        """)

                time.sleep(0.01)  # ~100 FPS - 提高频率以捕获LED快速变化

        finally:
            # Cleanup
            self.camera_running = False

            # Wait for threads to finish
            for thread in self.camera_threads:
                if thread.is_alive():
                    thread.join(timeout=1.0)

            # Release cameras
            for cap in self.camera_caps:
                if cap is not None:
                    cap.release()

            cv2.destroyAllWindows()

    def save_detection_results(self, results, frame: np.ndarray, camera_id: int) -> None:
        """
        Save detection results to specified directory.

        Args:
            results: YOLO detection results
            frame (np.ndarray): Original frame
            camera_id (int): Camera identifier
        """
        if not self.save_detections or not results[0].boxes:
            return  # No detections to save or saving disabled

        # 添加调试信息：记录检测到的类别和时间戳
        detected_classes = []
        if results[0].boxes is not None:
            for box in results[0].boxes:
                class_id = int(box.cls.item())
                class_name = self.model.names[class_id]
                detected_classes.append(class_name)

        current_time = time.time()

        # 统计类别检测频率
        if camera_id not in self.class_detection_stats:
            self.class_detection_stats[camera_id] = {}

        for class_name in detected_classes:
            if class_name not in self.class_detection_stats[camera_id]:
                self.class_detection_stats[camera_id][class_name] = 0
            self.class_detection_stats[camera_id][class_name] += 1

        # 计算检测间隔
        detection_interval = 0
        if camera_id in self.last_detection_time:
            detection_interval = current_time - self.last_detection_time[camera_id]
        self.last_detection_time[camera_id] = current_time

        print(f"🎯 Camera {camera_id} - Time: {current_time:.3f} - Interval: {detection_interval:.3f}s - Detected: {detected_classes}")

        # 每100次检测打印统计信息
        total_detections = sum(self.class_detection_stats[camera_id].values())
        if total_detections % 100 == 0:
            print(f"📊 Camera {camera_id} 检测统计 (总计{total_detections}次):")
            for class_name, count in self.class_detection_stats[camera_id].items():
                percentage = (count / total_detections) * 100
                print(f"   {class_name}: {count}次 ({percentage:.1f}%)")
            print(f"   平均检测间隔: {detection_interval:.3f}s")

        # Initialize detection count for this camera if not exists
        if camera_id not in self.detection_count:
            self.detection_count[camera_id] = 0

        # Create camera-specific directory
        camera_dir = Path(self.save_directory) / f"camera_{camera_id}"
        camera_dir.mkdir(parents=True, exist_ok=True)

        # Generate timestamp and filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # Include milliseconds
        self.detection_count[camera_id] += 1
        base_filename = f"detection_{timestamp}_{self.detection_count[camera_id]:04d}"

        # Save image if enabled
        if self.save_images:
            img_path = camera_dir / f"{base_filename}.jpg"
            annotated_frame = results[0].plot()
            cv2.imwrite(str(img_path), annotated_frame)

        # Save labels if enabled
        if self.save_labels:
            label_path = camera_dir / f"{base_filename}.txt"
            self._save_yolo_labels(results[0], label_path, frame.shape)

    def _save_yolo_labels(self, result, label_path: Path, img_shape: tuple) -> None:
        """
        Save detection results in YOLO format.

        Args:
            result: Single YOLO result object
            label_path (Path): Path to save the label file
            img_shape (tuple): Image shape (height, width, channels)
        """
        if not result.boxes:
            return

        height, width = img_shape[:2]

        with open(label_path, 'w') as f:
            for box in result.boxes:
                # Get class id and confidence
                class_id = int(box.cls.item())
                confidence = float(box.conf.item())

                # Get bounding box coordinates (xyxy format)
                x1, y1, x2, y2 = box.xyxy[0].tolist()

                # Convert to YOLO format (normalized xywh)
                x_center = (x1 + x2) / 2 / width
                y_center = (y1 + y2) / 2 / height
                bbox_width = (x2 - x1) / width
                bbox_height = (y2 - y1) / height

                # Write to file: class_id x_center y_center width height confidence
                f.write(f"{class_id} {x_center:.6f} {y_center:.6f} {bbox_width:.6f} {bbox_height:.6f} {confidence:.6f}\n")

    def add_distance_annotations(self, annotated_frame, result):
        """
        在标注图像上添加距离信息

        :param annotated_frame: 已标注的图像
        :param result: YOLO检测结果
        :return: 添加距离信息后的图像
        """
        if result.boxes is None or len(result.boxes) == 0:
            return annotated_frame

        # 调试信息
        if self.distance_debug:
            print(f"🔍 距离估算调试: 检测到 {len(result.boxes)} 个目标")
            print(f"🔍 距离估算器状态: {self.distance_estimator is not None}")
            print(f"🔍 距离估算启用: {self.enable_distance}")

        # 获取图像尺寸
        height, width = annotated_frame.shape[:2]

        for i, box in enumerate(result.boxes):
            # 获取检测框信息
            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()

            # 计算检测框中心和尺寸
            x_center = (x1 + x2) / 2
            y_center = (y1 + y2) / 2
            box_width = x2 - x1
            box_height = y2 - y1

            # 获取跟踪ID（如果有的话）
            track_id = None
            try:
                if hasattr(box, 'id') and box.id is not None:
                    if hasattr(box.id, 'cpu'):
                        track_id = int(box.id.cpu().numpy())
                    else:
                        track_id = int(box.id)
                elif hasattr(result, 'boxes') and hasattr(result.boxes, 'id') and result.boxes.id is not None:
                    # 有时候ID在boxes级别
                    if len(result.boxes.id) > i:
                        track_id = int(result.boxes.id[i].cpu().numpy() if hasattr(result.boxes.id[i], 'cpu') else result.boxes.id[i])
            except (AttributeError, IndexError, TypeError):
                # 如果获取ID失败，使用检测框索引作为临时ID
                track_id = i

            # 估算距离
            detection_box = (x_center, y_center, box_width, box_height)
            distance_result = self.distance_estimator.estimate_distance_from_detection(
                detection_box, track_id
            )

            if distance_result is not None:
                distance, azimuth, position_vector = distance_result

                # 格式化距离信息（只显示距离）
                distance_text = self.distance_estimator.format_distance_info(distance, azimuth)

                # 在终端输出详细的方位角信息
                direction = self.distance_estimator.get_direction_description(azimuth)
                print(f"📍 Target detected: Distance={distance:.2f}m, Direction={direction} ({azimuth:.1f}°), Position=[{position_vector[0]:.2f}, {position_vector[1]:.2f}, {position_vector[2]:.2f}]")

                # 计算文本位置，避免与YOLO标签重叠
                # YOLO标签通常在检测框左上角，我们将距离信息放在检测框下方中央
                distance_text_size = cv2.getTextSize(distance_text, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]

                # 距离信息放在检测框下方中央
                distance_x = int((x1 + x2) / 2) - distance_text_size[0] // 2
                distance_y = int(y2) + 25  # 检测框下方

                # 确保文本不会超出图像边界
                distance_x = max(5, min(distance_x, width - distance_text_size[0] - 5))
                distance_y = min(distance_y, height - 10)

                # 绘制距离信息背景
                cv2.rectangle(annotated_frame,
                            (distance_x - 5, distance_y - distance_text_size[1] - 5),
                            (distance_x + distance_text_size[0] + 5, distance_y + 5),
                            (0, 0, 0), -1)

                # 绘制距离文本（亮绿色，更醒目）
                cv2.putText(annotated_frame, distance_text,
                          (distance_x, distance_y),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

                # 如果有跟踪ID，显示在距离信息下方
                if track_id is not None:
                    id_text = f"ID:{track_id}"
                    id_text_size = cv2.getTextSize(id_text, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]

                    # ID信息放在距离信息下方
                    id_x = int((x1 + x2) / 2) - id_text_size[0] // 2
                    id_y = distance_y + 20

                    # 确保ID文本不会超出图像边界
                    id_x = max(5, min(id_x, width - id_text_size[0] - 5))
                    if id_y < height - 10:
                        # 绘制ID信息背景
                        cv2.rectangle(annotated_frame,
                                    (id_x - 3, id_y - id_text_size[1] - 3),
                                    (id_x + id_text_size[0] + 3, id_y + 3),
                                    (0, 0, 0), -1)

                        # 绘制ID文本（蓝色）
                        cv2.putText(annotated_frame, id_text,
                                  (id_x, id_y),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)

        return annotated_frame

    def image_inference(self) -> None:
        """Perform inference on uploaded images."""
        for idx, img_info in enumerate(self.img_file_names):
            img_path = img_info["path"]
            image = cv2.imread(img_path)  # Load and display the original image
            if image is not None:
                self.st.markdown(f"#### Processed: {img_info['name']}")
                col1, col2 = self.st.columns(2)
                with col1:
                    self.st.image(image, channels="BGR", caption="Original Image")
                results = self.model(image, conf=self.conf, iou=self.iou, classes=self.selected_ind, imgsz=(736, 1280))
                annotated_image = results[0].plot()
                with col2:
                    self.st.image(annotated_image, channels="BGR", caption="Predicted Image")
                try:  # Clean up temporary file
                    os.unlink(img_path)
                except FileNotFoundError:
                    pass  # File doesn't exist, ignore
            else:
                self.st.error("Could not load the uploaded image.")

    def inference(self) -> None:
        """Perform real-time object detection inference on video or webcam feed."""
        self.web_ui()  # Initialize the web interface
        self.sidebar()  # Create the sidebar
        self.source_upload()  # Upload the video source
        self.configure()  # Configure the app

        if self.st.sidebar.button("Start"):
            if self.source == "image":
                if self.img_file_names:
                    self.image_inference()
                else:
                    self.st.info("Please upload an image file to perform inference.")
                return
            elif self.source == "multi-webcam":
                self.multi_camera_inference()
                return

            # Single camera or video processing
            stop_button = self.st.sidebar.button("Stop")  # Button to stop the inference

            # Initialize camera/video with better backend support
            if self.source == "webcam":
                # Try V4L2 backend first for webcam
                cap = cv2.VideoCapture(self.vid_file_name, cv2.CAP_V4L2)
                if not cap.isOpened():
                    cap = cv2.VideoCapture(self.vid_file_name)  # Fallback to default

                if cap.isOpened():
                    # Set custom resolution
                    cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
                    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)
                    actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    self.st.info(f"Camera resolution set to: {actual_width}x{actual_height}")
            else:
                cap = cv2.VideoCapture(self.vid_file_name)  # Video file

            if not cap.isOpened():
                self.st.error("Could not open webcam or video source.")
                return

            while cap.isOpened():
                success, frame = cap.read()
                if not success:
                    self.st.warning("Failed to read frame from webcam. Please verify the webcam is connected properly.")
                    break

                # Process frame with model - fixed input size for consistency
                if self.enable_trk:
                    results = self.model.track(
                        frame, conf=self.conf, iou=self.iou, classes=self.selected_ind,
                        persist=True, imgsz=(736, 1280)
                    )
                else:
                    results = self.model(frame, conf=self.conf, iou=self.iou, classes=self.selected_ind, imgsz=(736, 1280))

                annotated_frame = results[0].plot()  # Add annotations on frame

                # Add distance estimation if enabled
                if self.enable_distance and self.distance_estimator and results[0].boxes is not None:
                    annotated_frame = self.add_distance_annotations(annotated_frame, results[0])
                elif self.enable_distance:
                    # 调试信息：为什么距离估算没有运行
                    if not self.distance_estimator:
                        print("⚠️ 距离估算器未初始化")
                    if results[0].boxes is None:
                        print("⚠️ 没有检测到目标")

                # Save detection results if enabled (use camera_id 0 for single camera)
                self.save_detection_results(results, frame, 0)

                if stop_button:
                    cap.release()  # Release the capture
                    self.st.stop()  # Stop streamlit app

                self.org_frame.image(frame, channels="BGR", caption="Original Frame")  # Display original frame
                self.ann_frame.image(annotated_frame, channels="BGR", caption="Predicted Frame")  # Display processed

            cap.release()  # Release the capture
        cv2.destroyAllWindows()  # Destroy all OpenCV windows


if __name__ == "__main__":
    import sys  # Import the sys module for accessing command-line arguments

    # Check if a model name is provided as a command-line argument
    args = len(sys.argv)
    model = sys.argv[1] if args > 1 else None  # Assign first argument as the model name if provided
    # Create an instance of the Inference class and run inference
    Inference(model=model).inference()
