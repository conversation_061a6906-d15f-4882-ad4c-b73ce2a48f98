#!/usr/bin/env python3
"""
Quick FPS Test for 4 Cameras
快速测试4个摄像头的FPS

Simple script to quickly test FPS performance of 4 cameras simultaneously.
"""

import cv2
import time
import threading
from typing import Dict, List
import numpy as np


def test_camera_fps(camera_indices: List[int] = [0, 2, 4, 6], duration: float = 10.0):
    """
    Test FPS for multiple cameras simultaneously.
    
    Args:
        camera_indices: List of camera indices to test
        duration: Test duration in seconds
    """
    print(f"🎥 Testing FPS for cameras: {camera_indices}")
    print(f"⏱️  Test duration: {duration} seconds")
    print("=" * 50)
    
    # Initialize cameras
    cameras = {}
    camera_frames = {}
    camera_fps = {}
    camera_running = True
    
    # Try to open each camera
    for cam_idx in camera_indices:
        try:
            cap = cv2.VideoCapture(cam_idx)
            if cap.isOpened():
                # Set properties for better performance
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 360)
                
                # Test frame capture
                ret, frame = cap.read()
                if ret:
                    cameras[cam_idx] = cap
                    camera_frames[cam_idx] = None
                    camera_fps[cam_idx] = 0.0
                    print(f"✅ Camera {cam_idx}: Initialized successfully")
                else:
                    cap.release()
                    print(f"❌ Camera {cam_idx}: Cannot read frames")
            else:
                print(f"❌ Camera {cam_idx}: Cannot open")
        except Exception as e:
            print(f"❌ Camera {cam_idx}: Error - {e}")
    
    if not cameras:
        print("❌ No cameras available for testing")
        return
    
    print(f"\n🚀 Starting FPS test with {len(cameras)} cameras...")
    
    def capture_frames(cam_idx: int, cap: cv2.VideoCapture):
        """Capture frames and calculate FPS for a single camera."""
        frame_count = 0
        start_time = time.time()
        last_fps_time = start_time
        
        while camera_running and cap.isOpened():
            ret, frame = cap.read()
            if ret:
                camera_frames[cam_idx] = frame
                frame_count += 1
                
                # Calculate FPS every second
                current_time = time.time()
                if current_time - last_fps_time >= 1.0:
                    fps = frame_count / (current_time - start_time)
                    camera_fps[cam_idx] = fps
                    last_fps_time = current_time
            
            time.sleep(0.03)  # ~30 FPS max
    
    # Start capture threads
    threads = []
    for cam_idx, cap in cameras.items():
        thread = threading.Thread(target=capture_frames, args=(cam_idx, cap))
        thread.daemon = True
        thread.start()
        threads.append(thread)
    
    # Monitor and report FPS
    start_time = time.time()
    try:
        while time.time() - start_time < duration:
            time.sleep(2.0)  # Report every 2 seconds
            
            elapsed = time.time() - start_time
            print(f"\n📊 FPS Report ({elapsed:.1f}s elapsed):")
            
            total_fps = 0
            active_cameras = 0
            
            for cam_idx in sorted(cameras.keys()):
                fps = camera_fps.get(cam_idx, 0)
                if fps > 0:
                    total_fps += fps
                    active_cameras += 1
                
                status = "🟢" if fps > 15 else "🟡" if fps > 5 else "🔴"
                print(f"  {status} Camera {cam_idx}: {fps:6.1f} FPS")
            
            if active_cameras > 0:
                avg_fps = total_fps / active_cameras
                print(f"  📈 Average FPS: {avg_fps:6.1f}")
                print(f"  🎯 Total Combined FPS: {total_fps:6.1f}")
    
    except KeyboardInterrupt:
        print("\n🛑 Test stopped by user")
    
    finally:
        # Cleanup
        print("\n🧹 Cleaning up...")
        camera_running = False
        
        for thread in threads:
            thread.join(timeout=1.0)
        
        for cap in cameras.values():
            cap.release()
        
        cv2.destroyAllWindows()
        
        # Final report
        print("\n" + "=" * 50)
        print("📋 FINAL TEST RESULTS")
        print("=" * 50)
        
        total_fps = 0
        active_cameras = 0
        max_fps = 0
        min_fps = float('inf')
        
        for cam_idx in sorted(cameras.keys()):
            fps = camera_fps.get(cam_idx, 0)
            if fps > 0:
                total_fps += fps
                active_cameras += 1
                max_fps = max(max_fps, fps)
                min_fps = min(min_fps, fps)
            
            status = "🟢" if fps > 15 else "🟡" if fps > 5 else "🔴"
            print(f"  {status} Camera {cam_idx}: {fps:6.1f} FPS")
        
        if active_cameras > 0:
            avg_fps = total_fps / active_cameras
            print(f"\n📊 Summary:")
            print(f"  • Active Cameras: {active_cameras}/{len(camera_indices)}")
            print(f"  • Average FPS: {avg_fps:.1f}")
            print(f"  • Maximum FPS: {max_fps:.1f}")
            print(f"  • Minimum FPS: {min_fps:.1f}")
            print(f"  • Total Combined FPS: {total_fps:.1f}")
            
            # Performance assessment
            if avg_fps >= 20:
                print(f"  🎉 Performance: Excellent")
            elif avg_fps >= 15:
                print(f"  👍 Performance: Good")
            elif avg_fps >= 10:
                print(f"  ⚠️  Performance: Fair")
            else:
                print(f"  ❌ Performance: Poor")
        else:
            print("❌ No cameras produced valid FPS data")
        
        print("=" * 50)


def test_individual_cameras():
    """Test each camera individually to identify working cameras."""
    print("🔍 Testing individual cameras...")
    camera_indices = [0, 1, 2, 3, 4, 5, 6, 7]  # Test more camera indices
    working_cameras = []
    
    for cam_idx in camera_indices:
        try:
            cap = cv2.VideoCapture(cam_idx)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    height, width = frame.shape[:2]
                    working_cameras.append(cam_idx)
                    print(f"✅ Camera {cam_idx}: Working ({width}x{height})")
                else:
                    print(f"❌ Camera {cam_idx}: Cannot read frames")
                cap.release()
            else:
                print(f"❌ Camera {cam_idx}: Cannot open")
        except Exception as e:
            print(f"❌ Camera {cam_idx}: Error - {e}")
        
        time.sleep(0.1)  # Small delay between tests
    
    print(f"\n📋 Working cameras found: {working_cameras}")
    return working_cameras


if __name__ == "__main__":
    print("🎥 Quick FPS Test for Multiple Cameras")
    print("=" * 50)
    
    # First, find working cameras
    working_cameras = test_individual_cameras()
    
    if len(working_cameras) >= 4:
        # Test with first 4 working cameras
        test_cameras = working_cameras[:4]
        print(f"\n🎯 Testing FPS with 4 cameras: {test_cameras}")
        test_camera_fps(test_cameras, duration=15.0)
    elif len(working_cameras) > 0:
        # Test with available cameras
        print(f"\n🎯 Testing FPS with {len(working_cameras)} available cameras: {working_cameras}")
        test_camera_fps(working_cameras, duration=15.0)
    else:
        print("\n❌ No working cameras found!")
        print("💡 Troubleshooting tips:")
        print("  • Check camera connections")
        print("  • Try different camera indices")
        print("  • Check if cameras are being used by other applications")
        print("  • Run: lsusb | grep -i camera")
