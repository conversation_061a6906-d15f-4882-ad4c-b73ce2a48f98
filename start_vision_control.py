#!/usr/bin/env python3
"""
视觉控制系统启动脚本

一键启动视觉控制系统，包含预检查和配置
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查系统依赖...")
    
    required_packages = [
        'ultralytics',
        'streamlit', 
        'opencv-python',
        'numpy',
        'torch'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖项检查通过")
    return True

def check_files():
    """检查必要文件"""
    print("\n🔍 检查系统文件...")
    
    required_files = [
        'Control/local_robot_controller.py',
        'ultralytics/solutions/streamlit_inference.py',
        'test_vision_control.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ 缺少文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有文件检查通过")
    return True

def run_system_test():
    """运行系统测试"""
    print("\n🧪 运行系统测试...")
    
    try:
        result = subprocess.run([
            sys.executable, 'test_vision_control.py'
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ 系统测试通过")
            return True
        else:
            print("❌ 系统测试失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 系统测试超时")
        return False
    except Exception as e:
        print(f"❌ 系统测试异常: {e}")
        return False

def start_streamlit():
    """启动Streamlit应用"""
    print("\n🚀 启动视觉控制系统...")
    print("=" * 50)
    print("📝 使用说明:")
    print("1. 在浏览器中打开显示的URL")
    print("2. 在侧边栏启用 'Vision Control'")
    print("3. 设置目标类别为 'redcar'")
    print("4. 调整检测阈值和控制参数")
    print("5. 点击 'Start' 开始视觉控制")
    print("6. 使用 Ctrl+C 停止系统")
    print("=" * 50)
    
    try:
        # 启动streamlit应用
        subprocess.run([
            'streamlit', 'run', 
            'ultralytics/solutions/streamlit_inference.py',
            '--server.headless', 'false',
            '--server.port', '8501',
            '--server.address', '0.0.0.0'
        ])
    except KeyboardInterrupt:
        print("\n\n⏹️ 系统已停止")
    except Exception as e:
        print(f"\n\n💥 启动失败: {e}")

def main():
    """主函数"""
    print("🚀 视觉控制系统启动器")
    print("=" * 50)
    
    # 检查当前目录
    if not Path('ultralytics').exists():
        print("❌ 请在ultralytics项目根目录下运行此脚本")
        sys.exit(1)
    
    # 1. 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 2. 检查文件
    if not check_files():
        sys.exit(1)
    
    # 3. 询问是否运行测试
    print("\n❓ 是否运行系统测试? (推荐)")
    choice = input("输入 y/yes 运行测试，其他键跳过: ").lower().strip()
    
    if choice in ['y', 'yes']:
        if not run_system_test():
            print("\n⚠️ 系统测试失败，但您仍可以尝试启动系统")
            choice = input("是否继续启动? (y/n): ").lower().strip()
            if choice not in ['y', 'yes']:
                sys.exit(1)
    
    # 4. 启动系统
    start_streamlit()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ 启动被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 启动过程中发生错误: {e}")
        sys.exit(1)
