#!/usr/bin/env python3
"""
视觉控制系统测试脚本

测试视觉感知模块和控制模块之间的通信和协作
"""

import time
import threading
import sys
import os

# 添加Control目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'Control'))

def test_controller():
    """测试控制器功能"""
    print("🧪 测试控制器功能...")
    
    try:
        from Control.local_robot_controller import LocalRobotController
        
        # 创建控制器实例
        controller = LocalRobotController(enable_safety=True)
        print("✅ 控制器创建成功")
        
        # 测试各种命令
        test_commands = [
            ('move_forward', {'speed': 0.2, 'duration': 0.5}),
            ('turn_left', {'angular_speed': 1.0, 'duration': 0.3}),
            ('move_backward', {'speed': 0.2, 'duration': 0.5}),
            ('turn_right', {'angular_speed': 1.0, 'duration': 0.3}),
            ('stop', {})
        ]
        
        for command, params in test_commands:
            print(f"📤 发送命令: {command} - {params}")
            controller.send_command(command, params)
            time.sleep(1.0)  # 等待命令执行
            
            # 检查状态
            status = controller.get_status()
            print(f"📊 控制器状态: {status}")
        
        print("✅ 控制器测试完成")
        controller.shutdown()
        return True
        
    except ImportError as e:
        print(f"❌ 无法导入控制器模块: {e}")
        return False
    except Exception as e:
        print(f"❌ 控制器测试失败: {e}")
        return False

def test_vision_module():
    """测试视觉模块（模拟）"""
    print("🧪 测试视觉模块...")
    
    try:
        from Control.local_robot_controller import get_controller
        
        # 获取控制器实例
        controller = get_controller()
        print("✅ 获取控制器实例成功")
        
        # 模拟视觉检测到目标的情况
        print("🎯 模拟检测到redcar...")
        
        # 模拟连续检测
        for i in range(25):  # 超过阈值20帧
            print(f"📹 帧 {i+1}: 检测到redcar")
            
            if i >= 19:  # 达到阈值后发送控制命令
                print("🚗 达到检测阈值，发送前进命令")
                controller.send_command('move_forward', {
                    'speed': 0.25,
                    'duration': 1.0,
                    'target_info': {
                        'class_name': 'redcar',
                        'confidence': 0.85,
                        'distance': 2.5,
                        'azimuth': 5.0
                    }
                })
                break
            
            time.sleep(0.1)  # 模拟帧间隔
        
        time.sleep(2.0)  # 等待命令执行
        
        print("✅ 视觉模块测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 视觉模块测试失败: {e}")
        return False

def test_integrated_system():
    """测试集成系统"""
    print("🧪 测试集成系统...")
    
    try:
        from Control.local_robot_controller import get_controller
        
        controller = get_controller()
        
        # 模拟复杂的视觉控制场景
        scenarios = [
            {
                'name': '目标在正前方',
                'target_info': {'distance': 2.0, 'azimuth': 0.0},
                'expected_command': 'move_forward'
            },
            {
                'name': '目标在右前方',
                'target_info': {'distance': 3.0, 'azimuth': 20.0},
                'expected_command': 'move_forward_right'
            },
            {
                'name': '目标在左前方',
                'target_info': {'distance': 1.5, 'azimuth': -25.0},
                'expected_command': 'move_forward_left'
            },
            {
                'name': '目标很近',
                'target_info': {'distance': 0.8, 'azimuth': 5.0},
                'expected_command': 'move_forward'  # 减速前进
            }
        ]
        
        for scenario in scenarios:
            print(f"\n📋 测试场景: {scenario['name']}")
            target_info = scenario['target_info']
            
            # 根据目标信息决定控制策略
            distance = target_info['distance']
            azimuth = target_info['azimuth']
            
            if azimuth > 15:
                command = 'move_forward_right'
                params = {'speed': 0.25, 'duration': 0.8, 'angular_speed': 0.5}
            elif azimuth < -15:
                command = 'move_forward_left'
                params = {'speed': 0.25, 'duration': 0.8, 'angular_speed': 0.5}
            else:
                command = 'move_forward'
                params = {'speed': 0.25, 'duration': 1.0}
            
            # 根据距离调整参数
            if distance < 1.0:
                params['speed'] = 0.15
                params['duration'] = 0.5
            elif distance > 3.0:
                params['speed'] = 0.35
                params['duration'] = 1.5
            
            print(f"🎯 目标信息: 距离={distance}m, 方位角={azimuth}°")
            print(f"🚗 执行命令: {command} - {params}")
            
            controller.send_command(command, params)
            time.sleep(1.5)  # 等待命令执行
        
        print("\n✅ 集成系统测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 集成系统测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始视觉控制系统测试")
    print("=" * 50)
    
    # 测试结果
    results = {}
    
    # 1. 测试控制器
    print("\n1️⃣ 测试控制器模块")
    results['controller'] = test_controller()
    
    # 2. 测试视觉模块
    print("\n2️⃣ 测试视觉模块")
    results['vision'] = test_vision_module()
    
    # 3. 测试集成系统
    print("\n3️⃣ 测试集成系统")
    results['integrated'] = test_integrated_system()
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    all_passed = all(results.values())
    if all_passed:
        print("\n🎉 所有测试通过！系统可以正常工作。")
        print("\n📝 使用说明:")
        print("1. 启动streamlit应用: streamlit run ultralytics/solutions/streamlit_inference.py")
        print("2. 在侧边栏启用 'Vision Control'")
        print("3. 设置目标类别为 'redcar'")
        print("4. 调整检测阈值和控制参数")
        print("5. 点击 'Start' 开始视觉控制")
    else:
        print("\n⚠️ 部分测试失败，请检查系统配置。")
    
    return all_passed

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 测试过程中发生未预期的错误: {e}")
        sys.exit(1)
