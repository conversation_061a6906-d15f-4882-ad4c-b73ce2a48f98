# 视觉控制系统 - Vision Control System

## 概述

这是一个解耦的视觉感知和机器人控制系统，基于YOLO目标检测和本地控制器实现。系统能够检测特定目标（如redcar），并根据目标的位置和距离信息自动控制机器人的移动。

## 系统架构

```
┌─────────────────────┐    队列通信    ┌─────────────────────┐
│   视觉感知模块        │ ──────────────► │    控制执行模块      │
│ (streamlit_inference) │                │ (local_robot_controller) │
│                     │                │                     │
│ • YOLO目标检测       │                │ • 10种基本行为       │
│ • 距离/方位角计算     │                │ • 队列通信          │
│ • 连续帧计数         │                │ • 安全检查          │
│ • 智能决策          │                │ • 状态管理          │
└─────────────────────┘                └─────────────────────┘
```

## 主要特性

### 视觉感知模块 (`ultralytics/solutions/streamlit_inference.py`)
- ✅ 基于YOLO的实时目标检测
- ✅ 距离和方位角估算
- ✅ 连续帧检测计数（默认20帧阈值）
- ✅ 智能控制决策（根据目标位置调整动作）
- ✅ Streamlit Web界面
- ✅ 多摄像头支持

### 控制执行模块 (`Control/local_robot_controller.py`)
- ✅ 10种基本移动行为
- ✅ 线程安全的命令队列
- ✅ 安全检查和限制
- ✅ 命令超时保护
- ✅ 状态监控

## 安装和配置

### 1. 依赖安装
```bash
# 确保已安装ultralytics和相关依赖
pip install ultralytics streamlit opencv-python numpy torch
```

### 2. 文件结构
```
ultralytics-main/
├── Control/
│   └── local_robot_controller.py    # 本地控制器
├── ultralytics/solutions/
│   └── streamlit_inference.py       # 增强的视觉感知模块
├── test_vision_control.py           # 测试脚本
└── VISION_CONTROL_README.md         # 本文档
```

## 使用方法

### 1. 系统测试
首先运行测试脚本确保系统正常工作：
```bash
python test_vision_control.py
```

### 2. 启动视觉控制系统
```bash
streamlit run ultralytics/solutions/streamlit_inference.py
```

### 3. 配置参数
在Streamlit界面的侧边栏中：

#### 基础设置
- **Source**: 选择摄像头源（webcam/multi-webcam）
- **Model**: 选择YOLO模型
- **Classes**: 选择要检测的类别

#### 距离估算设置
- ✅ 启用 "Enable Distance Estimation"
- 设置目标真实宽度（默认0.31m适用于小车）
- 配置相机参数（如果需要）

#### 视觉控制设置
- ✅ 启用 "Enable Vision Control"
- **Target Class**: 选择目标类别（默认"redcar"）
- **Detection Threshold**: 连续检测帧数阈值（5-50帧）
- **Control Cooldown**: 控制动作间隔时间（0.5-10秒）

### 4. 手动控制测试
使用侧边栏的"Manual Control"面板测试基本控制功能：
- ⬆️ Forward: 前进
- ⬇️ Backward: 后退
- ⬅️ Left: 左转
- ➡️ Right: 右转
- 🛑 Stop: 停止
- 🔄 Rotate: 原地旋转

## 控制逻辑

### 检测触发条件
1. 连续检测到目标类别达到设定阈值（默认20帧）
2. 目标置信度超过设定阈值
3. 距离上次控制动作超过冷却时间

### 智能控制策略
根据目标的方位角和距离自动选择最佳控制动作：

| 目标位置 | 方位角范围 | 控制动作 | 说明 |
|---------|-----------|---------|------|
| 正前方 | -15° ~ +15° | move_forward | 直线前进 |
| 右前方 | > +15° | move_forward_right | 右前方移动 |
| 左前方 | < -15° | move_forward_left | 左前方移动 |

### 距离自适应
- **近距离** (< 1.0m): 减速前进，速度0.15m/s
- **中距离** (1.0-3.0m): 正常速度，0.25m/s
- **远距离** (> 3.0m): 加速前进，速度0.35m/s

## 10种基本控制行为

| 命令 | 功能 | 参数 |
|------|------|------|
| `move_forward` | 前进 | speed, duration |
| `move_backward` | 后退 | speed, duration |
| `turn_left` | 左转 | angular_speed, duration |
| `turn_right` | 右转 | angular_speed, duration |
| `move_left` | 左移 | speed, duration |
| `move_right` | 右移 | speed, duration |
| `move_forward_left` | 左前移动 | speed, duration |
| `move_forward_right` | 右前移动 | speed, duration |
| `move_backward_left` | 左后移动 | speed, duration |
| `move_backward_right` | 右后移动 | speed, duration |
| `stop` | 停止 | - |
| `emergency_stop` | 紧急停止 | - |

## 安全特性

### 控制器安全
- ✅ 速度限制（线速度≤0.5m/s，角速度≤3.0rad/s）
- ✅ 命令超时保护（2秒）
- ✅ 紧急停止功能
- ✅ 队列大小限制（100条命令）

### 视觉控制安全
- ✅ 连续检测要求（防止误触发）
- ✅ 控制冷却时间（防止频繁动作）
- ✅ 置信度阈值检查
- ✅ 异常处理和日志记录

## 故障排除

### 常见问题

1. **控制器无法导入**
   ```
   ❌ 无法加载机器人控制器模块
   ```
   - 检查Control目录是否存在
   - 确保local_robot_controller.py文件完整

2. **摄像头无法打开**
   ```
   ❌ Could not open webcam or video source
   ```
   - 检查摄像头连接
   - 尝试不同的摄像头索引（0,1,2...）

3. **检测不到目标**
   ```
   ⚠️ 没有检测到目标
   ```
   - 降低置信度阈值
   - 检查目标类别名称是否正确
   - 确保光照条件良好

4. **控制命令无响应**
   ```
   ❌ 机器人控制器不可用
   ```
   - 检查控制器是否正确初始化
   - 查看控制器状态和队列大小

### 调试模式
启用距离估算的调试模式可以看到详细的计算过程：
```python
self.distance_debug = True
```

## 扩展开发

### 添加新的控制行为
在`LocalRobotController`类中添加新方法：
```python
def custom_action(self, params):
    """自定义动作"""
    # 实现自定义控制逻辑
    pass
```

### 修改检测逻辑
在`process_vision_control`方法中自定义检测和决策逻辑：
```python
def process_vision_control(self, result, camera_id):
    # 自定义检测逻辑
    pass
```

### 集成硬件接口
修改`set_velocity`方法以连接实际的硬件控制接口：
```python
def set_velocity(self, x, y, theta, duration):
    # 替换为实际的硬件控制代码
    # 例如：serial通信、GPIO控制等
    pass
```

## 许可证

Copyright (c) 2024 WindyLab of Westlake University, China
All rights reserved.
