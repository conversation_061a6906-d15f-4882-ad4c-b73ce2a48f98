#!/usr/bin/env python3
"""
FPS Optimization Tool
FPS优化工具

Diagnose and optimize camera FPS performance issues.
"""

import cv2
import time
import threading
import sys
from typing import Dict, List, Tuple
import numpy as np


class FPSOptimizer:
    """Tool to diagnose and optimize camera FPS."""
    
    def __init__(self):
        self.results = {}
    
    def test_single_camera_performance(self, cam_idx: int, duration: float = 10.0) -> Dict:
        """Test single camera performance with different settings."""
        print(f"🔬 Testing Camera {cam_idx} performance...")
        
        results = {
            'camera_index': cam_idx,
            'tests': {}
        }
        
        # Test configurations
        configs = [
            {'name': 'Default', 'settings': {}},
            {'name': 'MJPEG', 'settings': {'fourcc': cv2.VideoWriter_fourcc(*'MJPG')}},
            {'name': 'Low Buffer', 'settings': {'buffer': 1}},
            {'name': 'High FPS', 'settings': {'fps': 60}},
            {'name': 'Optimized', 'settings': {
                'fourcc': cv2.VideoWriter_fourcc(*'MJPG'),
                'buffer': 1,
                'fps': 30,
                'auto_exposure': 0.25,
                'autofocus': 0
            }}
        ]
        
        for config in configs:
            print(f"  Testing {config['name']} configuration...")
            fps = self._test_camera_config(cam_idx, config['settings'], duration)
            results['tests'][config['name']] = fps
            print(f"    Result: {fps:.1f} FPS")
        
        return results
    
    def _test_camera_config(self, cam_idx: int, settings: Dict, duration: float) -> float:
        """Test camera with specific configuration."""
        try:
            cap = cv2.VideoCapture(cam_idx)
            if not cap.isOpened():
                return 0.0
            
            # Apply settings
            if 'fourcc' in settings:
                cap.set(cv2.CAP_PROP_FOURCC, settings['fourcc'])
            if 'buffer' in settings:
                cap.set(cv2.CAP_PROP_BUFFERSIZE, settings['buffer'])
            if 'fps' in settings:
                cap.set(cv2.CAP_PROP_FPS, settings['fps'])
            if 'auto_exposure' in settings:
                cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, settings['auto_exposure'])
            if 'autofocus' in settings:
                cap.set(cv2.CAP_PROP_AUTOFOCUS, settings['autofocus'])
            
            # Set resolution
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 360)
            
            # Warm up
            for _ in range(5):
                cap.read()
            
            # Test FPS
            frame_count = 0
            start_time = time.time()
            
            while time.time() - start_time < duration:
                ret, frame = cap.read()
                if ret:
                    frame_count += 1
                time.sleep(0.01)  # Small delay to prevent CPU overload
            
            elapsed = time.time() - start_time
            fps = frame_count / elapsed
            
            cap.release()
            return fps
            
        except Exception as e:
            print(f"    Error: {e}")
            return 0.0
    
    def test_multi_camera_interference(self, camera_indices: List[int], duration: float = 10.0) -> Dict:
        """Test how cameras interfere with each other."""
        print(f"🔄 Testing multi-camera interference...")
        
        results = {
            'single_camera_fps': {},
            'multi_camera_fps': {}
        }
        
        # Test each camera individually
        print("  Testing individual camera performance...")
        for cam_idx in camera_indices:
            fps = self._test_camera_config(cam_idx, {
                'fourcc': cv2.VideoWriter_fourcc(*'MJPG'),
                'buffer': 1
            }, duration)
            results['single_camera_fps'][cam_idx] = fps
            print(f"    Camera {cam_idx} alone: {fps:.1f} FPS")
        
        # Test cameras together
        print("  Testing cameras together...")
        multi_fps = self._test_multiple_cameras(camera_indices, duration)
        results['multi_camera_fps'] = multi_fps
        
        return results
    
    def _test_multiple_cameras(self, camera_indices: List[int], duration: float) -> Dict[int, float]:
        """Test multiple cameras simultaneously."""
        cameras = {}
        threads = {}
        frame_counts = {cam_idx: 0 for cam_idx in camera_indices}
        running = True
        
        # Initialize cameras
        for cam_idx in camera_indices:
            try:
                cap = cv2.VideoCapture(cam_idx)
                if cap.isOpened():
                    cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 360)
                    cameras[cam_idx] = cap
            except Exception as e:
                print(f"    Failed to initialize camera {cam_idx}: {e}")
        
        def capture_thread(cam_idx: int, cap: cv2.VideoCapture):
            while running:
                ret, frame = cap.read()
                if ret:
                    frame_counts[cam_idx] += 1
                time.sleep(0.01)
        
        # Start threads
        start_time = time.time()
        for cam_idx, cap in cameras.items():
            thread = threading.Thread(target=capture_thread, args=(cam_idx, cap))
            thread.daemon = True
            thread.start()
            threads[cam_idx] = thread
        
        # Wait for duration
        time.sleep(duration)
        running = False
        
        # Calculate FPS
        elapsed = time.time() - start_time
        fps_results = {}
        for cam_idx in camera_indices:
            if cam_idx in frame_counts:
                fps = frame_counts[cam_idx] / elapsed
                fps_results[cam_idx] = fps
                print(f"    Camera {cam_idx} together: {fps:.1f} FPS")
            else:
                fps_results[cam_idx] = 0.0
        
        # Cleanup
        for cap in cameras.values():
            cap.release()
        
        return fps_results
    
    def test_resolution_impact(self, cam_idx: int) -> Dict:
        """Test how resolution affects FPS."""
        print(f"📐 Testing resolution impact on Camera {cam_idx}...")
        
        resolutions = [
            (320, 240, "QVGA"),
            (640, 360, "nHD"),
            (640, 480, "VGA"),
            (800, 600, "SVGA"),
            (1280, 720, "HD"),
            (1920, 1080, "FHD")
        ]
        
        results = {}
        
        for width, height, name in resolutions:
            print(f"  Testing {name} ({width}x{height})...")
            
            try:
                cap = cv2.VideoCapture(cam_idx)
                if not cap.isOpened():
                    results[name] = 0.0
                    continue
                
                # Optimize settings
                cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
                
                # Test FPS
                frame_count = 0
                start_time = time.time()
                test_duration = 5.0
                
                while time.time() - start_time < test_duration:
                    ret, frame = cap.read()
                    if ret:
                        frame_count += 1
                    time.sleep(0.01)
                
                elapsed = time.time() - start_time
                fps = frame_count / elapsed
                results[name] = fps
                
                print(f"    {name}: {fps:.1f} FPS")
                cap.release()
                
            except Exception as e:
                print(f"    {name}: Error - {e}")
                results[name] = 0.0
        
        return results
    
    def generate_optimization_report(self, camera_indices: List[int]) -> None:
        """Generate comprehensive optimization report."""
        print("🚀 FPS Optimization Analysis")
        print("=" * 50)
        
        # Test individual cameras
        individual_results = {}
        for cam_idx in camera_indices:
            if self._camera_available(cam_idx):
                individual_results[cam_idx] = self.test_single_camera_performance(cam_idx, 5.0)
        
        # Test multi-camera interference
        interference_results = self.test_multi_camera_interference(camera_indices, 5.0)
        
        # Test resolution impact on first available camera
        resolution_results = {}
        for cam_idx in camera_indices:
            if self._camera_available(cam_idx):
                resolution_results[cam_idx] = self.test_resolution_impact(cam_idx)
                break
        
        # Generate report
        print("\n📊 OPTIMIZATION REPORT")
        print("=" * 50)
        
        # Individual camera performance
        print("\n🎥 Individual Camera Performance:")
        for cam_idx, results in individual_results.items():
            print(f"\nCamera {cam_idx}:")
            best_config = max(results['tests'].items(), key=lambda x: x[1])
            for config, fps in results['tests'].items():
                status = "⭐" if config == best_config[0] else "  "
                print(f"  {status} {config}: {fps:.1f} FPS")
        
        # Multi-camera interference
        print("\n🔄 Multi-Camera Interference Analysis:")
        if interference_results:
            for cam_idx in camera_indices:
                if cam_idx in interference_results['single_camera_fps']:
                    single_fps = interference_results['single_camera_fps'][cam_idx]
                    multi_fps = interference_results['multi_camera_fps'].get(cam_idx, 0)
                    if single_fps > 0:
                        interference = (single_fps - multi_fps) / single_fps * 100
                        print(f"  Camera {cam_idx}: {single_fps:.1f} → {multi_fps:.1f} FPS ({interference:.1f}% loss)")
        
        # Resolution impact
        if resolution_results:
            print("\n📐 Resolution Impact:")
            for cam_idx, results in resolution_results.items():
                print(f"\nCamera {cam_idx}:")
                for resolution, fps in results.items():
                    print(f"  {resolution}: {fps:.1f} FPS")
        
        # Recommendations
        print("\n💡 OPTIMIZATION RECOMMENDATIONS:")
        print("=" * 40)
        
        if individual_results:
            best_fps = max([max(r['tests'].values()) for r in individual_results.values()])
            if best_fps < 15:
                print("🔴 LOW FPS DETECTED - Critical optimizations needed:")
                print("  • Use 320x240 resolution")
                print("  • Enable MJPEG codec")
                print("  • Set buffer size to 1")
                print("  • Disable auto-exposure and autofocus")
                print("  • Check USB bandwidth limitations")
            elif best_fps < 25:
                print("🟡 MODERATE FPS - Some optimizations recommended:")
                print("  • Use 640x360 resolution")
                print("  • Enable MJPEG codec")
                print("  • Reduce number of simultaneous cameras")
            else:
                print("🟢 GOOD FPS - Minor optimizations:")
                print("  • Current settings are working well")
                print("  • Consider higher resolution if needed")
        
        print("\n🔧 System-level optimizations:")
        print("  • Close other camera applications")
        print("  • Use USB 3.0 ports")
        print("  • Distribute cameras across different USB controllers")
        print("  • Increase system priority for camera processes")
    
    def _camera_available(self, cam_idx: int) -> bool:
        """Check if camera is available."""
        try:
            cap = cv2.VideoCapture(cam_idx)
            available = cap.isOpened()
            cap.release()
            return available
        except:
            return False


def main():
    """Main function."""
    print("🔧 FPS Optimization Tool")
    print("=" * 30)
    
    # Get camera indices
    try:
        camera_input = input("Enter camera indices to test (default: 0,2,4,6): ").strip()
        if camera_input:
            camera_indices = [int(x.strip()) for x in camera_input.split(",")]
        else:
            camera_indices = [0, 2, 4, 6]
    except ValueError:
        print("Invalid input, using default: 0,2,4,6")
        camera_indices = [0, 2, 4, 6]
    
    optimizer = FPSOptimizer()
    optimizer.generate_optimization_report(camera_indices)


if __name__ == "__main__":
    main()
