#!/usr/bin/env python3
"""
深度诊断USB摄像头问题
找出为什么即使低分辨率也无法同时工作
"""

import cv2
import time
import threading
import psutil
import subprocess
import os
from collections import defaultdict

def analyze_usb_bandwidth_reality():
    """分析USB带宽的实际情况"""
    print("🔍 USB带宽实际分析")
    print("=" * 50)
    
    print("📊 理论 vs 实际带宽:")
    print("   USB 2.0理论带宽: 480 Mbps")
    print("   USB 2.0实际可用: ~60-70% = 288-336 Mbps")
    print("   原因: 协议开销、错误校正、其他设备占用")
    
    # 检查USB设备占用
    try:
        result = subprocess.run(['lsusb'], capture_output=True, text=True)
        usb_devices = result.stdout.split('\n')
        active_devices = [line for line in usb_devices if line.strip() and 'root hub' not in line]
        
        print(f"\n🔌 当前USB设备数量: {len(active_devices)}")
        for device in active_devices:
            if device.strip():
                print(f"   {device}")
                
    except Exception as e:
        print(f"❌ 无法获取USB设备信息: {e}")

def test_progressive_camera_loading():
    """渐进式摄像头加载测试"""
    print(f"\n🧪 渐进式摄像头加载测试")
    print("=" * 50)
    
    camera_indices = [0, 2, 4, 6]
    active_cameras = {}
    
    for i, cam_idx in enumerate(camera_indices, 1):
        print(f"\n📷 尝试添加第{i}个摄像头 (索引{cam_idx})...")
        
        try:
            cap = cv2.VideoCapture(cam_idx)
            if cap.isOpened():
                # 设置低分辨率
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 360)
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                
                # 等待摄像头稳定
                time.sleep(0.5)
                
                # 测试连续读取
                success_count = 0
                total_attempts = 10
                
                for attempt in range(total_attempts):
                    ret, frame = cap.read()
                    if ret and frame is not None and frame.size > 0:
                        success_count += 1
                    time.sleep(0.1)
                
                success_rate = success_count / total_attempts
                print(f"   成功率: {success_rate*100:.1f}% ({success_count}/{total_attempts})")
                
                if success_rate >= 0.8:  # 80%成功率认为稳定
                    active_cameras[cam_idx] = cap
                    print(f"   ✅ 摄像头{cam_idx}添加成功")
                    
                    # 测试所有活跃摄像头
                    print(f"   🔄 测试{len(active_cameras)}个摄像头同时工作...")
                    simultaneous_test_result = test_simultaneous_capture(active_cameras, duration=3)
                    
                    if not simultaneous_test_result:
                        print(f"   ❌ 同时工作失败，移除摄像头{cam_idx}")
                        cap.release()
                        del active_cameras[cam_idx]
                        break
                else:
                    print(f"   ❌ 摄像头{cam_idx}不稳定，跳过")
                    cap.release()
            else:
                print(f"   ❌ 无法打开摄像头{cam_idx}")
                
        except Exception as e:
            print(f"   ❌ 摄像头{cam_idx}异常: {e}")
    
    print(f"\n📊 最终结果: {len(active_cameras)}个摄像头可同时工作")
    print(f"   工作的摄像头: {list(active_cameras.keys())}")
    
    # 清理
    for cap in active_cameras.values():
        cap.release()
    
    return list(active_cameras.keys())

def test_simultaneous_capture(cameras_dict, duration=3):
    """测试多摄像头同时捕获"""
    frame_counts = defaultdict(int)
    errors = defaultdict(int)
    
    def capture_thread(cam_idx, cap):
        for _ in range(int(duration * 10)):  # 每秒10次
            try:
                ret, frame = cap.read()
                if ret and frame is not None and frame.size > 0:
                    frame_counts[cam_idx] += 1
                else:
                    errors[cam_idx] += 1
            except Exception as e:
                errors[cam_idx] += 1
            time.sleep(0.1)
    
    # 启动所有线程
    threads = []
    for cam_idx, cap in cameras_dict.items():
        thread = threading.Thread(target=capture_thread, args=(cam_idx, cap))
        thread.start()
        threads.append(thread)
    
    # 等待完成
    for thread in threads:
        thread.join()
    
    # 分析结果
    total_expected = duration * 10
    all_success = True
    
    for cam_idx in cameras_dict.keys():
        success_count = frame_counts[cam_idx]
        error_count = errors[cam_idx]
        success_rate = success_count / total_expected
        
        print(f"     摄像头{cam_idx}: {success_count}/{total_expected} 成功 ({success_rate*100:.1f}%), {error_count} 错误")
        
        if success_rate < 0.7:  # 70%成功率阈值
            all_success = False
    
    return all_success

def test_different_formats():
    """测试不同的视频格式"""
    print(f"\n🎨 测试不同视频格式")
    print("=" * 50)
    
    formats = [
        ("默认", None),
        ("MJPG", cv2.VideoWriter_fourcc(*'MJPG')),
        ("YUYV", cv2.VideoWriter_fourcc(*'YUYV')),
    ]
    
    cam_idx = 0  # 使用摄像头0测试
    
    for format_name, fourcc in formats:
        print(f"\n📹 测试格式: {format_name}")
        
        try:
            cap = cv2.VideoCapture(cam_idx)
            if cap.isOpened():
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 360)
                
                if fourcc:
                    cap.set(cv2.CAP_PROP_FOURCC, fourcc)
                
                time.sleep(0.2)
                
                # 获取实际格式信息
                actual_fourcc = int(cap.get(cv2.CAP_PROP_FOURCC))
                actual_format = "".join([chr((actual_fourcc >> 8 * i) & 0xFF) for i in range(4)])
                actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                actual_fps = cap.get(cv2.CAP_PROP_FPS)
                
                print(f"   实际格式: {actual_format}")
                print(f"   实际分辨率: {actual_width}x{actual_height}")
                print(f"   实际帧率: {actual_fps}")
                
                # 测试读取性能
                start_time = time.time()
                frame_count = 0
                
                for _ in range(30):  # 读取30帧
                    ret, frame = cap.read()
                    if ret:
                        frame_count += 1
                    time.sleep(0.01)
                
                elapsed = time.time() - start_time
                fps = frame_count / elapsed
                
                # 计算实际带宽
                if frame_count > 0:
                    bytes_per_frame = actual_width * actual_height * 2  # 假设YUYV
                    if actual_format == "MJPG":
                        bytes_per_frame = actual_width * actual_height * 0.5  # MJPG压缩估算
                    
                    actual_bandwidth = (bytes_per_frame * fps * 8) / (1024 * 1024)
                    print(f"   实际FPS: {fps:.1f}")
                    print(f"   估算带宽: {actual_bandwidth:.1f} Mbps")
                
                cap.release()
            else:
                print(f"   ❌ 无法打开摄像头")
                
        except Exception as e:
            print(f"   ❌ 格式测试异常: {e}")

def check_system_resources():
    """检查系统资源使用情况"""
    print(f"\n💻 系统资源检查")
    print("=" * 50)
    
    # CPU使用率
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"CPU使用率: {cpu_percent}%")
    
    # 内存使用率
    memory = psutil.virtual_memory()
    print(f"内存使用率: {memory.percent}% ({memory.used//1024//1024}MB/{memory.total//1024//1024}MB)")
    
    # USB相关进程
    print(f"\n🔍 USB相关进程:")
    for proc in psutil.process_iter(['pid', 'name', 'cpu_percent']):
        try:
            if any(keyword in proc.info['name'].lower() for keyword in ['usb', 'camera', 'video', 'v4l']):
                print(f"   PID {proc.info['pid']}: {proc.info['name']} (CPU: {proc.info['cpu_percent']}%)")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass

def test_usb_controller_limits():
    """测试USB控制器的实际限制"""
    print(f"\n🔌 USB控制器限制测试")
    print("=" * 50)
    
    print("📋 测试策略:")
    print("1. 单个摄像头最大带宽测试")
    print("2. 双摄像头组合测试")
    print("3. 找出实际的并发限制")
    
    # 单摄像头高分辨率测试
    print(f"\n📷 单摄像头最大分辨率测试:")
    resolutions = [(1920, 1080), (1280, 720), (640, 480), (640, 360)]
    
    for width, height in resolutions:
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
            
            time.sleep(0.2)
            
            success_count = 0
            for _ in range(10):
                ret, frame = cap.read()
                if ret:
                    success_count += 1
                time.sleep(0.1)
            
            success_rate = success_count / 10
            print(f"   {width}x{height}: {success_rate*100:.1f}% 成功率")
            
            cap.release()
            
            if success_rate < 0.8:
                print(f"   ⚠️  {width}x{height} 不稳定，可能接近带宽限制")
                break

def main():
    print("🔬 深度诊断: 为什么640x360仍然失败?")
    print("=" * 60)
    
    # 1. 分析USB带宽实际情况
    analyze_usb_bandwidth_reality()
    
    # 2. 检查系统资源
    check_system_resources()
    
    # 3. 测试不同格式
    test_different_formats()
    
    # 4. USB控制器限制测试
    test_usb_controller_limits()
    
    # 5. 渐进式摄像头加载
    working_cameras = test_progressive_camera_loading()
    
    print(f"\n🎯 诊断结论")
    print("=" * 50)
    print(f"✅ 可同时工作的摄像头: {working_cameras}")
    print(f"📊 最大并发数: {len(working_cameras)}")
    
    if len(working_cameras) < 4:
        print(f"\n💡 可能的原因:")
        print(f"   1. USB控制器硬件限制 (不仅仅是带宽)")
        print(f"   2. 驱动程序限制")
        print(f"   3. 系统资源不足")
        print(f"   4. USB Hub内部限制")
        print(f"   5. 摄像头固件限制")
        
        print(f"\n🛠️  建议解决方案:")
        print(f"   1. 使用不同的USB端口 (方案2)")
        print(f"   2. 降低到更低分辨率 (320x240)")
        print(f"   3. 使用MJPG压缩格式")
        print(f"   4. 考虑网络摄像头方案")

if __name__ == "__main__":
    main()
