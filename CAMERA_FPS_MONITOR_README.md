# 纯摄像头FPS监控器 / Pure Camera FPS Monitor

## 📋 概述 / Overview

这是一个专门的多摄像头FPS监控应用，**完全不包含YOLO检测**，专注于显示纯摄像头画面和FPS性能分析。

### 🎯 主要用途
- 测试四个摄像头同时运行的原始FPS性能
- 分析USB带宽限制对摄像头的影响
- 建立性能基准线（无AI处理开销）
- 识别最佳摄像头配置

## 🚀 快速开始

### 方法1: 使用专用启动脚本
```bash
./run_camera_fps_monitor.sh
```

### 方法2: 直接运行
```bash
python3 -m streamlit run streamlit_camera_fps_only.py
```

### 方法3: 通过主菜单
```bash
./run_fps_tests.sh
```
选择选项3 "Camera FPS Monitor (pure camera feeds, no AI detection)"

## 📊 功能特性

### 🎥 摄像头管理
- **多摄像头支持**: 最多8个摄像头同时监控
- **智能配置**: 预设的摄像头组合（避免USB冲突）
- **分辨率选择**: 640x360, 640x480, 1280x720, 1920x1080, 自定义
- **实时预览**: 2x2或3x3网格布局

### 📈 FPS监控
- **实时FPS计算**: 每个摄像头独立计算
- **滚动窗口平均**: 基于最近30帧的FPS
- **颜色编码状态**: 🟢优秀 🟡良好 🔴较差
- **整体性能统计**: 平均FPS、总处理帧数

### 🎛️ 显示选项
- **FPS叠加显示**: 在画面上显示实时FPS
- **时间戳**: 可选的时间戳显示
- **画质调节**: 50%-100%画质设置
- **更新频率**: 可调节的FPS更新间隔

## ⚙️ 配置选项

### 摄像头配置预设
```
Reliable (0,2)          - 最稳定的双摄像头配置
All Available (0,2,4,6) - 四摄像头标准配置
Sequential (0,1,2,3)    - 连续索引（可能有USB冲突）
Even Numbers (0,2,4,6)  - 偶数索引
Odd Numbers (1,3,5,7)   - 奇数索引
Custom                  - 自定义配置
```

### 推荐分辨率设置
```
640x360  - 16:9宽屏，最佳多摄像头性能
640x480  - 4:3标准，兼容性好
800x600  - 中等分辨率
1280x720 - 高清，单摄像头推荐
1920x1080- 全高清，高性能系统
```

## 📊 性能基准

### 预期FPS（无AI处理）
```
分辨率      单摄像头    双摄像头    四摄像头
640x360     28-30      22-25      18-22
640x480     25-28      20-23      15-20
1280x720    20-25      15-20      10-15
1920x1080   15-20      10-15      5-10
```

### 系统资源使用
- **CPU使用率**: 5-15%（无AI处理）
- **内存使用**: 100-300MB
- **USB带宽**: 主要瓶颈

## 🔧 使用指南

### 首次使用建议
1. **测试摄像头可用性**
   ```bash
   ./run_camera_fps_monitor.sh
   # 选择选项2: Test Camera Availability First
   ```

2. **从低分辨率开始**
   - 先使用640x360分辨率
   - 确认所有摄像头正常工作
   - 逐步提高分辨率测试性能

3. **监控时间建议**
   - 至少运行30秒获得稳定读数
   - 观察FPS是否稳定
   - 记录最佳配置参数

### 最佳实践
```bash
# 1. 启动应用
python3 -m streamlit run streamlit_camera_fps_only.py

# 2. 在Web界面中配置:
#    - 摄像头数量: 4
#    - 摄像头配置: All Available (0,2,4,6)
#    - 分辨率: 640x360
#    - 启用FPS叠加显示

# 3. 点击"Start Monitoring"开始监控

# 4. 观察指标:
#    - 每个摄像头的FPS
#    - 平均FPS
#    - 整体处理FPS
```

## 📈 性能分析

### 关键指标解读
- **Individual Camera FPS**: 每个摄像头的实际捕获帧率
- **Average Camera FPS**: 所有摄像头的平均FPS
- **Overall Processing FPS**: 系统整体处理能力
- **Active Cameras**: 成功初始化的摄像头数量

### 性能优化建议
1. **USB优化**
   - 使用USB 3.0接口
   - 分散摄像头到不同USB控制器
   - 避免USB Hub级联

2. **分辨率优化**
   - 640x360: 最佳多摄像头性能
   - 避免超过系统处理能力的分辨率

3. **系统优化**
   - 关闭其他摄像头应用
   - 确保足够的系统资源
   - 使用SSD存储提高I/O性能

## 🛠️ 故障排除

### 常见问题

1. **摄像头无法打开**
   ```bash
   # 检查摄像头设备
   ls /dev/video*
   
   # 测试摄像头
   python3 -c "import cv2; print([cv2.VideoCapture(i).isOpened() for i in range(8)])"
   ```

2. **FPS过低**
   - 降低分辨率到640x360
   - 减少同时运行的摄像头数量
   - 检查USB带宽使用情况

3. **部分摄像头失败**
   - 这是正常现象（USB带宽限制）
   - 尝试不同的摄像头索引组合
   - 使用"Reliable (0,2)"配置

### 调试命令
```bash
# 检查USB设备
lsusb | grep -i camera

# 检查视频设备
v4l2-ctl --list-devices

# 测试特定摄像头
python3 -c "
import cv2
cap = cv2.VideoCapture(0)
print(f'Camera 0: {cap.isOpened()}')
if cap.isOpened():
    ret, frame = cap.read()
    print(f'Frame capture: {ret}, Shape: {frame.shape if ret else None}')
cap.release()
"
```

## 📋 与YOLO版本的对比

| 特性 | 纯FPS监控器 | YOLO检测版本 |
|------|-------------|--------------|
| FPS性能 | 最高 | 较低（AI处理开销） |
| CPU使用 | 5-15% | 30-80% |
| 内存使用 | 100-300MB | 500MB-2GB |
| 功能 | 纯监控 | 目标检测+监控 |
| 用途 | 性能基准测试 | 实际应用 |

## 🎯 使用场景

### 适合的场景
- **性能基准测试**: 确定硬件极限
- **系统调试**: 排除AI处理的影响
- **配置优化**: 找到最佳摄像头设置
- **硬件验证**: 测试新摄像头或系统

### 不适合的场景
- 需要目标检测的应用
- 生产环境监控
- 需要AI分析的场景

## 📞 技术支持

如果遇到问题，请提供：
- 系统信息 (`uname -a`)
- 摄像头型号和数量
- 使用的分辨率设置
- 完整的错误日志
- FPS监控截图
