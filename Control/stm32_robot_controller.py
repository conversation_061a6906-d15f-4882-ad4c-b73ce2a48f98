#!/usr/bin/env python3
"""
STM32机器人控制板Python接口

基于RobotControlBoard-main的通信协议实现的Python控制接口
支持串口和USB虚拟串口通信，提供完整的机器人控制功能

Copyright (c) 2024 WindyLab of Westlake University, China
All rights reserved.
"""

import serial
import struct
import time
import threading
import queue
import logging
from typing import Optional, Tuple, Dict, Any, List
from dataclasses import dataclass
from enum import IntEnum
import crc16

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CommandCode(IntEnum):
    """命令码定义"""
    # 控制命令
    MOVE_MODE_CMD = 0x50      # 移动模式设置指令
    SPEED_CTL_CMD = 0x60      # 速度控制
    UP_RGB_CMD = 0x70         # 上彩灯指令
    DOWN_RGB_CMD = 0x71       # 下彩灯指令
    
    # 数据消息
    IMU_MSG = 0x10           # IMU数据
    GPS_MSG = 0x28           # GPS数据
    ODOMETRY_MSG = 0x12      # 里程计数据
    BATTERY_MSG = 0x02       # 电池数据
    VICON_DATA_MSG = 0x18    # VICON数据指令
    HEART_MSG = 0x01         # 心跳包数据


class ControlMode(IntEnum):
    """控制模式"""
    MOTOR_CONTROL = 0        # 电机控制模式
    MOVE_CONTROL = 1         # 速度控制模式


@dataclass
class IMUData:
    """IMU数据结构"""
    accel_x: float
    accel_y: float
    accel_z: float
    gyro_x: float
    gyro_y: float
    gyro_z: float
    mag_x: float
    mag_y: float
    mag_z: float
    temperature: float


@dataclass
class OdometryData:
    """里程计数据结构"""
    vx: float              # X方向速度
    vy: float              # Y方向速度
    wz: float              # 角速度
    dis_x: float           # X方向位移
    dis_y: float           # Y方向位移
    yaw_all: float         # 总偏航角


@dataclass
class BatteryData:
    """电池数据结构"""
    voltage: float         # 电压
    current: float         # 电流
    percentage: int        # 电量百分比
    temperature: float     # 温度


class STM32RobotController:
    """STM32机器人控制器"""
    
    # 协议常量
    SEND_FRAME_HEAD = 0xFE
    SEND_FRAME_ADDR = 0x55
    RECV_FRAME_HEAD = 0xFE
    RECV_FRAME_ADDR = 0xAA
    
    def __init__(self, port: str = '/dev/ttyUSB0', baudrate: int = 921600, timeout: float = 1.0):
        """
        初始化STM32机器人控制器
        
        Args:
            port: 串口设备路径
            baudrate: 波特率
            timeout: 超时时间
        """
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        
        # 串口连接
        self.serial_conn: Optional[serial.Serial] = None
        self.connected = False
        
        # 数据队列
        self.send_queue = queue.Queue(maxsize=100)
        self.recv_queue = queue.Queue(maxsize=100)
        
        # 线程控制
        self.running = False
        self.send_thread: Optional[threading.Thread] = None
        self.recv_thread: Optional[threading.Thread] = None
        
        # 状态数据
        self.imu_data: Optional[IMUData] = None
        self.odometry_data: Optional[OdometryData] = None
        self.battery_data: Optional[BatteryData] = None
        
        # 心跳包
        self.robot_id = 1
        self.camera_status = 1
        self.last_heartbeat_time = 0
        self.heartbeat_interval = 1.0  # 1秒发送一次心跳包
        
        logger.info(f"STM32机器人控制器初始化 - 端口: {port}, 波特率: {baudrate}")
    
    def connect(self) -> bool:
        """
        连接到STM32控制板
        
        Returns:
            bool: 连接是否成功
        """
        try:
            self.serial_conn = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=self.timeout,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE
            )
            
            if self.serial_conn.is_open:
                self.connected = True
                self.running = True
                
                # 启动通信线程
                self.send_thread = threading.Thread(target=self._send_loop, daemon=True)
                self.recv_thread = threading.Thread(target=self._recv_loop, daemon=True)
                
                self.send_thread.start()
                self.recv_thread.start()
                
                logger.info("✅ 成功连接到STM32控制板")
                
                # 发送初始心跳包
                self.send_heartbeat()
                
                return True
            else:
                logger.error("❌ 串口打开失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        self.running = False
        
        if self.send_thread and self.send_thread.is_alive():
            self.send_thread.join(timeout=1.0)
        
        if self.recv_thread and self.recv_thread.is_alive():
            self.recv_thread.join(timeout=1.0)
        
        if self.serial_conn and self.serial_conn.is_open:
            self.serial_conn.close()
        
        self.connected = False
        logger.info("🔌 已断开STM32控制板连接")
    
    def _calculate_crc(self, data: bytes) -> int:
        """计算CRC校验码"""
        return crc16.crc16xmodem(data) & 0xFFFF
    
    def _build_frame(self, cmd: int, data: bytes = b'') -> bytes:
        """
        构建数据帧
        
        Args:
            cmd: 命令码
            data: 数据内容
            
        Returns:
            bytes: 完整的数据帧
        """
        frame = bytearray()
        frame.append(self.SEND_FRAME_HEAD)  # 帧头
        frame.append(self.SEND_FRAME_ADDR)  # 地址
        frame.append(cmd)                   # 命令码
        frame.append(len(data))             # 数据长度
        frame.extend(data)                  # 数据内容
        
        # 计算CRC校验码
        crc = self._calculate_crc(bytes(frame))
        frame.extend(struct.pack('<H', crc))  # 小端序CRC
        
        return bytes(frame)
    
    def _parse_frame(self, frame: bytes) -> Optional[Tuple[int, bytes]]:
        """
        解析接收到的数据帧
        
        Args:
            frame: 接收到的数据帧
            
        Returns:
            Optional[Tuple[int, bytes]]: (命令码, 数据) 或 None
        """
        if len(frame) < 6:  # 最小帧长度
            return None
        
        # 检查帧头和地址
        if frame[0] != self.RECV_FRAME_HEAD or frame[1] != self.RECV_FRAME_ADDR:
            return None
        
        cmd = frame[2]
        data_len = frame[3]
        
        if len(frame) < 4 + data_len + 2:  # 检查帧长度
            return None
        
        data = frame[4:4+data_len]
        received_crc = struct.unpack('<H', frame[4+data_len:4+data_len+2])[0]
        
        # 验证CRC
        calculated_crc = self._calculate_crc(frame[:4+data_len])
        if received_crc != calculated_crc:
            logger.warning(f"CRC校验失败: 接收={received_crc:04X}, 计算={calculated_crc:04X}")
            return None
        
        return cmd, data
    
    def _send_loop(self):
        """发送线程循环"""
        while self.running:
            try:
                # 检查心跳包
                current_time = time.time()
                if current_time - self.last_heartbeat_time > self.heartbeat_interval:
                    self.send_heartbeat()
                
                # 处理发送队列
                try:
                    frame = self.send_queue.get(timeout=0.1)
                    if self.serial_conn and self.serial_conn.is_open:
                        self.serial_conn.write(frame)
                        logger.debug(f"📤 发送数据: {frame.hex()}")
                    self.send_queue.task_done()
                except queue.Empty:
                    continue
                    
            except Exception as e:
                logger.error(f"发送线程错误: {e}")
                time.sleep(0.1)
    
    def _recv_loop(self):
        """接收线程循环"""
        buffer = bytearray()
        
        while self.running:
            try:
                if self.serial_conn and self.serial_conn.is_open:
                    data = self.serial_conn.read(self.serial_conn.in_waiting or 1)
                    if data:
                        buffer.extend(data)
                        
                        # 查找完整的帧
                        while len(buffer) >= 6:
                            # 查找帧头
                            start_idx = -1
                            for i in range(len(buffer) - 1):
                                if (buffer[i] == self.RECV_FRAME_HEAD and 
                                    buffer[i+1] == self.RECV_FRAME_ADDR):
                                    start_idx = i
                                    break
                            
                            if start_idx == -1:
                                buffer.clear()
                                break
                            
                            # 移除帧头前的数据
                            if start_idx > 0:
                                buffer = buffer[start_idx:]
                            
                            # 检查是否有完整帧
                            if len(buffer) < 4:
                                break
                            
                            data_len = buffer[3]
                            frame_len = 4 + data_len + 2
                            
                            if len(buffer) < frame_len:
                                break
                            
                            # 提取完整帧
                            frame = bytes(buffer[:frame_len])
                            buffer = buffer[frame_len:]
                            
                            # 解析帧
                            result = self._parse_frame(frame)
                            if result:
                                cmd, frame_data = result
                                self._handle_received_data(cmd, frame_data)
                                logger.debug(f"📥 接收数据: CMD={cmd:02X}, 长度={len(frame_data)}")
                
                time.sleep(0.001)  # 1ms延时
                
            except Exception as e:
                logger.error(f"接收线程错误: {e}")
                time.sleep(0.1)

    def _handle_received_data(self, cmd: int, data: bytes):
        """
        处理接收到的数据

        Args:
            cmd: 命令码
            data: 数据内容
        """
        try:
            if cmd == CommandCode.IMU_MSG:
                self._parse_imu_data(data)
            elif cmd == CommandCode.ODOMETRY_MSG:
                self._parse_odometry_data(data)
            elif cmd == CommandCode.BATTERY_MSG:
                self._parse_battery_data(data)
            elif cmd == CommandCode.GPS_MSG:
                logger.debug("收到GPS数据")
            else:
                logger.debug(f"收到未处理的命令: {cmd:02X}")

        except Exception as e:
            logger.error(f"处理接收数据错误: {e}")

    def _parse_imu_data(self, data: bytes):
        """解析IMU数据"""
        if len(data) >= 40:  # 10个float值
            values = struct.unpack('<10f', data[:40])
            self.imu_data = IMUData(
                accel_x=values[0], accel_y=values[1], accel_z=values[2],
                gyro_x=values[3], gyro_y=values[4], gyro_z=values[5],
                mag_x=values[6], mag_y=values[7], mag_z=values[8],
                temperature=values[9]
            )
            logger.debug("IMU数据已更新")

    def _parse_odometry_data(self, data: bytes):
        """解析里程计数据"""
        if len(data) >= 24:  # 6个float值
            values = struct.unpack('<6f', data[:24])
            self.odometry_data = OdometryData(
                vx=values[0], vy=values[1], wz=values[2],
                dis_x=values[3], dis_y=values[4], yaw_all=values[5]
            )
            logger.debug("里程计数据已更新")

    def _parse_battery_data(self, data: bytes):
        """解析电池数据"""
        if len(data) >= 16:  # 3个float + 1个int
            voltage, current, temperature = struct.unpack('<3f', data[:12])
            percentage = struct.unpack('<I', data[12:16])[0]
            self.battery_data = BatteryData(
                voltage=voltage, current=current,
                percentage=percentage, temperature=temperature
            )
            logger.debug("电池数据已更新")

    def _send_command(self, cmd: int, data: bytes = b''):
        """
        发送命令

        Args:
            cmd: 命令码
            data: 数据内容
        """
        if not self.connected:
            logger.warning("未连接到控制板，无法发送命令")
            return False

        try:
            frame = self._build_frame(cmd, data)
            self.send_queue.put(frame, timeout=0.1)
            return True
        except queue.Full:
            logger.warning("发送队列已满，命令被丢弃")
            return False
        except Exception as e:
            logger.error(f"发送命令失败: {e}")
            return False

    # ==================== 控制命令接口 ====================

    def set_control_mode(self, mode: ControlMode) -> bool:
        """
        设置控制模式

        Args:
            mode: 控制模式

        Returns:
            bool: 是否发送成功
        """
        data = struct.pack('<B', mode.value)
        success = self._send_command(CommandCode.MOVE_MODE_CMD, data)
        if success:
            logger.info(f"🎮 设置控制模式: {mode.name}")
        return success

    def move_robot(self, vx: float, vy: float, wz: float) -> bool:
        """
        控制机器人移动

        Args:
            vx: X方向速度 (m/s)
            vy: Y方向速度 (m/s)
            wz: 角速度 (rad/s)

        Returns:
            bool: 是否发送成功
        """
        data = struct.pack('<3f', vx, vy, wz)
        success = self._send_command(CommandCode.SPEED_CTL_CMD, data)
        if success:
            logger.info(f"🚗 机器人移动: vx={vx:.3f}, vy={vy:.3f}, wz={wz:.3f}")
        return success

    def stop_robot(self) -> bool:
        """
        停止机器人

        Returns:
            bool: 是否发送成功
        """
        return self.move_robot(0.0, 0.0, 0.0)

    def send_heartbeat(self) -> bool:
        """
        发送心跳包

        Returns:
            bool: 是否发送成功
        """
        data = struct.pack('<2B', self.robot_id, self.camera_status)
        success = self._send_command(CommandCode.HEART_MSG, data)
        if success:
            self.last_heartbeat_time = time.time()
            logger.debug(f"💓 发送心跳包: ID={self.robot_id}, CAM={self.camera_status}")
        return success

    def set_rgb_light(self, is_upper: bool, colors: List[Tuple[int, int, int, int]]) -> bool:
        """
        设置RGB灯带

        Args:
            is_upper: True为上灯带，False为下灯带
            colors: 颜色列表，每个元素为(R, G, B, 数量)

        Returns:
            bool: 是否发送成功
        """
        cmd = CommandCode.UP_RGB_CMD if is_upper else CommandCode.DOWN_RGB_CMD

        data = bytearray()
        for r, g, b, count in colors:
            data.extend([r, g, b, count])

        success = self._send_command(cmd, bytes(data))
        if success:
            light_type = "上" if is_upper else "下"
            logger.info(f"💡 设置{light_type}灯带: {len(colors)}个颜色组")
        return success

    # ==================== 高级控制方法 ====================

    def move_forward(self, speed: float = 0.25, duration: float = 1.0) -> bool:
        """前进"""
        if self.move_robot(speed, 0.0, 0.0):
            if duration > 0:
                time.sleep(duration)
                return self.stop_robot()
            return True
        return False

    def move_backward(self, speed: float = 0.25, duration: float = 1.0) -> bool:
        """后退"""
        if self.move_robot(-speed, 0.0, 0.0):
            if duration > 0:
                time.sleep(duration)
                return self.stop_robot()
            return True
        return False

    def turn_left(self, angular_speed: float = 1.0, duration: float = 0.5) -> bool:
        """左转"""
        if self.move_robot(0.0, 0.0, angular_speed):
            if duration > 0:
                time.sleep(duration)
                return self.stop_robot()
            return True
        return False

    def turn_right(self, angular_speed: float = 1.0, duration: float = 0.5) -> bool:
        """右转"""
        if self.move_robot(0.0, 0.0, -angular_speed):
            if duration > 0:
                time.sleep(duration)
                return self.stop_robot()
            return True
        return False

    def move_left(self, speed: float = 0.25, duration: float = 1.0) -> bool:
        """左移"""
        if self.move_robot(0.0, speed, 0.0):
            if duration > 0:
                time.sleep(duration)
                return self.stop_robot()
            return True
        return False

    def move_right(self, speed: float = 0.25, duration: float = 1.0) -> bool:
        """右移"""
        if self.move_robot(0.0, -speed, 0.0):
            if duration > 0:
                time.sleep(duration)
                return self.stop_robot()
            return True
        return False

    def move_diagonal(self, vx: float, vy: float, duration: float = 1.0) -> bool:
        """斜向移动"""
        if self.move_robot(vx, vy, 0.0):
            if duration > 0:
                time.sleep(duration)
                return self.stop_robot()
            return True
        return False

    # ==================== 数据获取接口 ====================

    def get_imu_data(self) -> Optional[IMUData]:
        """获取IMU数据"""
        return self.imu_data

    def get_odometry_data(self) -> Optional[OdometryData]:
        """获取里程计数据"""
        return self.odometry_data

    def get_battery_data(self) -> Optional[BatteryData]:
        """获取电池数据"""
        return self.battery_data

    def get_robot_status(self) -> Dict[str, Any]:
        """获取机器人状态"""
        return {
            'connected': self.connected,
            'robot_id': self.robot_id,
            'camera_status': self.camera_status,
            'last_heartbeat': self.last_heartbeat_time,
            'send_queue_size': self.send_queue.qsize(),
            'recv_queue_size': self.recv_queue.qsize(),
            'imu_available': self.imu_data is not None,
            'odometry_available': self.odometry_data is not None,
            'battery_available': self.battery_data is not None
        }

    # ==================== 工具方法 ====================

    def wait_for_data(self, data_type: str, timeout: float = 5.0) -> bool:
        """
        等待特定类型的数据

        Args:
            data_type: 数据类型 ('imu', 'odometry', 'battery')
            timeout: 超时时间

        Returns:
            bool: 是否在超时前收到数据
        """
        start_time = time.time()

        while time.time() - start_time < timeout:
            if data_type == 'imu' and self.imu_data is not None:
                return True
            elif data_type == 'odometry' and self.odometry_data is not None:
                return True
            elif data_type == 'battery' and self.battery_data is not None:
                return True

            time.sleep(0.1)

        return False

    def clear_data(self):
        """清除所有传感器数据"""
        self.imu_data = None
        self.odometry_data = None
        self.battery_data = None
        logger.info("🧹 已清除所有传感器数据")

    def __enter__(self):
        """上下文管理器入口"""
        if not self.connect():
            raise ConnectionError("无法连接到STM32控制板")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()

    def __del__(self):
        """析构函数"""
        if hasattr(self, 'connected') and self.connected:
            self.disconnect()


# ==================== 便捷函数 ====================

def find_robot_ports() -> List[str]:
    """
    查找可能的机器人串口设备

    Returns:
        List[str]: 可能的串口设备列表
    """
    import glob

    possible_ports = []

    # Linux/Unix系统
    possible_ports.extend(glob.glob('/dev/ttyUSB*'))
    possible_ports.extend(glob.glob('/dev/ttyACM*'))
    possible_ports.extend(glob.glob('/dev/ttyS*'))

    # macOS系统
    possible_ports.extend(glob.glob('/dev/tty.usb*'))
    possible_ports.extend(glob.glob('/dev/cu.usb*'))

    return sorted(possible_ports)


def auto_connect_robot(baudrate: int = 921600, timeout: float = 2.0) -> Optional[STM32RobotController]:
    """
    自动连接机器人控制板

    Args:
        baudrate: 波特率
        timeout: 连接超时时间

    Returns:
        Optional[STM32RobotController]: 成功连接的控制器实例或None
    """
    ports = find_robot_ports()

    if not ports:
        logger.warning("未找到可用的串口设备")
        return None

    for port in ports:
        logger.info(f"尝试连接: {port}")
        try:
            controller = STM32RobotController(port=port, baudrate=baudrate, timeout=timeout)
            if controller.connect():
                logger.info(f"✅ 成功连接到: {port}")
                return controller
            else:
                controller.disconnect()
        except Exception as e:
            logger.debug(f"连接 {port} 失败: {e}")

    logger.error("❌ 无法连接到任何机器人控制板")
    return None


if __name__ == '__main__':
    # 测试代码
    print("🚀 STM32机器人控制器测试")

    # 查找可用端口
    ports = find_robot_ports()
    print(f"发现端口: {ports}")

    # 尝试自动连接
    robot = auto_connect_robot()

    if robot:
        try:
            print("📋 机器人状态:", robot.get_robot_status())

            # 设置控制模式
            robot.set_control_mode(ControlMode.MOVE_CONTROL)
            time.sleep(0.5)

            # 测试基本移动
            print("🧪 测试基本移动...")
            robot.move_forward(0.2, 1.0)
            time.sleep(0.5)

            robot.turn_left(1.0, 0.5)
            time.sleep(0.5)

            robot.stop_robot()

            # 等待传感器数据
            print("📊 等待传感器数据...")
            if robot.wait_for_data('odometry', 3.0):
                odometry = robot.get_odometry_data()
                print(f"里程计数据: {odometry}")

            if robot.wait_for_data('battery', 3.0):
                battery = robot.get_battery_data()
                print(f"电池数据: {battery}")

        finally:
            robot.disconnect()
    else:
        print("❌ 无法连接到机器人控制板")
