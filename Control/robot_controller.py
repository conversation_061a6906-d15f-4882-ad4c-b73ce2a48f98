#!/usr/bin/env python3
"""
机器人控制模块 - 解耦的控制执行器
基于stick_control.py的逻辑，提供10种基本行为控制

Copyright (c) 2024 WindyLab of Westlake University, China
All rights reserved.
"""

import json
import time
import threading
import rospy
import os
from paho.mqtt import client as mqtt_client
from collections import deque
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class RobotController:
    """
    机器人控制器 - 接收视觉模块命令并执行相应的控制动作
    """
    
    def __init__(self, entity_id=7, enable_safety=True):
        """
        初始化机器人控制器
        
        Args:
            entity_id (int): 机器人实体ID
            enable_safety (bool): 是否启用安全检查
        """
        self.entity_id = entity_id
        self.enable_safety = enable_safety
        
        # 控制状态
        self.current_velocity = {'x': 0, 'y': 0, 'theta': 0}
        self.is_moving = False
        self.last_command_time = time.time()
        self.command_timeout = 2.0  # 命令超时时间（秒）
        
        # 命令队列和历史
        self.command_queue = deque(maxlen=100)
        self.command_history = deque(maxlen=1000)
        
        # 安全限制
        self.max_linear_speed = 0.5  # 最大线速度
        self.max_angular_speed = 3.0  # 最大角速度
        self.emergency_stop = False
        
        # 初始化ROS节点
        try:
            rospy.init_node("robot_controller", anonymous=True)
        except rospy.exceptions.ROSException:
            logger.warning("ROS节点已存在，跳过初始化")
        
        # 初始化MQTT客户端
        self.mqtt_client = self.setup_mqtt_client()
        
        # 启动控制循环线程
        self.control_thread = threading.Thread(target=self.control_loop, daemon=True)
        self.control_thread.start()
        
        logger.info(f"机器人控制器初始化完成 - Entity ID: {entity_id}")
    
    def setup_mqtt_client(self):
        """设置MQTT客户端"""
        broker_ip = "*********"
        port = 1883
        keepalive = 60
        client_id = f"RobotController_{self.entity_id}"
        
        try:
            broker = os.environ.get("REMOTE_SERVER", broker_ip)
        except KeyError:
            broker = broker_ip
        
        # 检查网络连接
        net_status = -1
        retry_count = 0
        max_retries = 5
        
        while net_status != 0 and retry_count < max_retries:
            net_status = os.system(f"ping -c 2 {broker} > /dev/null 2>&1")
            if net_status != 0:
                retry_count += 1
                logger.warning(f"网络连接失败，重试 {retry_count}/{max_retries}")
                time.sleep(2)
        
        if net_status != 0:
            logger.error("无法连接到MQTT服务器")
            return None
        
        mqtt_client_instance = MqttClientThread(
            broker=broker, 
            port=port, 
            keepalive=keepalive, 
            client_id=client_id,
            message_callback=self.handle_vision_command
        )
        
        # 订阅视觉模块的命令主题
        command_topic = f"/VSWARM{self.entity_id}_robot/vision_command"
        mqtt_client_instance.subscribe(command_topic)
        
        return mqtt_client_instance
    
    def handle_vision_command(self, topic, payload):
        """
        处理来自视觉模块的命令
        
        Args:
            topic (str): MQTT主题
            payload (bytes): 消息载荷
        """
        try:
            command_data = json.loads(payload.decode('utf-8'))
            logger.info(f"收到视觉命令: {command_data}")
            
            # 验证命令格式
            if not self.validate_command(command_data):
                logger.warning("无效的命令格式")
                return
            
            # 添加到命令队列
            command_data['received_time'] = time.time()
            self.command_queue.append(command_data)
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
        except Exception as e:
            logger.error(f"处理视觉命令时出错: {e}")
    
    def validate_command(self, command_data):
        """验证命令格式"""
        required_fields = ['command', 'parameters']
        return all(field in command_data for field in required_fields)
    
    def control_loop(self):
        """主控制循环"""
        rate = rospy.Rate(50)  # 50Hz控制频率
        
        while not rospy.is_shutdown():
            try:
                # 处理命令队列
                if self.command_queue:
                    command = self.command_queue.popleft()
                    self.execute_command(command)
                
                # 安全检查
                if self.enable_safety:
                    self.safety_check()
                
                # 超时检查
                self.timeout_check()
                
                rate.sleep()
                
            except Exception as e:
                logger.error(f"控制循环错误: {e}")
                time.sleep(0.1)
    
    def execute_command(self, command_data):
        """
        执行控制命令
        
        Args:
            command_data (dict): 命令数据
        """
        command = command_data.get('command')
        parameters = command_data.get('parameters', {})
        
        # 记录命令历史
        self.command_history.append({
            'command': command,
            'parameters': parameters,
            'timestamp': time.time()
        })
        
        # 执行对应的行为
        if command == 'move_forward':
            self.move_forward(parameters)
        elif command == 'move_backward':
            self.move_backward(parameters)
        elif command == 'turn_left':
            self.turn_left(parameters)
        elif command == 'turn_right':
            self.turn_right(parameters)
        elif command == 'move_left':
            self.move_left(parameters)
        elif command == 'move_right':
            self.move_right(parameters)
        elif command == 'move_forward_left':
            self.move_forward_left(parameters)
        elif command == 'move_forward_right':
            self.move_forward_right(parameters)
        elif command == 'move_backward_left':
            self.move_backward_left(parameters)
        elif command == 'move_backward_right':
            self.move_backward_right(parameters)
        elif command == 'stop':
            self.stop(parameters)
        elif command == 'emergency_stop':
            self.emergency_stop_action()
        else:
            logger.warning(f"未知命令: {command}")
    
    # 10种基本行为实现
    def move_forward(self, params):
        """前进"""
        speed = params.get('speed', 0.25)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=speed, y=0, theta=0, duration=duration)
        logger.info(f"执行前进 - 速度: {speed}, 持续时间: {duration}s")
    
    def move_backward(self, params):
        """后退"""
        speed = params.get('speed', 0.25)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=-speed, y=0, theta=0, duration=duration)
        logger.info(f"执行后退 - 速度: {speed}, 持续时间: {duration}s")
    
    def turn_left(self, params):
        """左转"""
        angular_speed = params.get('angular_speed', 1.0)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=0, y=0, theta=angular_speed, duration=duration)
        logger.info(f"执行左转 - 角速度: {angular_speed}, 持续时间: {duration}s")
    
    def turn_right(self, params):
        """右转"""
        angular_speed = params.get('angular_speed', 1.0)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=0, y=0, theta=-angular_speed, duration=duration)
        logger.info(f"执行右转 - 角速度: {angular_speed}, 持续时间: {duration}s")
    
    def move_left(self, params):
        """左移"""
        speed = params.get('speed', 0.25)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=0, y=speed, theta=0, duration=duration)
        logger.info(f"执行左移 - 速度: {speed}, 持续时间: {duration}s")
    
    def move_right(self, params):
        """右移"""
        speed = params.get('speed', 0.25)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=0, y=-speed, theta=0, duration=duration)
        logger.info(f"执行右移 - 速度: {speed}, 持续时间: {duration}s")
    
    def move_forward_left(self, params):
        """左前方移动"""
        speed = params.get('speed', 0.25)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=speed*0.7, y=speed*0.7, theta=0, duration=duration)
        logger.info(f"执行左前移动 - 速度: {speed}, 持续时间: {duration}s")
    
    def move_forward_right(self, params):
        """右前方移动"""
        speed = params.get('speed', 0.25)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=speed*0.7, y=-speed*0.7, theta=0, duration=duration)
        logger.info(f"执行右前移动 - 速度: {speed}, 持续时间: {duration}s")
    
    def move_backward_left(self, params):
        """左后方移动"""
        speed = params.get('speed', 0.25)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=-speed*0.7, y=speed*0.7, theta=0, duration=duration)
        logger.info(f"执行左后移动 - 速度: {speed}, 持续时间: {duration}s")
    
    def move_backward_right(self, params):
        """右后方移动"""
        speed = params.get('speed', 0.25)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=-speed*0.7, y=-speed*0.7, theta=0, duration=duration)
        logger.info(f"执行右后移动 - 速度: {speed}, 持续时间: {duration}s")
    
    def stop(self, params):
        """停止"""
        self.set_velocity(x=0, y=0, theta=0, duration=0.1)
        logger.info("执行停止")
    
    def emergency_stop_action(self):
        """紧急停止"""
        self.emergency_stop = True
        self.set_velocity(x=0, y=0, theta=0, duration=0.0)
        logger.warning("执行紧急停止")
