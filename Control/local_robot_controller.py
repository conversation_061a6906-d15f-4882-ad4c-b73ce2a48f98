#!/usr/bin/env python3
"""
本地机器人控制模块 - 简化版本，适用于本地机器
基于stick_control.py的逻辑，提供10种基本行为控制

Copyright (c) 2024 WindyLab of Westlake University, China
All rights reserved.
"""

import json
import time
import threading
import queue
import logging
from collections import deque
from datetime import datetime
from typing import Dict, Any, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class LocalRobotController:
    """
    本地机器人控制器 - 使用队列进行进程间通信
    """
    
    def __init__(self, enable_safety=True):
        """
        初始化本地机器人控制器
        
        Args:
            enable_safety (bool): 是否启用安全检查
        """
        self.enable_safety = enable_safety
        
        # 控制状态
        self.current_velocity = {'x': 0, 'y': 0, 'theta': 0}
        self.is_moving = False
        self.last_command_time = time.time()
        self.command_timeout = 2.0  # 命令超时时间（秒）
        
        # 命令队列 - 线程安全
        self.command_queue = queue.Queue(maxsize=100)
        self.command_history = deque(maxlen=1000)
        
        # 安全限制
        self.max_linear_speed = 0.5  # 最大线速度
        self.max_angular_speed = 3.0  # 最大角速度
        self.emergency_stop = False
        
        # 控制线程
        self.running = True
        self.control_thread = threading.Thread(target=self.control_loop, daemon=True)
        self.control_thread.start()
        
        logger.info("本地机器人控制器初始化完成")
    
    def send_command(self, command: str, parameters: Dict[str, Any] = None):
        """
        发送控制命令（外部接口）
        
        Args:
            command (str): 命令名称
            parameters (dict): 命令参数
        """
        if parameters is None:
            parameters = {}
            
        command_data = {
            'command': command,
            'parameters': parameters,
            'timestamp': time.time(),
            'source': 'vision_module'
        }
        
        try:
            self.command_queue.put(command_data, timeout=0.1)
            logger.info(f"命令已发送: {command}")
        except queue.Full:
            logger.warning("命令队列已满，丢弃命令")
    
    def control_loop(self):
        """主控制循环"""
        while self.running:
            try:
                # 等待命令，超时时间0.1秒
                try:
                    command_data = self.command_queue.get(timeout=0.1)
                    self.execute_command(command_data)
                    self.command_queue.task_done()
                except queue.Empty:
                    pass  # 没有命令时继续循环
                
                # 安全检查
                if self.enable_safety:
                    self.safety_check()
                
                # 超时检查
                self.timeout_check()
                
                time.sleep(0.02)  # 50Hz控制频率
                
            except Exception as e:
                logger.error(f"控制循环错误: {e}")
                time.sleep(0.1)
    
    def execute_command(self, command_data: Dict[str, Any]):
        """
        执行控制命令
        
        Args:
            command_data (dict): 命令数据
        """
        command = command_data.get('command')
        parameters = command_data.get('parameters', {})
        
        # 记录命令历史
        self.command_history.append({
            'command': command,
            'parameters': parameters,
            'timestamp': time.time()
        })
        
        # 执行对应的行为
        if command == 'move_forward':
            self.move_forward(parameters)
        elif command == 'move_backward':
            self.move_backward(parameters)
        elif command == 'turn_left':
            self.turn_left(parameters)
        elif command == 'turn_right':
            self.turn_right(parameters)
        elif command == 'move_left':
            self.move_left(parameters)
        elif command == 'move_right':
            self.move_right(parameters)
        elif command == 'move_forward_left':
            self.move_forward_left(parameters)
        elif command == 'move_forward_right':
            self.move_forward_right(parameters)
        elif command == 'move_backward_left':
            self.move_backward_left(parameters)
        elif command == 'move_backward_right':
            self.move_backward_right(parameters)
        elif command == 'stop':
            self.stop(parameters)
        elif command == 'emergency_stop':
            self.emergency_stop_action()
        else:
            logger.warning(f"未知命令: {command}")
    
    # 10种基本行为实现
    def move_forward(self, params: Dict[str, Any]):
        """前进"""
        speed = params.get('speed', 0.25)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=speed, y=0, theta=0, duration=duration)
        logger.info(f"执行前进 - 速度: {speed}, 持续时间: {duration}s")
    
    def move_backward(self, params: Dict[str, Any]):
        """后退"""
        speed = params.get('speed', 0.25)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=-speed, y=0, theta=0, duration=duration)
        logger.info(f"执行后退 - 速度: {speed}, 持续时间: {duration}s")
    
    def turn_left(self, params: Dict[str, Any]):
        """左转"""
        angular_speed = params.get('angular_speed', 1.0)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=0, y=0, theta=angular_speed, duration=duration)
        logger.info(f"执行左转 - 角速度: {angular_speed}, 持续时间: {duration}s")
    
    def turn_right(self, params: Dict[str, Any]):
        """右转"""
        angular_speed = params.get('angular_speed', 1.0)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=0, y=0, theta=-angular_speed, duration=duration)
        logger.info(f"执行右转 - 角速度: {angular_speed}, 持续时间: {duration}s")
    
    def move_left(self, params: Dict[str, Any]):
        """左移"""
        speed = params.get('speed', 0.25)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=0, y=speed, theta=0, duration=duration)
        logger.info(f"执行左移 - 速度: {speed}, 持续时间: {duration}s")
    
    def move_right(self, params: Dict[str, Any]):
        """右移"""
        speed = params.get('speed', 0.25)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=0, y=-speed, theta=0, duration=duration)
        logger.info(f"执行右移 - 速度: {speed}, 持续时间: {duration}s")
    
    def move_forward_left(self, params: Dict[str, Any]):
        """左前方移动"""
        speed = params.get('speed', 0.25)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=speed*0.7, y=speed*0.7, theta=0, duration=duration)
        logger.info(f"执行左前移动 - 速度: {speed}, 持续时间: {duration}s")
    
    def move_forward_right(self, params: Dict[str, Any]):
        """右前方移动"""
        speed = params.get('speed', 0.25)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=speed*0.7, y=-speed*0.7, theta=0, duration=duration)
        logger.info(f"执行右前移动 - 速度: {speed}, 持续时间: {duration}s")
    
    def move_backward_left(self, params: Dict[str, Any]):
        """左后方移动"""
        speed = params.get('speed', 0.25)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=-speed*0.7, y=speed*0.7, theta=0, duration=duration)
        logger.info(f"执行左后移动 - 速度: {speed}, 持续时间: {duration}s")
    
    def move_backward_right(self, params: Dict[str, Any]):
        """右后方移动"""
        speed = params.get('speed', 0.25)
        duration = params.get('duration', 0.5)
        self.set_velocity(x=-speed*0.7, y=-speed*0.7, theta=0, duration=duration)
        logger.info(f"执行右后移动 - 速度: {speed}, 持续时间: {duration}s")
    
    def stop(self, params: Dict[str, Any]):
        """停止"""
        self.set_velocity(x=0, y=0, theta=0, duration=0.1)
        logger.info("执行停止")
    
    def emergency_stop_action(self):
        """紧急停止"""
        self.emergency_stop = True
        self.set_velocity(x=0, y=0, theta=0, duration=0.0)
        logger.warning("执行紧急停止")
    
    def set_velocity(self, x: float, y: float, theta: float, duration: float):
        """
        设置机器人速度
        
        Args:
            x (float): X方向速度
            y (float): Y方向速度  
            theta (float): 角速度
            duration (float): 持续时间
        """
        # 安全限制
        if self.enable_safety:
            x = max(-self.max_linear_speed, min(self.max_linear_speed, x))
            y = max(-self.max_linear_speed, min(self.max_linear_speed, y))
            theta = max(-self.max_angular_speed, min(self.max_angular_speed, theta))
        
        # 更新当前速度
        self.current_velocity = {'x': x, 'y': y, 'theta': theta}
        self.last_command_time = time.time()
        self.is_moving = (x != 0 or y != 0 or theta != 0)
        
        # 这里应该是实际的硬件控制代码
        # 目前只是打印，你需要根据实际硬件接口来实现
        print(f"🚗 设置速度: x={x:.3f}, y={y:.3f}, theta={theta:.3f}, 持续时间={duration:.3f}s")
        
        # 模拟执行时间
        if duration > 0:
            time.sleep(duration)
            # 执行完成后停止
            self.current_velocity = {'x': 0, 'y': 0, 'theta': 0}
            self.is_moving = False
    
    def safety_check(self):
        """安全检查"""
        if self.emergency_stop:
            self.current_velocity = {'x': 0, 'y': 0, 'theta': 0}
            self.is_moving = False
            return
    
    def timeout_check(self):
        """超时检查"""
        if self.is_moving and (time.time() - self.last_command_time) > self.command_timeout:
            logger.warning("命令超时，执行停止")
            self.stop({})
    
    def get_status(self) -> Dict[str, Any]:
        """获取控制器状态"""
        return {
            'current_velocity': self.current_velocity,
            'is_moving': self.is_moving,
            'emergency_stop': self.emergency_stop,
            'queue_size': self.command_queue.qsize(),
            'last_command_time': self.last_command_time
        }
    
    def shutdown(self):
        """关闭控制器"""
        self.running = False
        self.emergency_stop_action()
        if self.control_thread.is_alive():
            self.control_thread.join(timeout=1.0)
        logger.info("机器人控制器已关闭")


# 全局控制器实例
_controller_instance: Optional[LocalRobotController] = None

def get_controller() -> LocalRobotController:
    """获取全局控制器实例（单例模式）"""
    global _controller_instance
    if _controller_instance is None:
        _controller_instance = LocalRobotController()
    return _controller_instance


if __name__ == '__main__':
    # 测试代码
    controller = LocalRobotController()
    
    try:
        # 测试各种命令
        print("测试前进...")
        controller.send_command('move_forward', {'speed': 0.3, 'duration': 1.0})
        time.sleep(2)
        
        print("测试左转...")
        controller.send_command('turn_left', {'angular_speed': 1.5, 'duration': 0.5})
        time.sleep(2)
        
        print("测试停止...")
        controller.send_command('stop')
        time.sleep(1)
        
        print("状态:", controller.get_status())
        
    except KeyboardInterrupt:
        print("用户中断")
    finally:
        controller.shutdown()
