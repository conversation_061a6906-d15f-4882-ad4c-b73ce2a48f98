#!/usr/bin/env python3
"""
视觉机器人控制器 - 简化版本

专门为视觉控制系统设计的简化控制接口
兼容本地控制器和STM32控制板

Copyright (c) 2024 WindyLab of Westlake University, China
All rights reserved.
"""

import time
import logging
from typing import Optional, Dict, Any
from abc import ABC, abstractmethod

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class BaseRobotController(ABC):
    """机器人控制器基类"""
    
    @abstractmethod
    def move_forward(self, speed: float = 0.25, duration: float = 1.0) -> bool:
        """前进"""
        pass
    
    @abstractmethod
    def move_backward(self, speed: float = 0.25, duration: float = 1.0) -> bool:
        """后退"""
        pass
    
    @abstractmethod
    def turn_left(self, angular_speed: float = 1.0, duration: float = 0.5) -> bool:
        """左转"""
        pass
    
    @abstractmethod
    def turn_right(self, angular_speed: float = 1.0, duration: float = 0.5) -> bool:
        """右转"""
        pass
    
    @abstractmethod
    def stop(self) -> bool:
        """停止"""
        pass
    
    @abstractmethod
    def get_status(self) -> Dict[str, Any]:
        """获取状态"""
        pass


class VisionRobotController(BaseRobotController):
    """
    视觉机器人控制器
    
    自动检测并使用可用的控制器（本地模拟器或STM32控制板）
    """
    
    def __init__(self, prefer_hardware: bool = True, auto_connect: bool = True):
        """
        初始化视觉机器人控制器
        
        Args:
            prefer_hardware: 是否优先使用硬件控制器
            auto_connect: 是否自动连接
        """
        self.prefer_hardware = prefer_hardware
        self.controller: Optional[BaseRobotController] = None
        self.controller_type = "none"
        
        if auto_connect:
            self.connect()
    
    def connect(self) -> bool:
        """连接到控制器"""
        if self.prefer_hardware:
            # 首先尝试连接STM32控制板
            if self._try_connect_stm32():
                return True
            # 如果失败，回退到本地控制器
            return self._try_connect_local()
        else:
            # 首先尝试本地控制器
            if self._try_connect_local():
                return True
            # 如果失败，尝试STM32控制板
            return self._try_connect_stm32()
    
    def _try_connect_stm32(self) -> bool:
        """尝试连接STM32控制板"""
        try:
            from stm32_robot_controller import auto_connect_robot
            
            stm32_controller = auto_connect_robot(timeout=2.0)
            if stm32_controller:
                self.controller = STM32ControllerWrapper(stm32_controller)
                self.controller_type = "stm32"
                logger.info("✅ 已连接到STM32控制板")
                return True
        except ImportError:
            logger.debug("STM32控制器模块不可用")
        except Exception as e:
            logger.debug(f"STM32控制器连接失败: {e}")
        
        return False
    
    def _try_connect_local(self) -> bool:
        """尝试连接本地控制器"""
        try:
            from local_robot_controller import get_controller
            
            local_controller = get_controller()
            self.controller = LocalControllerWrapper(local_controller)
            self.controller_type = "local"
            logger.info("✅ 已连接到本地控制器")
            return True
        except ImportError:
            logger.debug("本地控制器模块不可用")
        except Exception as e:
            logger.debug(f"本地控制器连接失败: {e}")
        
        return False
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.controller is not None
    
    def get_controller_type(self) -> str:
        """获取控制器类型"""
        return self.controller_type
    
    # 实现基类方法
    def move_forward(self, speed: float = 0.25, duration: float = 1.0) -> bool:
        if self.controller:
            return self.controller.move_forward(speed, duration)
        logger.warning("❌ 控制器未连接")
        return False
    
    def move_backward(self, speed: float = 0.25, duration: float = 1.0) -> bool:
        if self.controller:
            return self.controller.move_backward(speed, duration)
        logger.warning("❌ 控制器未连接")
        return False
    
    def turn_left(self, angular_speed: float = 1.0, duration: float = 0.5) -> bool:
        if self.controller:
            return self.controller.turn_left(angular_speed, duration)
        logger.warning("❌ 控制器未连接")
        return False
    
    def turn_right(self, angular_speed: float = 1.0, duration: float = 0.5) -> bool:
        if self.controller:
            return self.controller.turn_right(angular_speed, duration)
        logger.warning("❌ 控制器未连接")
        return False
    
    def stop(self) -> bool:
        if self.controller:
            return self.controller.stop()
        logger.warning("❌ 控制器未连接")
        return False
    
    def get_status(self) -> Dict[str, Any]:
        if self.controller:
            status = self.controller.get_status()
            status['controller_type'] = self.controller_type
            return status
        return {'controller_type': 'none', 'connected': False}
    
    # 高级控制方法
    def move_left(self, speed: float = 0.25, duration: float = 1.0) -> bool:
        """左移（如果控制器支持）"""
        if hasattr(self.controller, 'move_left'):
            return self.controller.move_left(speed, duration)
        else:
            # 对于不支持侧移的控制器，使用转向+前进的组合
            logger.info("控制器不支持侧移，使用转向+前进组合")
            return (self.turn_left(1.0, 0.3) and 
                   self.move_forward(speed, duration) and 
                   self.turn_right(1.0, 0.3))
    
    def move_right(self, speed: float = 0.25, duration: float = 1.0) -> bool:
        """右移（如果控制器支持）"""
        if hasattr(self.controller, 'move_right'):
            return self.controller.move_right(speed, duration)
        else:
            # 对于不支持侧移的控制器，使用转向+前进的组合
            logger.info("控制器不支持侧移，使用转向+前进组合")
            return (self.turn_right(1.0, 0.3) and 
                   self.move_forward(speed, duration) and 
                   self.turn_left(1.0, 0.3))
    
    def move_forward_left(self, speed: float = 0.25, duration: float = 0.8) -> bool:
        """左前方移动"""
        if hasattr(self.controller, 'move_diagonal'):
            return self.controller.move_diagonal(speed*0.7, speed*0.7, duration)
        else:
            # 组合动作
            return (self.turn_left(0.5, 0.2) and 
                   self.move_forward(speed, duration))
    
    def move_forward_right(self, speed: float = 0.25, duration: float = 0.8) -> bool:
        """右前方移动"""
        if hasattr(self.controller, 'move_diagonal'):
            return self.controller.move_diagonal(speed*0.7, -speed*0.7, duration)
        else:
            # 组合动作
            return (self.turn_right(0.5, 0.2) and 
                   self.move_forward(speed, duration))
    
    def send_command(self, command: str, parameters: Dict[str, Any] = None):
        """
        发送通用命令（兼容之前的接口）
        
        Args:
            command: 命令名称
            parameters: 命令参数
        """
        if parameters is None:
            parameters = {}
        
        speed = parameters.get('speed', 0.25)
        duration = parameters.get('duration', 1.0)
        angular_speed = parameters.get('angular_speed', 1.0)
        
        if command == 'move_forward':
            return self.move_forward(speed, duration)
        elif command == 'move_backward':
            return self.move_backward(speed, duration)
        elif command == 'turn_left':
            return self.turn_left(angular_speed, duration)
        elif command == 'turn_right':
            return self.turn_right(angular_speed, duration)
        elif command == 'move_left':
            return self.move_left(speed, duration)
        elif command == 'move_right':
            return self.move_right(speed, duration)
        elif command == 'move_forward_left':
            return self.move_forward_left(speed, duration)
        elif command == 'move_forward_right':
            return self.move_forward_right(speed, duration)
        elif command == 'stop':
            return self.stop()
        elif command == 'emergency_stop':
            return self.stop()
        else:
            logger.warning(f"未知命令: {command}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.controller and hasattr(self.controller, 'disconnect'):
            self.controller.disconnect()
        self.controller = None
        self.controller_type = "none"
        logger.info("🔌 已断开控制器连接")
    
    def __enter__(self):
        """上下文管理器入口"""
        if not self.is_connected():
            if not self.connect():
                raise ConnectionError("无法连接到任何控制器")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()


class STM32ControllerWrapper(BaseRobotController):
    """STM32控制器包装器"""
    
    def __init__(self, stm32_controller):
        self.stm32_controller = stm32_controller
    
    def move_forward(self, speed: float = 0.25, duration: float = 1.0) -> bool:
        return self.stm32_controller.move_forward(speed, duration)
    
    def move_backward(self, speed: float = 0.25, duration: float = 1.0) -> bool:
        return self.stm32_controller.move_backward(speed, duration)
    
    def turn_left(self, angular_speed: float = 1.0, duration: float = 0.5) -> bool:
        return self.stm32_controller.turn_left(angular_speed, duration)
    
    def turn_right(self, angular_speed: float = 1.0, duration: float = 0.5) -> bool:
        return self.stm32_controller.turn_right(angular_speed, duration)
    
    def stop(self) -> bool:
        return self.stm32_controller.stop_robot()
    
    def get_status(self) -> Dict[str, Any]:
        return self.stm32_controller.get_robot_status()
    
    def move_left(self, speed: float = 0.25, duration: float = 1.0) -> bool:
        return self.stm32_controller.move_left(speed, duration)
    
    def move_right(self, speed: float = 0.25, duration: float = 1.0) -> bool:
        return self.stm32_controller.move_right(speed, duration)
    
    def move_diagonal(self, vx: float, vy: float, duration: float = 1.0) -> bool:
        return self.stm32_controller.move_diagonal(vx, vy, duration)
    
    def disconnect(self):
        self.stm32_controller.disconnect()


class LocalControllerWrapper(BaseRobotController):
    """本地控制器包装器"""
    
    def __init__(self, local_controller):
        self.local_controller = local_controller
    
    def move_forward(self, speed: float = 0.25, duration: float = 1.0) -> bool:
        self.local_controller.send_command('move_forward', {'speed': speed, 'duration': duration})
        return True
    
    def move_backward(self, speed: float = 0.25, duration: float = 1.0) -> bool:
        self.local_controller.send_command('move_backward', {'speed': speed, 'duration': duration})
        return True
    
    def turn_left(self, angular_speed: float = 1.0, duration: float = 0.5) -> bool:
        self.local_controller.send_command('turn_left', {'angular_speed': angular_speed, 'duration': duration})
        return True
    
    def turn_right(self, angular_speed: float = 1.0, duration: float = 0.5) -> bool:
        self.local_controller.send_command('turn_right', {'angular_speed': angular_speed, 'duration': duration})
        return True
    
    def stop(self) -> bool:
        self.local_controller.send_command('stop')
        return True
    
    def get_status(self) -> Dict[str, Any]:
        return self.local_controller.get_status()


# 全局控制器实例
_vision_controller_instance: Optional[VisionRobotController] = None

def get_vision_controller(prefer_hardware: bool = True) -> VisionRobotController:
    """获取全局视觉控制器实例（单例模式）"""
    global _vision_controller_instance
    if _vision_controller_instance is None:
        _vision_controller_instance = VisionRobotController(prefer_hardware=prefer_hardware)
    return _vision_controller_instance


if __name__ == '__main__':
    # 测试代码
    print("🚀 视觉机器人控制器测试")
    
    with VisionRobotController() as robot:
        print(f"📋 控制器类型: {robot.get_controller_type()}")
        print(f"📊 状态: {robot.get_status()}")
        
        # 测试基本移动
        print("🧪 测试基本移动...")
        robot.move_forward(0.2, 0.5)
        time.sleep(0.5)
        
        robot.turn_left(1.0, 0.3)
        time.sleep(0.5)
        
        robot.move_forward_right(0.2, 0.5)
        time.sleep(0.5)
        
        robot.stop()
        
        print("✅ 测试完成")
