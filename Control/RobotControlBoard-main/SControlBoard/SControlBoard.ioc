#MicroXplorer Configuration settings - do not modify
ADC1.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_4
ADC1.ClockPrescaler=ADC_CLOCK_ASYNC_DIV2
ADC1.ContinuousConvMode=ENABLE
ADC1.ConversionDataManagement=ADC_CONVERSIONDATA_DMA_CIRCULAR
ADC1.EnableRegularConversion=ENABLE
ADC1.IPParameters=master,Overrun,ClockPrescaler,Resolution,OversamplingMode,ContinuousConvMode,EnableRegularConversion,ConversionDataManagement,Rank-2\#ChannelRegularConversion,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion,OffsetNumber-2\#ChannelRegularConversion,OffsetSignedSaturation-2\#ChannelRegularConversion,NbrOfConversionFlag
ADC1.NbrOfConversionFlag=1
ADC1.OffsetNumber-2\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.OffsetSignedSaturation-2\#ChannelRegularConversion=DISABLE
ADC1.Overrun=ADC_OVR_DATA_OVERWRITTEN
ADC1.OversamplingMode=DISABLE
ADC1.Rank-2\#ChannelRegularConversion=1
ADC1.Resolution=ADC_RESOLUTION_16B
ADC1.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_64CYCLES_5
ADC1.master=1
Bdma1.MEMTOMEM.0.Direction=DMA_MEMORY_TO_MEMORY
Bdma1.MEMTOMEM.0.Instance=BDMA1_Channel0
Bdma1.MEMTOMEM.0.MemDataAlignment=DMA_MDATAALIGN_WORD
Bdma1.MEMTOMEM.0.MemInc=DMA_MINC_ENABLE
Bdma1.MEMTOMEM.0.Mode=DMA_NORMAL
Bdma1.MEMTOMEM.0.PeriphDataAlignment=DMA_PDATAALIGN_WORD
Bdma1.MEMTOMEM.0.PeriphInc=DMA_PINC_ENABLE
Bdma1.MEMTOMEM.0.Priority=DMA_PRIORITY_LOW
Bdma1.MEMTOMEM.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
Bdma1.Request0=MEMTOMEM
Bdma1.RequestsNb=1
Dma.ADC1.3.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.3.EventEnable=DISABLE
Dma.ADC1.3.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC1.3.Instance=DMA1_Stream3
Dma.ADC1.3.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.ADC1.3.MemInc=DMA_MINC_ENABLE
Dma.ADC1.3.Mode=DMA_CIRCULAR
Dma.ADC1.3.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.ADC1.3.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.3.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.ADC1.3.Priority=DMA_PRIORITY_LOW
Dma.ADC1.3.RequestNumber=1
Dma.ADC1.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.ADC1.3.SignalID=NONE
Dma.ADC1.3.SyncEnable=DISABLE
Dma.ADC1.3.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.ADC1.3.SyncRequestNumber=1
Dma.ADC1.3.SyncSignalID=NONE
Dma.Request0=USART1_RX
Dma.Request1=USART1_TX
Dma.Request2=TIM16_CH1
Dma.Request3=ADC1
Dma.Request4=SPI4_TX
Dma.Request5=UART5_RX
Dma.Request6=UART5_TX
Dma.Request7=UART4_RX
Dma.Request8=UART4_TX
Dma.RequestsNb=9
Dma.SPI4_TX.4.Direction=DMA_MEMORY_TO_PERIPH
Dma.SPI4_TX.4.EventEnable=DISABLE
Dma.SPI4_TX.4.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.SPI4_TX.4.Instance=DMA2_Stream7
Dma.SPI4_TX.4.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.SPI4_TX.4.MemInc=DMA_MINC_ENABLE
Dma.SPI4_TX.4.Mode=DMA_NORMAL
Dma.SPI4_TX.4.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.SPI4_TX.4.PeriphInc=DMA_PINC_DISABLE
Dma.SPI4_TX.4.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.SPI4_TX.4.Priority=DMA_PRIORITY_LOW
Dma.SPI4_TX.4.RequestNumber=1
Dma.SPI4_TX.4.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.SPI4_TX.4.SignalID=NONE
Dma.SPI4_TX.4.SyncEnable=DISABLE
Dma.SPI4_TX.4.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.SPI4_TX.4.SyncRequestNumber=1
Dma.SPI4_TX.4.SyncSignalID=NONE
Dma.TIM16_CH1.2.Direction=DMA_MEMORY_TO_PERIPH
Dma.TIM16_CH1.2.EventEnable=DISABLE
Dma.TIM16_CH1.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.TIM16_CH1.2.Instance=DMA1_Stream2
Dma.TIM16_CH1.2.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.TIM16_CH1.2.MemInc=DMA_MINC_ENABLE
Dma.TIM16_CH1.2.Mode=DMA_NORMAL
Dma.TIM16_CH1.2.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.TIM16_CH1.2.PeriphInc=DMA_PINC_DISABLE
Dma.TIM16_CH1.2.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.TIM16_CH1.2.Priority=DMA_PRIORITY_LOW
Dma.TIM16_CH1.2.RequestNumber=1
Dma.TIM16_CH1.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.TIM16_CH1.2.SignalID=NONE
Dma.TIM16_CH1.2.SyncEnable=DISABLE
Dma.TIM16_CH1.2.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.TIM16_CH1.2.SyncRequestNumber=1
Dma.TIM16_CH1.2.SyncSignalID=NONE
Dma.UART4_RX.7.Direction=DMA_PERIPH_TO_MEMORY
Dma.UART4_RX.7.EventEnable=DISABLE
Dma.UART4_RX.7.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART4_RX.7.Instance=DMA2_Stream6
Dma.UART4_RX.7.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART4_RX.7.MemInc=DMA_MINC_ENABLE
Dma.UART4_RX.7.Mode=DMA_NORMAL
Dma.UART4_RX.7.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART4_RX.7.PeriphInc=DMA_PINC_DISABLE
Dma.UART4_RX.7.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.UART4_RX.7.Priority=DMA_PRIORITY_LOW
Dma.UART4_RX.7.RequestNumber=1
Dma.UART4_RX.7.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.UART4_RX.7.SignalID=NONE
Dma.UART4_RX.7.SyncEnable=DISABLE
Dma.UART4_RX.7.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.UART4_RX.7.SyncRequestNumber=1
Dma.UART4_RX.7.SyncSignalID=NONE
Dma.UART4_TX.8.Direction=DMA_MEMORY_TO_PERIPH
Dma.UART4_TX.8.EventEnable=DISABLE
Dma.UART4_TX.8.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART4_TX.8.Instance=DMA2_Stream5
Dma.UART4_TX.8.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART4_TX.8.MemInc=DMA_MINC_ENABLE
Dma.UART4_TX.8.Mode=DMA_NORMAL
Dma.UART4_TX.8.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART4_TX.8.PeriphInc=DMA_PINC_DISABLE
Dma.UART4_TX.8.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.UART4_TX.8.Priority=DMA_PRIORITY_LOW
Dma.UART4_TX.8.RequestNumber=1
Dma.UART4_TX.8.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.UART4_TX.8.SignalID=NONE
Dma.UART4_TX.8.SyncEnable=DISABLE
Dma.UART4_TX.8.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.UART4_TX.8.SyncRequestNumber=1
Dma.UART4_TX.8.SyncSignalID=NONE
Dma.UART5_RX.5.Direction=DMA_PERIPH_TO_MEMORY
Dma.UART5_RX.5.EventEnable=DISABLE
Dma.UART5_RX.5.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART5_RX.5.Instance=DMA1_Stream5
Dma.UART5_RX.5.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART5_RX.5.MemInc=DMA_MINC_ENABLE
Dma.UART5_RX.5.Mode=DMA_NORMAL
Dma.UART5_RX.5.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART5_RX.5.PeriphInc=DMA_PINC_DISABLE
Dma.UART5_RX.5.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.UART5_RX.5.Priority=DMA_PRIORITY_LOW
Dma.UART5_RX.5.RequestNumber=1
Dma.UART5_RX.5.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.UART5_RX.5.SignalID=NONE
Dma.UART5_RX.5.SyncEnable=DISABLE
Dma.UART5_RX.5.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.UART5_RX.5.SyncRequestNumber=1
Dma.UART5_RX.5.SyncSignalID=NONE
Dma.UART5_TX.6.Direction=DMA_MEMORY_TO_PERIPH
Dma.UART5_TX.6.EventEnable=DISABLE
Dma.UART5_TX.6.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART5_TX.6.Instance=DMA1_Stream6
Dma.UART5_TX.6.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART5_TX.6.MemInc=DMA_MINC_ENABLE
Dma.UART5_TX.6.Mode=DMA_NORMAL
Dma.UART5_TX.6.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART5_TX.6.PeriphInc=DMA_PINC_DISABLE
Dma.UART5_TX.6.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.UART5_TX.6.Priority=DMA_PRIORITY_LOW
Dma.UART5_TX.6.RequestNumber=1
Dma.UART5_TX.6.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.UART5_TX.6.SignalID=NONE
Dma.UART5_TX.6.SyncEnable=DISABLE
Dma.UART5_TX.6.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.UART5_TX.6.SyncRequestNumber=1
Dma.UART5_TX.6.SyncSignalID=NONE
Dma.USART1_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.0.EventEnable=DISABLE
Dma.USART1_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.0.Instance=DMA2_Stream0
Dma.USART1_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.0.Mode=DMA_NORMAL
Dma.USART1_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.0.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.USART1_RX.0.Priority=DMA_PRIORITY_LOW
Dma.USART1_RX.0.RequestNumber=1
Dma.USART1_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.USART1_RX.0.SignalID=NONE
Dma.USART1_RX.0.SyncEnable=DISABLE
Dma.USART1_RX.0.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.USART1_RX.0.SyncRequestNumber=1
Dma.USART1_RX.0.SyncSignalID=NONE
Dma.USART1_TX.1.Direction=DMA_MEMORY_TO_PERIPH
Dma.USART1_TX.1.EventEnable=DISABLE
Dma.USART1_TX.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_TX.1.Instance=DMA2_Stream1
Dma.USART1_TX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_TX.1.MemInc=DMA_MINC_ENABLE
Dma.USART1_TX.1.Mode=DMA_NORMAL
Dma.USART1_TX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_TX.1.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_TX.1.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.USART1_TX.1.Priority=DMA_PRIORITY_LOW
Dma.USART1_TX.1.RequestNumber=1
Dma.USART1_TX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.USART1_TX.1.SignalID=NONE
Dma.USART1_TX.1.SyncEnable=DISABLE
Dma.USART1_TX.1.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.USART1_TX.1.SyncRequestNumber=1
Dma.USART1_TX.1.SyncSignalID=NONE
FDCAN1.CalculateBaudRateNominal=500000
FDCAN1.CalculateTimeBitNominal=2000
FDCAN1.CalculateTimeQuantumNominal=50.0
FDCAN1.IPParameters=CalculateTimeQuantumNominal,CalculateTimeBitNominal,CalculateBaudRateNominal,NominalPrescaler,NominalTimeSeg1,NominalTimeSeg2,NominalSyncJumpWidth,Mode,RxFifo0ElmtsNbr,TxFifoQueueElmtsNbr
FDCAN1.Mode=FDCAN_MODE_NORMAL
FDCAN1.NominalPrescaler=14
FDCAN1.NominalSyncJumpWidth=8
FDCAN1.NominalTimeSeg1=31
FDCAN1.NominalTimeSeg2=8
FDCAN1.RxFifo0ElmtsNbr=1
FDCAN1.TxFifoQueueElmtsNbr=1
FREERTOS.FootprintOK=true
FREERTOS.IPParameters=Tasks01,FootprintOK,configTIMER_TASK_STACK_DEPTH,configTOTAL_HEAP_SIZE
FREERTOS.Tasks01=IMU,24,1024,IMU_Task,Default,NULL,Dynamic,NULL,NULL;LVGL,8,1024,LVGL_TASK,Default,NULL,Dynamic,NULL,NULL;CommtRev,32,1024,CommuRevTask,Default,NULL,Dynamic,NULL,NULL;KEY,24,1024,KEY_Task,Default,NULL,Dynamic,NULL,NULL
FREERTOS.configTIMER_TASK_STACK_DEPTH=1024
FREERTOS.configTOTAL_HEAP_SIZE=30720
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C4.I2C_Speed_Mode=I2C_Standard
I2C4.IPParameters=Timing,I2C_Speed_Mode
I2C4.Timing=0x20B0CCFF
KeepUserPlacement=false
Mcu.CPN=STM32H7B0VBT6
Mcu.Family=STM32H7
Mcu.IP0=ADC1
Mcu.IP1=BDMA1
Mcu.IP10=OCTOSPI1
Mcu.IP11=RCC
Mcu.IP12=RNG
Mcu.IP13=SPI2
Mcu.IP14=SPI4
Mcu.IP15=SYS
Mcu.IP16=TIM1
Mcu.IP17=TIM2
Mcu.IP18=TIM3
Mcu.IP19=TIM4
Mcu.IP2=CORTEX_M7
Mcu.IP20=TIM5
Mcu.IP21=TIM6
Mcu.IP22=TIM8
Mcu.IP23=TIM13
Mcu.IP24=TIM15
Mcu.IP25=TIM16
Mcu.IP26=TIM17
Mcu.IP27=UART4
Mcu.IP28=UART5
Mcu.IP29=UART7
Mcu.IP3=DEBUG
Mcu.IP30=UART8
Mcu.IP31=UART9
Mcu.IP32=USART1
Mcu.IP33=USART2
Mcu.IP34=USB_DEVICE
Mcu.IP35=USB_OTG_HS
Mcu.IP4=DMA
Mcu.IP5=FDCAN1
Mcu.IP6=FREERTOS
Mcu.IP7=I2C4
Mcu.IP8=LPTIM1
Mcu.IP9=NVIC
Mcu.IPNb=36
Mcu.Name=STM32H7B0VBTx
Mcu.Package=LQFP100
Mcu.Pin0=PE2
Mcu.Pin1=PE3
Mcu.Pin10=PC3_C
Mcu.Pin11=PA0
Mcu.Pin12=PA1
Mcu.Pin13=PA2
Mcu.Pin14=PA3
Mcu.Pin15=PA6
Mcu.Pin16=PA7
Mcu.Pin17=PC4
Mcu.Pin18=PC5
Mcu.Pin19=PB0
Mcu.Pin2=PE4
Mcu.Pin20=PB1
Mcu.Pin21=PB2
Mcu.Pin22=PE7
Mcu.Pin23=PE8
Mcu.Pin24=PE9
Mcu.Pin25=PE10
Mcu.Pin26=PE11
Mcu.Pin27=PE12
Mcu.Pin28=PE13
Mcu.Pin29=PE14
Mcu.Pin3=PE5
Mcu.Pin30=PE15
Mcu.Pin31=PB10
Mcu.Pin32=PB11
Mcu.Pin33=PB12
Mcu.Pin34=PB13
Mcu.Pin35=PD12
Mcu.Pin36=PD13
Mcu.Pin37=PD14
Mcu.Pin38=PD15
Mcu.Pin39=PC6
Mcu.Pin4=PE6
Mcu.Pin40=PC7
Mcu.Pin41=PC8
Mcu.Pin42=PC9
Mcu.Pin43=PA9
Mcu.Pin44=PA10
Mcu.Pin45=PA11
Mcu.Pin46=PA12
Mcu.Pin47=PA13
Mcu.Pin48=PA14
Mcu.Pin49=PA15
Mcu.Pin5=PH0-OSC_IN
Mcu.Pin50=PC10
Mcu.Pin51=PC11
Mcu.Pin52=PD0
Mcu.Pin53=PD1
Mcu.Pin54=PD2
Mcu.Pin55=PD3
Mcu.Pin56=PD4
Mcu.Pin57=PD5
Mcu.Pin58=PB3
Mcu.Pin59=PB4
Mcu.Pin6=PH1-OSC_OUT
Mcu.Pin60=PB5
Mcu.Pin61=PB6
Mcu.Pin62=PB7
Mcu.Pin63=PB8
Mcu.Pin64=PE0
Mcu.Pin65=PE1
Mcu.Pin66=VP_FREERTOS_VS_CMSIS_V2
Mcu.Pin67=VP_LPTIM1_VS_LPTIM_counterModeInternalClock
Mcu.Pin68=VP_OCTOSPI1_VS_quad
Mcu.Pin69=VP_RNG_VS_RNG
Mcu.Pin7=PC0
Mcu.Pin70=VP_SYS_VS_tim7
Mcu.Pin71=VP_TIM6_VS_ClockSourceINT
Mcu.Pin72=VP_TIM13_VS_ClockSourceINT
Mcu.Pin73=VP_TIM15_VS_ClockSourceINT
Mcu.Pin74=VP_TIM16_VS_ClockSourceINT
Mcu.Pin75=VP_TIM17_VS_ClockSourceINT
Mcu.Pin76=VP_USB_DEVICE_VS_USB_DEVICE_CDC_HS
Mcu.Pin8=PC1
Mcu.Pin9=PC2_C
Mcu.PinsNb=77
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32H7B0VBTx
MxCube.Version=6.6.1
MxDb.Version=DB.6.0.60
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.DMA1_Stream2_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream3_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream5_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream6_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA2_Stream0_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA2_Stream1_IRQn=true\:5\:0\:false\:false\:true\:false\:false\:true\:true
NVIC.DMA2_Stream5_IRQn=true\:5\:0\:false\:false\:true\:false\:false\:true\:true
NVIC.DMA2_Stream6_IRQn=true\:5\:0\:false\:false\:true\:false\:false\:true\:true
NVIC.DMA2_Stream7_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.EXTI9_5_IRQn=true\:9\:0\:true\:false\:true\:true\:true\:true\:true
NVIC.FDCAN1_IT0_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:true\:false
NVIC.OTG_HS_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.PendSV_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SPI4_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:false\:false\:false\:false\:false
NVIC.SavedPendsvIrqHandlerGenerated=true
NVIC.SavedSvcallIrqHandlerGenerated=true
NVIC.SavedSystickIrqHandlerGenerated=true
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:true\:false
NVIC.TIM15_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.TIM6_DAC_IRQn=true\:5\:0\:false\:true\:true\:4\:true\:true\:true\:true
NVIC.TIM7_IRQn=true\:15\:0\:false\:false\:true\:false\:false\:true\:true
NVIC.TIM8_BRK_TIM12_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.TIM8_UP_TIM13_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.TimeBase=TIM7_IRQn
NVIC.TimeBaseIP=TIM7
NVIC.UART4_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.UART5_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.UART8_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.UART9_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:5\:0\:false\:true\:true\:3\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
OCTOSPI1.ChipSelectHighTime=1
OCTOSPI1.ClockMode=HAL_OSPI_CLOCK_MODE_3
OCTOSPI1.ClockPrescaler=3-1
OCTOSPI1.DeviceSize=23
OCTOSPI1.FifoThreshold=8
OCTOSPI1.IPParameters=ChipSelectHighTime,SampleShifting,FifoThreshold,ClockPrescaler,DeviceSize,ClockMode
OCTOSPI1.SampleShifting=HAL_OSPI_SAMPLE_SHIFTING_HALFCYCLE
PA0.GPIOParameters=GPIO_ModeDefaultPP,GPIO_Speed
PA0.GPIO_ModeDefaultPP=GPIO_MODE_AF_OD
PA0.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PA0.Locked=true
PA0.Signal=S_TIM5_CH1
PA1.GPIOParameters=GPIO_ModeDefaultPP,GPIO_Speed
PA1.GPIO_ModeDefaultPP=GPIO_MODE_AF_OD
PA1.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PA1.Locked=true
PA1.Signal=S_TIM5_CH2
PA10.GPIOParameters=GPIO_Speed,GPIO_PuPd
PA10.GPIO_PuPd=GPIO_PULLUP
PA10.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA10.Locked=true
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA11.Mode=Device_Only_FS
PA11.Signal=USB_OTG_HS_DM
PA12.Mode=Device_Only_FS
PA12.Signal=USB_OTG_HS_DP
PA13.Mode=Serial_Wire
PA13.Signal=DEBUG_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=DEBUG_JTCK-SWCLK
PA15.GPIOParameters=GPIO_ModeDefaultPP,GPIO_Speed
PA15.GPIO_ModeDefaultPP=GPIO_MODE_AF_OD
PA15.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PA15.Locked=true
PA15.Signal=S_TIM2_CH1_ETR
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA6.Locked=true
PA6.Mode=OCTOSPI1_IOL_Port1L
PA6.Signal=OCTOSPIM_P1_IO3
PA7.Locked=true
PA7.Mode=OCTOSPI1_IOL_Port1L
PA7.Signal=OCTOSPIM_P1_IO2
PA9.GPIOParameters=GPIO_Speed
PA9.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA9.Locked=true
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB0.Locked=true
PB0.Mode=OCTOSPI1_IOL_Port1L
PB0.Signal=OCTOSPIM_P1_IO1
PB1.Locked=true
PB1.Mode=OCTOSPI1_IOL_Port1L
PB1.Signal=OCTOSPIM_P1_IO0
PB10.Locked=true
PB10.Mode=OCTOSPI1_Port1_NCS
PB10.Signal=OCTOSPIM_P1_NCS
PB11.GPIOParameters=GPIO_Label
PB11.GPIO_Label=LCD_BL
PB11.Locked=true
PB11.Signal=GPIO_Output
PB12.Mode=Asynchronous
PB12.Signal=UART5_RX
PB13.Mode=Asynchronous
PB13.Signal=UART5_TX
PB2.GPIOParameters=GPIO_PuPd
PB2.GPIO_PuPd=GPIO_PULLUP
PB2.Locked=true
PB2.Mode=O1_P1_CLK
PB2.Signal=OCTOSPIM_P1_CLK
PB3.GPIOParameters=GPIO_ModeDefaultPP,GPIO_Speed
PB3.GPIO_ModeDefaultPP=GPIO_MODE_AF_OD
PB3.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB3.Locked=true
PB3.Signal=S_TIM2_CH2
PB4.GPIOParameters=GPIO_ModeDefaultPP,GPIO_Speed
PB4.GPIO_ModeDefaultPP=GPIO_MODE_AF_OD
PB4.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB4.Locked=true
PB4.Signal=S_TIM3_CH1
PB5.GPIOParameters=GPIO_ModeDefaultPP,GPIO_Speed
PB5.GPIO_ModeDefaultPP=GPIO_MODE_AF_OD
PB5.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB5.Locked=true
PB5.Signal=S_TIM3_CH2
PB6.GPIOParameters=GPIO_ModeDefaultPP,GPIO_Speed,GPIO_PuPd
PB6.GPIO_ModeDefaultPP=GPIO_MODE_AF_OD
PB6.GPIO_PuPd=GPIO_NOPULL
PB6.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB6.Locked=true
PB6.Signal=S_TIM4_CH1
PB7.GPIOParameters=GPIO_ModeDefaultPP,GPIO_Speed
PB7.GPIO_ModeDefaultPP=GPIO_MODE_AF_OD
PB7.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB7.Locked=true
PB7.Signal=S_TIM4_CH2
PB7.Stacked=true
PB8.GPIOParameters=GPIO_Speed,GPIO_PuPd
PB8.GPIO_PuPd=GPIO_PULLUP
PB8.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB8.Locked=true
PB8.Signal=S_TIM16_CH1
PC0.GPIOParameters=GPIO_Label
PC0.GPIO_Label=MPU_CS
PC0.Locked=true
PC0.Signal=GPIO_Output
PC1.GPIOParameters=GPIO_Label
PC1.GPIO_Label=MAG_CS
PC1.Locked=true
PC1.Signal=GPIO_Output
PC10.Mode=Asynchronous
PC10.Signal=UART4_TX
PC11.Mode=Asynchronous
PC11.Signal=UART4_RX
PC2_C.Locked=true
PC2_C.Mode=Full_Duplex_Master
PC2_C.Signal=SPI2_MISO
PC3_C.Locked=true
PC3_C.Mode=Full_Duplex_Master
PC3_C.Signal=SPI2_MOSI
PC4.GPIOParameters=GPIO_Label
PC4.GPIO_Label=ADC_BAT
PC4.Locked=true
PC4.Signal=SharedAnalog_PC4
PC5.GPIOParameters=GPIO_Label
PC5.GPIO_Label=MPU_INT
PC5.Locked=true
PC5.Signal=GPXTI5
PC6.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC6.GPIO_Label=Motor1_O1
PC6.GPIO_PuPd=GPIO_PULLUP
PC6.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PC6.Locked=true
PC6.Signal=S_TIM8_CH1
PC7.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC7.GPIO_Label=Motor2_O1
PC7.GPIO_PuPd=GPIO_PULLUP
PC7.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PC7.Locked=true
PC7.Signal=S_TIM8_CH2
PC8.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC8.GPIO_Label=Motor3_O1
PC8.GPIO_PuPd=GPIO_PULLUP
PC8.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PC8.Locked=true
PC8.Signal=S_TIM8_CH3
PC9.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC9.GPIO_Label=Motor4_O1
PC9.GPIO_PuPd=GPIO_PULLUP
PC9.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PC9.Locked=true
PC9.Signal=S_TIM8_CH4
PD0.GPIOParameters=GPIO_Speed,GPIO_PuPd
PD0.GPIO_PuPd=GPIO_PULLUP
PD0.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD0.Mode=FDCAN_Activate
PD0.Signal=FDCAN1_RX
PD1.GPIOParameters=GPIO_Speed,GPIO_PuPd
PD1.GPIO_PuPd=GPIO_PULLUP
PD1.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD1.Mode=FDCAN_Activate
PD1.Signal=FDCAN1_TX
PD12.Mode=I2C
PD12.Signal=I2C4_SCL
PD13.Mode=I2C
PD13.Signal=I2C4_SDA
PD14.Mode=Asynchronous
PD14.Signal=UART9_RX
PD15.Mode=Asynchronous
PD15.Signal=UART9_TX
PD2.GPIOParameters=GPIO_Label
PD2.GPIO_Label=LED0
PD2.Locked=true
PD2.Signal=GPIO_Output
PD3.Locked=true
PD3.Mode=Full_Duplex_Master
PD3.Signal=SPI2_SCK
PD4.GPIOParameters=GPIO_Label
PD4.GPIO_Label=KEY0
PD4.Locked=true
PD4.Signal=GPIO_Input
PD5.GPIOParameters=GPIO_Label
PD5.GPIO_Label=KEY1
PD5.Locked=true
PD5.Signal=GPIO_Input
PE0.Mode=Asynchronous
PE0.Signal=UART8_RX
PE1.Mode=Asynchronous
PE1.Signal=UART8_TX
PE10.GPIOParameters=GPIO_Label
PE10.GPIO_Label=LCD_RESET
PE10.Locked=true
PE10.Signal=GPIO_Output
PE11.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PE11.GPIO_Label=Motor2_O2
PE11.GPIO_PuPd=GPIO_PULLUP
PE11.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PE11.Locked=true
PE11.Signal=S_TIM1_CH2
PE12.GPIOParameters=GPIO_Speed
PE12.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PE12.Mode=Simplex_Bidirectional_Master
PE12.Signal=SPI4_SCK
PE13.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PE13.GPIO_Label=Motor3_O2
PE13.GPIO_PuPd=GPIO_PULLUP
PE13.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PE13.Locked=true
PE13.Signal=S_TIM1_CH3
PE14.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PE14.GPIO_Label=Motor4_O2
PE14.GPIO_PuPd=GPIO_PULLUP
PE14.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PE14.Locked=true
PE14.Signal=S_TIM1_CH4
PE15.GPIOParameters=GPIO_Label
PE15.GPIO_Label=LCD_DC
PE15.Locked=true
PE15.Signal=GPIO_Output
PE2.GPIOParameters=GPIO_Label
PE2.GPIO_Label=POWER_SW
PE2.Locked=true
PE2.Signal=GPIO_Input
PE3.GPIOParameters=GPIO_Label
PE3.GPIO_Label=POWER_OUT
PE3.Locked=true
PE3.Signal=GPIO_Output
PE4.GPIOParameters=GPIO_Speed
PE4.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PE4.Locked=true
PE4.Mode=NSS_Signal_Hard_Output
PE4.Signal=SPI4_NSS
PE5.Signal=S_TIM15_CH1
PE6.GPIOParameters=GPIO_Speed
PE6.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PE6.Locked=true
PE6.Mode=Simplex_Bidirectional_Master
PE6.Signal=SPI4_MOSI
PE7.Mode=Asynchronous
PE7.Signal=UART7_RX
PE8.Mode=Asynchronous
PE8.Signal=UART7_TX
PE9.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PE9.GPIO_Label=Motor1_O2
PE9.GPIO_PuPd=GPIO_PULLUP
PE9.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PE9.Locked=true
PE9.Signal=S_TIM1_CH1
PH0-OSC_IN.Locked=true
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Locked=true
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32H7B0VBTx
ProjectManager.FirmwarePackage=STM32Cube FW_H7 V1.10.0
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x400
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=0
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=SControlBoard.ioc
ProjectManager.ProjectName=SControlBoard
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART1_UART_Init-USART1-false-HAL-true,5-MX_TIM6_Init-TIM6-false-HAL-true,6-MX_OCTOSPI1_Init-OCTOSPI1-false-HAL-true,7-MX_SPI4_Init-SPI4-false-HAL-true,8-MX_TIM16_Init-TIM16-false-HAL-true,9-MX_RNG_Init-RNG-false-HAL-true,10-MX_ADC1_Init-ADC1-false-HAL-true,11-MX_SPI2_Init-SPI2-false-HAL-true,12-MX_TIM8_Init-TIM8-false-HAL-true,13-MX_TIM2_Init-TIM2-false-HAL-true,14-MX_TIM3_Init-TIM3-false-HAL-true,15-MX_TIM4_Init-TIM4-false-HAL-true,16-MX_TIM5_Init-TIM5-false-HAL-true,17-MX_FDCAN1_Init-FDCAN1-false-HAL-true,18-MX_TIM17_Init-TIM17-false-HAL-true,19-MX_UART4_Init-UART4-false-HAL-true,20-MX_UART5_Init-UART5-false-HAL-true,21-MX_UART7_Init-UART7-false-HAL-true,22-MX_UART8_Init-UART8-false-HAL-true,23-MX_USART2_UART_Init-USART2-false-HAL-true,24-MX_USB_DEVICE_Init-USB_DEVICE-false-HAL-false,25-MX_TIM1_Init-TIM1-false-HAL-true,26-MX_UART9_Init-UART9-false-HAL-true,27-MX_TIM15_Init-TIM15-false-HAL-true,28-MX_BDMA1_Init-BDMA1-false-HAL-true,29-MX_TIM13_Init-TIM13-false-HAL-true,30-MX_LPTIM1_Init-LPTIM1-false-HAL-true,31-MX_I2C4_Init-I2C4-false-HAL-true,0-MX_CORTEX_M7_Init-CORTEX_M7-false-HAL-true
RCC.ADCCLockSelection=RCC_ADCCLKSOURCE_CLKP
RCC.ADCFreq_Value=64000000
RCC.AHB12Freq_Value=280000000
RCC.AHB4Freq_Value=280000000
RCC.APB1Freq_Value=140000000
RCC.APB2Freq_Value=140000000
RCC.APB3Freq_Value=140000000
RCC.APB4Freq_Value=140000000
RCC.AXIClockFreq_Value=280000000
RCC.CDCPREFreq_Value=280000000
RCC.CDPPRE=RCC_APB3_DIV2
RCC.CDPPRE1=RCC_APB1_DIV2
RCC.CDPPRE2=RCC_APB2_DIV2
RCC.CECFreq_Value=32000
RCC.CKPERFreq_Value=64000000
RCC.CortexFreq_Value=280000000
RCC.CpuClockFreq_Value=280000000
RCC.DFSDM2ACLkFreq_Value=140000000
RCC.DFSDM2Freq_Value=140000000
RCC.DFSDMACLkFreq_Value=280000000
RCC.DFSDMFreq_Value=140000000
RCC.DIVM1=2
RCC.DIVM2=8
RCC.DIVM3=25
RCC.DIVN1=140
RCC.DIVN3=96
RCC.DIVP1Freq_Value=280000000
RCC.DIVP2Freq_Value=64500000
RCC.DIVP3Freq_Value=15360000
RCC.DIVQ1Freq_Value=280000000
RCC.DIVQ2=24
RCC.DIVQ2Freq_Value=5375000
RCC.DIVQ3Freq_Value=15360000
RCC.DIVR1Freq_Value=280000000
RCC.DIVR2Freq_Value=64500000
RCC.DIVR3Freq_Value=15360000
RCC.EnbaleCSS=true
RCC.FDCANFreq_Value=280000000
RCC.FMCFreq_Value=280000000
RCC.FamilyName=M
RCC.HCLK3ClockFreq_Value=280000000
RCC.HCLKFreq_Value=280000000
RCC.HSE_VALUE=8000000
RCC.I2C123Freq_Value=140000000
RCC.I2C4Freq_Value=140000000
RCC.IPParameters=ADCCLockSelection,ADCFreq_Value,AHB12Freq_Value,AHB4Freq_Value,APB1Freq_Value,APB2Freq_Value,APB3Freq_Value,APB4Freq_Value,AXIClockFreq_Value,CDCPREFreq_Value,CDPPRE,CDPPRE1,CDPPRE2,CECFreq_Value,CKPERFreq_Value,CortexFreq_Value,CpuClockFreq_Value,DFSDM2ACLkFreq_Value,DFSDM2Freq_Value,DFSDMACLkFreq_Value,DFSDMFreq_Value,DIVM1,DIVM2,DIVM3,DIVN1,DIVN3,DIVP1Freq_Value,DIVP2Freq_Value,DIVP3Freq_Value,DIVQ1Freq_Value,DIVQ2,DIVQ2Freq_Value,DIVQ3Freq_Value,DIVR1Freq_Value,DIVR2Freq_Value,DIVR3Freq_Value,EnbaleCSS,FDCANFreq_Value,FMCFreq_Value,FamilyName,HCLK3ClockFreq_Value,HCLKFreq_Value,HSE_VALUE,I2C123Freq_Value,I2C4Freq_Value,LPTIM1Freq_Value,LPTIM2Freq_Value,LPTIM345Freq_Value,LPUART1Freq_Value,LTDCFreq_Value,MCO1PinFreq_Value,MCO2PinFreq_Value,PLLSourceVirtual,QSPIFreq_Value,RNGFreq_Value,RTCFreq_Value,SAI1Freq_Value,SAI2AFreq_Value,SAI2BFreq_Value,SDMMCFreq_Value,SPDIFRXFreq_Value,SPI123CLockSelection,SPI123Freq_Value,SPI45Freq_Value,SPI6Freq_Value,SRDPPRE,SWPMI1Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,Tim1OutputFreq_Value,Tim2OutputFreq_Value,TraceFreq_Value,USART16Freq_Value,USART234578Freq_Value,USBCLockSelection,USBFreq_Value,VCO1OutputFreq_Value,VCO2OutputFreq_Value,VCO3OutputFreq_Value,VCOInput1Freq_Value,VCOInput2Freq_Value,VCOInput3Freq_Value
RCC.LPTIM1Freq_Value=140000000
RCC.LPTIM2Freq_Value=140000000
RCC.LPTIM345Freq_Value=140000000
RCC.LPUART1Freq_Value=140000000
RCC.LTDCFreq_Value=15360000
RCC.MCO1PinFreq_Value=64000000
RCC.MCO2PinFreq_Value=280000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.QSPIFreq_Value=280000000
RCC.RNGFreq_Value=48000000
RCC.RTCFreq_Value=32000
RCC.SAI1Freq_Value=280000000
RCC.SAI2AFreq_Value=280000000
RCC.SAI2BFreq_Value=280000000
RCC.SDMMCFreq_Value=280000000
RCC.SPDIFRXFreq_Value=280000000
RCC.SPI123CLockSelection=RCC_SPI123CLKSOURCE_CLKP
RCC.SPI123Freq_Value=64000000
RCC.SPI45Freq_Value=140000000
RCC.SPI6Freq_Value=140000000
RCC.SRDPPRE=RCC_APB4_DIV2
RCC.SWPMI1Freq_Value=140000000
RCC.SYSCLKFreq_VALUE=280000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.Tim1OutputFreq_Value=280000000
RCC.Tim2OutputFreq_Value=280000000
RCC.TraceFreq_Value=280000000
RCC.USART16Freq_Value=140000000
RCC.USART234578Freq_Value=140000000
RCC.USBCLockSelection=RCC_USBCLKSOURCE_HSI48
RCC.USBFreq_Value=48000000
RCC.VCO1OutputFreq_Value=560000000
RCC.VCO2OutputFreq_Value=129000000
RCC.VCO3OutputFreq_Value=30720000
RCC.VCOInput1Freq_Value=4000000
RCC.VCOInput2Freq_Value=1000000
RCC.VCOInput3Freq_Value=320000
SH.GPXTI5.0=GPIO_EXTI5
SH.GPXTI5.ConfNb=1
SH.S_TIM15_CH1.0=TIM15_CH1,Output Compare1 CH1
SH.S_TIM15_CH1.ConfNb=1
SH.S_TIM16_CH1.0=TIM16_CH1,PWM Generation1 CH1
SH.S_TIM16_CH1.ConfNb=1
SH.S_TIM1_CH1.0=TIM1_CH1,PWM Generation1 CH1
SH.S_TIM1_CH1.ConfNb=1
SH.S_TIM1_CH2.0=TIM1_CH2,PWM Generation2 CH2
SH.S_TIM1_CH2.ConfNb=1
SH.S_TIM1_CH3.0=TIM1_CH3,PWM Generation3 CH3
SH.S_TIM1_CH3.ConfNb=1
SH.S_TIM1_CH4.0=TIM1_CH4,PWM Generation4 CH4
SH.S_TIM1_CH4.ConfNb=1
SH.S_TIM2_CH1_ETR.0=TIM2_CH1,Encoder_Interface
SH.S_TIM2_CH1_ETR.ConfNb=1
SH.S_TIM2_CH2.0=TIM2_CH2,Encoder_Interface
SH.S_TIM2_CH2.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,Encoder_Interface
SH.S_TIM3_CH1.ConfNb=1
SH.S_TIM3_CH2.0=TIM3_CH2,Encoder_Interface
SH.S_TIM3_CH2.ConfNb=1
SH.S_TIM4_CH1.0=TIM4_CH1,Encoder_Interface
SH.S_TIM4_CH1.ConfNb=1
SH.S_TIM4_CH2.0=TIM4_CH2,Encoder_Interface
SH.S_TIM4_CH2.ConfNb=1
SH.S_TIM5_CH1.0=TIM5_CH1,Encoder_Interface
SH.S_TIM5_CH1.ConfNb=1
SH.S_TIM5_CH2.0=TIM5_CH2,Encoder_Interface
SH.S_TIM5_CH2.ConfNb=1
SH.S_TIM8_CH1.0=TIM8_CH1,PWM Generation1 CH1
SH.S_TIM8_CH1.ConfNb=1
SH.S_TIM8_CH2.0=TIM8_CH2,PWM Generation2 CH2
SH.S_TIM8_CH2.ConfNb=1
SH.S_TIM8_CH3.0=TIM8_CH3,PWM Generation3 CH3
SH.S_TIM8_CH3.ConfNb=1
SH.S_TIM8_CH4.0=TIM8_CH4,PWM Generation4 CH4
SH.S_TIM8_CH4.ConfNb=1
SH.SharedAnalog_PC4.0=GPIO_Analog
SH.SharedAnalog_PC4.1=ADC1_INP4,IN4-Single-Ended
SH.SharedAnalog_PC4.ConfNb=2
SPI2.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_64
SPI2.CLKPhase=SPI_PHASE_2EDGE
SPI2.CLKPolarity=SPI_POLARITY_HIGH
SPI2.CalculateBaudRate=1000.0 KBits/s
SPI2.DataSize=SPI_DATASIZE_8BIT
SPI2.Direction=SPI_DIRECTION_2LINES
SPI2.IPParameters=VirtualType,Mode,Direction,BaudRatePrescaler,CalculateBaudRate,DataSize,CLKPolarity,CLKPhase,NSSPMode
SPI2.Mode=SPI_MODE_MASTER
SPI2.NSSPMode=SPI_NSS_PULSE_DISABLE
SPI2.VirtualType=VM_MASTER
SPI4.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_2
SPI4.CalculateBaudRate=70.0 MBits/s
SPI4.DataSize=SPI_DATASIZE_8BIT
SPI4.Direction=SPI_DIRECTION_1LINE
SPI4.FifoThreshold=SPI_FIFO_THRESHOLD_02DATA
SPI4.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,VirtualNSS,DataSize,FifoThreshold,NSSPMode,BaudRatePrescaler
SPI4.Mode=SPI_MODE_MASTER
SPI4.NSSPMode=SPI_NSS_PULSE_DISABLE
SPI4.VirtualNSS=VM_NSSHARD
SPI4.VirtualType=VM_MASTER
TIM1.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM1.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM1.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM1.Channel-PWM\ Generation3\ CH3=TIM_CHANNEL_3
TIM1.Channel-PWM\ Generation4\ CH4=TIM_CHANNEL_4
TIM1.IPParameters=Channel-PWM Generation1 CH1,Channel-PWM Generation2 CH2,Channel-PWM Generation3 CH3,Channel-PWM Generation4 CH4,Prescaler,Period,AutoReloadPreload
TIM1.Period=10000-1
TIM1.Prescaler=28-1
TIM13.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_DISABLE
TIM13.IPParameters=Prescaler,Period,AutoReloadPreload
TIM13.Period=10000-1
TIM13.Prescaler=280-1
TIM15.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_DISABLE
TIM15.Channel-Output\ Compare1\ CH1=TIM_CHANNEL_1
TIM15.Channel-Output\ Compare2\ CH2=TIM_CHANNEL_2
TIM15.IPParameters=Prescaler,Period,AutoReloadPreload,Channel-Output Compare1 CH1,Channel-Output Compare2 CH2
TIM15.Period=1000-1
TIM15.Prescaler=280-1
TIM16.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM16.Channel=TIM_CHANNEL_1
TIM16.IPParameters=Channel,Prescaler,Period,AutoReloadPreload
TIM16.Period=1600-1
TIM16.Prescaler=28-1
TIM17.IPParameters=Prescaler
TIM17.Prescaler=280-1
TIM2.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_DISABLE
TIM2.EncoderMode=TIM_ENCODERMODE_TI12
TIM2.IC1Filter=2
TIM2.IC2Filter=2
TIM2.IPParameters=Period,IC1Filter,IC2Filter,AutoReloadPreload,EncoderMode
TIM2.Period=0xffff
TIM3.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_DISABLE
TIM3.EncoderMode=TIM_ENCODERMODE_TI12
TIM3.IC1Filter=2
TIM3.IC2Filter=2
TIM3.IPParameters=IC2Filter,IC1Filter,AutoReloadPreload,EncoderMode
TIM4.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_DISABLE
TIM4.EncoderMode=TIM_ENCODERMODE_TI12
TIM4.IC1Filter=2
TIM4.IC2Filter=2
TIM4.IPParameters=IC2Filter,IC1Filter,Period,AutoReloadPreload,EncoderMode
TIM4.Period=0xffff
TIM5.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_DISABLE
TIM5.EncoderMode=TIM_ENCODERMODE_TI12
TIM5.IC1Filter=2
TIM5.IC2Filter=2
TIM5.IPParameters=IC1Filter,IC2Filter,Period,AutoReloadPreload,EncoderMode
TIM5.Period=0xffff
TIM6.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM6.IPParameters=Prescaler,Period,AutoReloadPreload
TIM6.Period=1000-1
TIM6.Prescaler=280-1
TIM8.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM8.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM8.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM8.Channel-PWM\ Generation3\ CH3=TIM_CHANNEL_3
TIM8.Channel-PWM\ Generation4\ CH4=TIM_CHANNEL_4
TIM8.IPParameters=Period,AutoReloadPreload,Prescaler,Channel-PWM Generation1 CH1,Channel-PWM Generation2 CH2,Channel-PWM Generation3 CH3,Channel-PWM Generation4 CH4
TIM8.Period=10000-1
TIM8.Prescaler=28-1
UART4.BaudRate=921600
UART4.DMADisableonRxErrorParam=UART_ADVFEATURE_DMA_DISABLEONRXERROR
UART4.IPParameters=OverrunDisableParam,DMADisableonRxErrorParam,BaudRate,SwapParam
UART4.OverrunDisableParam=UART_ADVFEATURE_OVERRUN_DISABLE
UART4.SwapParam=UART_ADVFEATURE_SWAP_ENABLE
UART5.BaudRate=9600
UART5.DMADisableonRxErrorParam=UART_ADVFEATURE_DMA_DISABLEONRXERROR
UART5.IPParameters=BaudRate,SwapParam,OverrunDisableParam,DMADisableonRxErrorParam
UART5.OverrunDisableParam=UART_ADVFEATURE_OVERRUN_DISABLE
UART5.SwapParam=UART_ADVFEATURE_SWAP_ENABLE
UART7.BaudRate=921600
UART7.DMADisableonRxErrorParam=UART_ADVFEATURE_DMA_DISABLEONRXERROR
UART7.IPParameters=OverrunDisableParam,DMADisableonRxErrorParam,BaudRate
UART7.OverrunDisableParam=UART_ADVFEATURE_OVERRUN_DISABLE
UART8.BaudRate=100000
UART8.DMADisableonRxErrorParam=UART_ADVFEATURE_DMA_DISABLEONRXERROR
UART8.IPParameters=OverrunDisableParam,DMADisableonRxErrorParam,BaudRate,WordLength,Parity,StopBits,RxPinLevelInvertParam
UART8.OverrunDisableParam=UART_ADVFEATURE_OVERRUN_DISABLE
UART8.Parity=PARITY_EVEN
UART8.RxPinLevelInvertParam=UART_ADVFEATURE_RXINV_ENABLE
UART8.StopBits=UART_STOPBITS_2
UART8.WordLength=WORDLENGTH_9B
USART1.BaudRate=921600
USART1.DMADisableonRxErrorParam=ADVFEATURE_DMA_DISABLEONRXERROR
USART1.IPParameters=VirtualMode-Asynchronous,BaudRate,DMADisableonRxErrorParam,OverrunDisableParam
USART1.OverrunDisableParam=ADVFEATURE_OVERRUN_DISABLE
USART1.VirtualMode-Asynchronous=VM_ASYNC
USART2.BaudRate=921600
USART2.DMADisableonRxErrorParam=ADVFEATURE_DMA_DISABLEONRXERROR
USART2.IPParameters=VirtualMode-Asynchronous,BaudRate,OverrunDisableParam,DMADisableonRxErrorParam
USART2.OverrunDisableParam=ADVFEATURE_OVERRUN_DISABLE
USART2.VirtualMode-Asynchronous=VM_ASYNC
USB_DEVICE.CLASS_NAME_HS=CDC
USB_DEVICE.IPParameters=VirtualMode-CDC_HS,VirtualModeHS,CLASS_NAME_HS,VID-CDC_HS,PID_CDC_HS
USB_DEVICE.PID_CDC_HS=0xa060
USB_DEVICE.VID-CDC_HS=0x0483
USB_DEVICE.VirtualMode-CDC_HS=Cdc
USB_DEVICE.VirtualModeHS=Cdc_HS
USB_OTG_HS.IPParameters=VirtualMode-Device_Only_FS
USB_OTG_HS.VirtualMode-Device_Only_FS=Device_Only_FS
VP_FREERTOS_VS_CMSIS_V2.Mode=CMSIS_V2
VP_FREERTOS_VS_CMSIS_V2.Signal=FREERTOS_VS_CMSIS_V2
VP_LPTIM1_VS_LPTIM_counterModeInternalClock.Mode=Counts__internal_clock_event_00
VP_LPTIM1_VS_LPTIM_counterModeInternalClock.Signal=LPTIM1_VS_LPTIM_counterModeInternalClock
VP_OCTOSPI1_VS_quad.Mode=quad_mode
VP_OCTOSPI1_VS_quad.Signal=OCTOSPI1_VS_quad
VP_RNG_VS_RNG.Mode=RNG_Activate
VP_RNG_VS_RNG.Signal=RNG_VS_RNG
VP_SYS_VS_tim7.Mode=TIM7
VP_SYS_VS_tim7.Signal=SYS_VS_tim7
VP_TIM13_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM13_VS_ClockSourceINT.Signal=TIM13_VS_ClockSourceINT
VP_TIM15_VS_ClockSourceINT.Mode=Internal
VP_TIM15_VS_ClockSourceINT.Signal=TIM15_VS_ClockSourceINT
VP_TIM16_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM16_VS_ClockSourceINT.Signal=TIM16_VS_ClockSourceINT
VP_TIM17_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM17_VS_ClockSourceINT.Signal=TIM17_VS_ClockSourceINT
VP_TIM6_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM6_VS_ClockSourceINT.Signal=TIM6_VS_ClockSourceINT
VP_USB_DEVICE_VS_USB_DEVICE_CDC_HS.Mode=CDC_HS
VP_USB_DEVICE_VS_USB_DEVICE_CDC_HS.Signal=USB_DEVICE_VS_USB_DEVICE_CDC_HS
board=custom
