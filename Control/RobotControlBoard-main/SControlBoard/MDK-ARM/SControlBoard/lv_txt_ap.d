.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\lv_txt_ap.c
.\scontrolboard\lv_txt_ap.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stddef.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\lv_bidi.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../lv_conf_internal.h
.\scontrolboard\lv_txt_ap.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../lv_conf_kconfig.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../../../lv_conf.h
.\scontrolboard\lv_txt_ap.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\lv_txt.h
.\scontrolboard\lv_txt_ap.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdarg.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\lv_area.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../font/lv_font.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../font/lv_symbol_def.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\lv_printf.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\lv_types.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\lv_txt_ap.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/lv_draw.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/../misc/lv_style.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/../misc/lv_color.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/../misc/lv_assert.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/../misc/lv_log.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/../misc/lv_mem.h
.\scontrolboard\lv_txt_ap.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/../misc/lv_math.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/../misc/lv_anim.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/../misc/lv_style_gen.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/lv_img_decoder.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/lv_img_buf.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/../misc/lv_fs.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/lv_img_cache.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/lv_draw_rect.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/sw/lv_draw_sw_gradient.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/sw/lv_draw_sw_dither.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/sw/../../core/lv_obj_pos.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/lv_draw_label.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/lv_draw_img.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/lv_draw_line.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/lv_draw_triangle.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/lv_draw_arc.h
.\scontrolboard\lv_txt_ap.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/lv_draw_mask.h
