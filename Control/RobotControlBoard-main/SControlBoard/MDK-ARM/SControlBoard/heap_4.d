.\scontrolboard\heap_4.o: ../Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c
.\scontrolboard\heap_4.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\scontrolboard\heap_4.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h
.\scontrolboard\heap_4.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stddef.h
.\scontrolboard\heap_4.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\scontrolboard\heap_4.o: ../Core/Inc/FreeRTOSConfig.h
.\scontrolboard\heap_4.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h
.\scontrolboard\heap_4.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h
.\scontrolboard\heap_4.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h
.\scontrolboard\heap_4.o: ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h
.\scontrolboard\heap_4.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h
.\scontrolboard\heap_4.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/task.h
.\scontrolboard\heap_4.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/list.h
