.\scontrolboard\lv_font.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\font\lv_font.c
.\scontrolboard\lv_font.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\font\lv_font.h
.\scontrolboard\lv_font.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\font\../lv_conf_internal.h
.\scontrolboard\lv_font.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\scontrolboard\lv_font.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\font\../lv_conf_kconfig.h
.\scontrolboard\lv_font.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\font\../../../lv_conf.h
.\scontrolboard\lv_font.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stddef.h
.\scontrolboard\lv_font.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
.\scontrolboard\lv_font.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\font\lv_symbol_def.h
.\scontrolboard\lv_font.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\font\../misc/lv_area.h
.\scontrolboard\lv_font.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\font\../misc/lv_utils.h
.\scontrolboard\lv_font.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\font\../misc/lv_log.h
.\scontrolboard\lv_font.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\font\../misc/lv_types.h
.\scontrolboard\lv_font.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\font\../misc/lv_assert.h
.\scontrolboard\lv_font.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\font\../misc/lv_mem.h
.\scontrolboard\lv_font.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
