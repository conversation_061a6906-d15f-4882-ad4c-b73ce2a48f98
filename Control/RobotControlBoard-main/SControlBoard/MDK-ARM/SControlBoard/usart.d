.\scontrolboard\usart.o: ../Core/Src/usart.c
.\scontrolboard\usart.o: ../Core/Inc/usart.h
.\scontrolboard\usart.o: ../Core/Inc/main.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal.h
.\scontrolboard\usart.o: ../Core/Inc/stm32h7xx_hal_conf.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_rcc.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_def.h
.\scontrolboard\usart.o: ../Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h7xx.h
.\scontrolboard\usart.o: ../Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h7b0xx.h
.\scontrolboard\usart.o: ../Drivers/CMSIS/Include/core_cm7.h
.\scontrolboard\usart.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\scontrolboard\usart.o: ../Drivers/CMSIS/Include/cmsis_version.h
.\scontrolboard\usart.o: ../Drivers/CMSIS/Include/cmsis_compiler.h
.\scontrolboard\usart.o: ../Drivers/CMSIS/Include/cmsis_armcc.h
.\scontrolboard\usart.o: ../Drivers/CMSIS/Include/mpu_armv7.h
.\scontrolboard\usart.o: ../Drivers/CMSIS/Device/ST/STM32H7xx/Include/system_stm32h7xx.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h
.\scontrolboard\usart.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stddef.h
.\scontrolboard\usart.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_rcc_ex.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_gpio.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_gpio_ex.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_dma.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_dma_ex.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_mdma.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_exti.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_cortex.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_adc.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_ll_adc.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_adc_ex.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_fdcan.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_flash.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_flash_ex.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_hsem.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_i2c.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_i2c_ex.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_lptim.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_ospi.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_pwr.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_pwr_ex.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_rng.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_rng_ex.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_spi.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_spi_ex.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_tim.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_tim_ex.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_uart.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_uart_ex.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_pcd.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_ll_usb.h
.\scontrolboard\usart.o: ../Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_pcd_ex.h
.\scontrolboard\usart.o: ../Drivers/BSP/Inc/bsp_uart.h
.\scontrolboard\usart.o: ../Drivers/BSP/Inc/bsp.h
.\scontrolboard\usart.o: ../Drivers/BSP/Inc/bsp_ringBuffer.h
.\scontrolboard\usart.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\scontrolboard\usart.o: ../Drivers/BSP/Inc/bsp_ws2812.h
