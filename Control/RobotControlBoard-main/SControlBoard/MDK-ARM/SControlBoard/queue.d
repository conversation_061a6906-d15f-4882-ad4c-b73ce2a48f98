.\scontrolboard\queue.o: ../Middlewares/Third_Party/FreeRTOS/Source/queue.c
.\scontrolboard\queue.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\scontrolboard\queue.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\scontrolboard\queue.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h
.\scontrolboard\queue.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stddef.h
.\scontrolboard\queue.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\scontrolboard\queue.o: ../Core/Inc/FreeRTOSConfig.h
.\scontrolboard\queue.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h
.\scontrolboard\queue.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h
.\scontrolboard\queue.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h
.\scontrolboard\queue.o: ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h
.\scontrolboard\queue.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h
.\scontrolboard\queue.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/task.h
.\scontrolboard\queue.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/list.h
.\scontrolboard\queue.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/queue.h
