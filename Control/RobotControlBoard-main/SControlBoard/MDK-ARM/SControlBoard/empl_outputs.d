.\scontrolboard\empl_outputs.o: ..\Middlewares\MotionDriver\eMPL-hal\eMPL_outputs.c
.\scontrolboard\empl_outputs.o: ..\Middlewares\MotionDriver\eMPL-hal\eMPL_outputs.h
.\scontrolboard\empl_outputs.o: ../Middlewares/MotionDriver/driver/include/mltypes.h
.\scontrolboard\empl_outputs.o: ../Middlewares/MotionDriver/driver/include/stdint_invensense.h
.\scontrolboard\empl_outputs.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\scontrolboard\empl_outputs.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\scontrolboard\empl_outputs.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\errno.h
.\scontrolboard\empl_outputs.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
.\scontrolboard\empl_outputs.o: ../Middlewares/MotionDriver/mllite/ml_math_func.h
.\scontrolboard\empl_outputs.o: ../Middlewares/MotionDriver/driver/include/mlmath.h
.\scontrolboard\empl_outputs.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\scontrolboard\empl_outputs.o: ../Middlewares/MotionDriver/mllite/start_manager.h
.\scontrolboard\empl_outputs.o: ../Middlewares/MotionDriver/mllite/data_builder.h
.\scontrolboard\empl_outputs.o: ../Middlewares/MotionDriver/mllite/results_holder.h
