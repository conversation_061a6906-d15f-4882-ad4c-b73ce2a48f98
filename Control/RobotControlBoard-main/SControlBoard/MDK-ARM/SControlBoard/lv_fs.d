.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\lv_fs.c
.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\lv_fs.h
.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../lv_conf_internal.h
.\scontrolboard\lv_fs.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../lv_conf_kconfig.h
.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../../../lv_conf.h
.\scontrolboard\lv_fs.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../misc/lv_assert.h
.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../misc/lv_log.h
.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../misc/lv_types.h
.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../misc/lv_mem.h
.\scontrolboard\lv_fs.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stddef.h
.\scontrolboard\lv_fs.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\lv_ll.h
.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\lv_gc.h
.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\lv_timer.h
.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/lv_img_cache.h
.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/lv_img_decoder.h
.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/lv_img_buf.h
.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/../misc/lv_color.h
.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/../misc/lv_math.h
.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/../misc/lv_area.h
.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../draw/lv_draw_mask.h
.\scontrolboard\lv_fs.o: ..\Middlewares\LVGL\LVGL.Simulator\lvgl\src\misc\../core/lv_obj_pos.h
