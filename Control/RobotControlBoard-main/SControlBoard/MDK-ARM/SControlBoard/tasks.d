.\scontrolboard\tasks.o: ../Middlewares/Third_Party/FreeRTOS/Source/tasks.c
.\scontrolboard\tasks.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\scontrolboard\tasks.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\scontrolboard\tasks.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h
.\scontrolboard\tasks.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stddef.h
.\scontrolboard\tasks.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\scontrolboard\tasks.o: ../Core/Inc/FreeRTOSConfig.h
.\scontrolboard\tasks.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h
.\scontrolboard\tasks.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h
.\scontrolboard\tasks.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h
.\scontrolboard\tasks.o: ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h
.\scontrolboard\tasks.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h
.\scontrolboard\tasks.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/task.h
.\scontrolboard\tasks.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/list.h
.\scontrolboard\tasks.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h
.\scontrolboard\tasks.o: ../Middlewares/Third_Party/FreeRTOS/Source/include/stack_macros.h
