/*
 $License:
    Copyright (C) 2011 InvenSense Corporation, All Rights Reserved.
    See included License.txt for License information.
 $
 */
/*******************************************************************************
 *
 * $Id: mlmath.c 5629 2011-06-11 03:13:08Z mcaramello $
 *
 *******************************************************************************/

#include <math.h>

double ml_asin(double x)
{
    return asin(x);
}

double ml_atan(double x)
{
    return atan(x);
}

double ml_atan2(double x, double y)
{
    return atan2(x, y);
}

double ml_log(double x)
{
    return log(x);
}

double ml_sqrt(double x)
{
    return sqrt(x);
}

double ml_ceil(double x)
{
    return ceil(x);
}

double ml_floor(double x)
{
    return floor(x);
}

double ml_cos(double x)
{
    return cos(x);
}

double ml_sin(double x)
{
    return sin(x);
}

double ml_acos(double x)
{
    return acos(x);
}

double ml_pow(double x, double y)
{
    return pow(x, y);
}
