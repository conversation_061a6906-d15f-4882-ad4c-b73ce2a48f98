/**
 * @file shell_port.c
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 0.1
 * @date 2019-02-22
 * 
 * @copyright (c) 2019 Letter
 * 
 */

#include "FreeRTOS.h"
#include "task.h"
#include "shell.h"
#include "stm32h7xx_hal.h"
#include "bsp_print.h"
#include "log.h"
#include "shell_port.h"
#include "semphr.h"
#include <stdbool.h>
#include "bsp_uart.h"

void uartLogWrite(char *buffer, short len);


Shell shell;
Log	shellLog;
char shellBuffer[512];


/**
 * @brief 用户shell写
 * 
 * @param data 数据
 * @param len 数据长度
 * 
 * @return short 实际写入的数据长度
 */
short UserShellWrite(char *data, unsigned short len)
{
//    HAL_UART_Transmit(&UART_DEBUG_Handle, (uint8_t *)data, len,0x1FF);
    UART1_RingBufferWrite((uint8_t *)data, len);
//    HAL_UART_Transmit_DMA(&UART_DEBUG_Handle, (uint8_t *)data,len);
    return len;
}


/**
 * @brief 用户shell读,当使用中断接收数据时可不初始化此函数，
 *        在中断中调用shellHandler(&shell, readBufferRX[i])即可。
 * @param data 数据
 * @param len 数据长度
 * @return short 实际读取到
 */
short UserShellRead(char *data, unsigned short len)
{
   return HAL_UART_Receive(&UART_DEBUG_Handle, (uint8_t *)data, len,HAL_MAX_DELAY); 
}



/**
 * @brief 用户shell解锁
 * 
 * @param shell shell
 * 
 * @return int 0
 */
void UserLogWrite(char *buffer, short len)
{
    HAL_UART_Transmit(&UART_DEBUG_Handle, (uint8_t *)buffer, len,0x1FF);
}


/**
 * @brief 用户shell初始化
 * 
 */
void userShellInit(void)
{
    shell.write = UserShellWrite;
    shellLog.write = UserLogWrite; 
    shellLog.active = true;
	shellLog.level = LOG_ALL;

    shellInit(&shell, shellBuffer, 512);
     
    logRegister(&shellLog, &shell);
    logInfo(" Success open the shell ");
}
//CEVENT_EXPORT(EVENT_INIT_STAGE2, userShellInit);

