/*! ----------------------------------------------------------------------------
 * @file    RobotConfig.h
 * 
 * @version V1.00 
 *
 * @brief   无人车配置文件
 *          在此文件中配置无人车的参数。
 *
 * @Modify
 *
 *      版本号     日期      作者      说明
 *      V1.00   2022/08/02   DSS      发布
 *
 */
#ifndef __ROBOTCONFIG_H__
#define __ROBOTCONFIG_H__


/* 电机驱动的头文件 */
#include "bsp_motor.h"

/* 采用常规麦克纳姆轮 */
//#define USE_NORMAL_MECANUM
///* 采用非对称麦克纳姆轮 */
#define USE_ASYMMETRIC_MECANUM


/* 定义电机最大输出的PWM */
#define MAX_MOTOR_PWM   MOTOR_TIM_PERIOD

/* 定义麦克纳姆轮直径，单位m */
#define MECANUM_WHELL_75 0.075f 
#define MECANUM_WHELL_60 0.075f 

#ifdef USE_NORMAL_MECANUM

/* 定义轮子到X轴的距离 单位m */
#define WheelToX 0.085  //买的小车
//#define WheelToX 0.095  //自己做的小车，大尺寸
//#define WheelToX 0.095  //非对称小车，买的
/* 定义轮子到Y轴的距离 单位m */
#define WheelToY 0.093  //买的小车
//#define WheelToY 0.075  //自己做的小车，大尺寸

/* 定义机器人向前行驶1m编码器的数值 */
//#define RORWARD_ROTARION_ENCODER_1M    ONE_CIRCLE_ENCODER/(MECANUM_WHELL_75*3.1415926f) /* 公式 */
#define RORWARD_ROTARION_ENCODER_1M 254648
/* 定义机器人向右行驶1m编码器的数值 */                                                                                                                                                                                                                                                                                     
#define RIGHT_ROTARION_ENCODER_1M 254648

/* 定义机器人顺时针旋转一圈360度需要设的比例 */
//#define CLOCKWISE_ROTARION_360  2*ONE_CIRCLE_ENCODER*sqrt((WheelToX*WheelToX+WheelToY*WheelToY))/MECANUM_WHELL_75
#define CLOCKWISE_ROTARION_360 4300
#endif

#ifdef USE_ASYMMETRIC_MECANUM

//#define WheelToX 0.063f             /* 麦轮到X轴的距离 */ 
//#define WheelToY_LEFT_UP 0.076f     /* 左上麦轮到Y轴的距离 */ 
//#define WheelToY_LEFT_DOWN 0.076f   /* 左下麦轮到Y轴的距离 */ 
//#define WheelToY_RIGHT_UP 0.026f    /* 右上麦轮到Y轴的距离 */ 
//#define WheelToY_RIGHT_DOWN 0.026f  /* 右下麦轮到Y轴的距离 */ 

#define WheelToX 0.067f              /* 麦轮到X轴的距离 */ 
#define WheelToY_LEFT_UP 0.047f     /* 左上麦轮到Y轴的距离 */ 
#define WheelToY_LEFT_DOWN 0.047f   /* 左下麦轮到Y轴的距离 */ 
#define WheelToY_RIGHT_UP 0.076f    /* 右上麦轮到Y轴的距离 */ 
#define WheelToY_RIGHT_DOWN 0.076f  /* 右下麦轮到Y轴的距离 */

/* 定义机器人向前行驶1m编码器的数值 */
//#define RORWARD_ROTARION_ENCODER_1M    ONE_CIRCLE_ENCODER/(MECANUM_WHELL_75*3.1415926f) /* 公式 */
/* 定义机器人向前行驶1m编码器的数值 */
#define RORWARD_ROTARION_ENCODER_1M 318310
/* 定义机器人向右行驶1m编码器的数值 */                                                                                                                                                                                                                                                                                     
#define RIGHT_ROTARION_ENCODER_1M 318310

/* 定义机器人顺时针旋转一圈360度需要设的比例 */
//#define CLOCKWISE_ROTARION_360  2*ONE_CIRCLE_ENCODER*sqrt((WheelToX*WheelToX+WheelToY*WheelToY))/MECANUM_WHELL_75
#define CLOCKWISE_ROTARION_360 4300
#endif




#endif
 



