/*! ----------------------------------------------------------------------------
 * @file    bsp.h
 * 
 * @version V1.00 
 *
 * @brief   板级驱动总头文件
 *          文件中包含了板级驱动全局数据
 *
 * @Modify
 *
 *      版本号     日期      作者      说明
 *      V1.00   2022/08/02   DSS      发布
 *
 */
#ifndef __MOVEPID_H__
#define __MOVEPID_H__



/* 定义pid结构体 */
typedef struct 
{
	float kp;
	float ki;
	float kd;
} PID_t;

/* 外部变量声明 */
extern PID_t MotorSpeedPID;
extern PID_t SpinPID;
extern PID_t DisPID;
extern PID_t MoveYawPID;

/* 函数声明 */
int PositionPIDtoSpd_Motor1(int deviation,PID_t pid);
int PositionPIDtoSpd_Motor2(int deviation,PID_t pid);
int PositionPIDtoSpd_Motor3(int deviation,PID_t pid);
int PositionPIDtoSpd_Motor4(int deviation,PID_t pid);
int PositionPIDToSpin(float deviation,float nowEncodeYawSpeed,PID_t pid);
int PositionPIDToDis(float deviation,PID_t pid);
float PositionPIDToYaw(float deviation,PID_t pid);

#endif
 



