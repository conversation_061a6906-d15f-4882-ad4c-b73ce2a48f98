#ifndef _REMOTE_CONTROL_H__
#define _REMOTE_CONTROL_H__

#include <stdint.h>

/* 定义使用哪种遥控器 */
#define AMOVLAB 1               //使用阿木实验室的遥控器
//#define YELLOW_CONTROL 1        //使用黄色破的遥控器




#ifdef AMOVLAB
typedef struct
{
    float vx;           /* x轴速度 */
    float vy;           /* y轴速度 */
    float wz;           /* z轴速度 */
    uint8_t siwtch_C;   /* 开关C,上中下三个键值 */
    uint8_t siwtch_D;   /* 开关D */
}RemoteData_t;
#endif

#ifdef YELLOW_CONTROL
typedef struct
{
    float vx;           /* x轴速度 */
    float vy;           /* y轴速度 */
    float wz;           /* z轴速度 */
    uint8_t siwtch_A;   /* 开关A,上下两个键值 */
    uint8_t siwtch_B;   /* 开关B,上下两个键值 */
    uint8_t siwtch_C;   /* 开关C,上中下三个键值 */
}RemoteData_t;

#endif



static RemoteData_t remoteData; 

/* 开关位置枚举类型 */
enum 
{
    DOWN = 0,   /* 拨动开关向下拨 */
    UP,         /* 拨动开关向上拨 */
    MIDDLE      /* 拨动开关处于中间 */
};

/* 函数声明 */

void RemoteControlPro_10ms(void);

#endif


