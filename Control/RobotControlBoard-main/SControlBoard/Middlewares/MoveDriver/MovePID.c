/*! ----------------------------------------------------------------------------
 * @file    MovePID.c
 * 
 * @version V1.00 
 *
 * @brief   此文件为关于无人车运动PID驱动文件
 *
 *
 * @Modify
 *
 *      版本号     日期      作者      说明
 *      V1.00   2022/08/02   DSS      发布
 *
 */
#include "MovePID.h"
#include "MoveDriverPort.h"
#include "RobotConfig.h"

/* 电机转速PID参数 */
PID_t MotorSpeedPID=
{
	.kp=4,			
	.ki=0.55,			
	.kd=0					
};

/* 小车旋转PID参数 */
PID_t SpinPID=
{
	.kp=70,		//80	
	.ki=0,			
	.kd=0					
};

/* 小车距离PID参数 */
PID_t DisPID=
{
	.kp=6000,			
	.ki=0,			
	.kd=0					
};

/* 小车速度控制引入YAW角控制PID参数 */
PID_t MoveYawPID=
{
	.kp=3.5f,			
	.ki=0,			
	.kd=1					
};

/**
 * @brief: 使用位置式PID控制器控制电机速度，对积分值进行限幅
 * @param[in] deviation: 和目标值得偏差     
 * @param[in] 位置式pid的参数
 * @retval: 调节占空比的一个整形数
 */
int PositionPIDtoSpd_Motor1(int deviation,PID_t pid)
{
	static float Bias,PWM,Integral_bias,Last_Bias,pwmKI=0;
    /* 计算偏差 */
	Bias = deviation; 
    
    /* 求出偏差的积分 */
	Integral_bias+=Bias;
    
	pwmKI=pid.ki*Integral_bias;
    /* 限幅 */
	if(pwmKI>MAX_MOTOR_PWM) Integral_bias=MAX_MOTOR_PWM/pid.ki;
	else if(pwmKI<-MAX_MOTOR_PWM) Integral_bias=-MAX_MOTOR_PWM/pid.ki;
    /* 位置式PID控制器 */
	PWM=pid.kp*Bias+pwmKI+pid.kd*(Bias-Last_Bias);  
    /* PWM限幅 */
    if(PWM>MAX_MOTOR_PWM) PWM = MAX_MOTOR_PWM;
    else if(PWM<-MAX_MOTOR_PWM) PWM = -MAX_MOTOR_PWM;
    /* 保存上一次偏差  */  
	Last_Bias=Bias; 
    
	return PWM;    
}

/**
 * @brief: 使用位置式PID控制器控制电机速度，对积分值进行限幅
 * @param[in] deviation: 和目标值得偏差
 * @param[in] pid: 位置式pid的参数
 * @retval: 调节占空比的一个整形数
 */
int PositionPIDtoSpd_Motor2(int deviation,PID_t pid)
{
	static float Bias,PWM,Integral_bias,Last_Bias,pwmKI=0;
    /* 计算偏差 */
	Bias = deviation; 
    
    /* 求出偏差的积分 */
	Integral_bias+=Bias;
    
	pwmKI=pid.ki*Integral_bias;
    /* 限幅 */
	if(pwmKI>MAX_MOTOR_PWM) Integral_bias=MAX_MOTOR_PWM/pid.ki;
	else if(pwmKI<-MAX_MOTOR_PWM) Integral_bias=-MAX_MOTOR_PWM/pid.ki;
    /* 位置式PID控制器 */
	PWM=pid.kp*Bias+pwmKI+pid.kd*(Bias-Last_Bias);  
    /* PWM限幅 */
    if(PWM>MAX_MOTOR_PWM) PWM = MAX_MOTOR_PWM;
    else if(PWM<-MAX_MOTOR_PWM) PWM = -MAX_MOTOR_PWM;
    /* 保存上一次偏差  */  
	Last_Bias=Bias; 
    
	return PWM;    
}

/**
 * @brief: 使用位置式PID控制器控制电机速度，对积分值进行限幅
 * @param[in] deviation: 和目标值得偏差
 * @param[in] pid: 位置式pid的参数
 * @retval: 调节占空比的一个整形数
 */
int PositionPIDtoSpd_Motor3(int deviation,PID_t pid)
{
	static float Bias,PWM,Integral_bias,Last_Bias,pwmKI=0;
    /* 计算偏差 */
	Bias = deviation;
    
    /* 求出偏差的积分 */
	Integral_bias+=Bias;
    
	pwmKI=pid.ki*Integral_bias;
    /* 限幅 */
	if(pwmKI>MAX_MOTOR_PWM) Integral_bias=MAX_MOTOR_PWM/pid.ki;
	else if(pwmKI<-MAX_MOTOR_PWM) Integral_bias=-MAX_MOTOR_PWM/pid.ki;
    /* 位置式PID控制器 */
	PWM=pid.kp*Bias+pwmKI+pid.kd*(Bias-Last_Bias);  
    /* PWM限幅 */
    if(PWM>MAX_MOTOR_PWM) PWM = MAX_MOTOR_PWM;
    else if(PWM<-MAX_MOTOR_PWM) PWM = -MAX_MOTOR_PWM;
    /* 保存上一次偏差  */  
	Last_Bias=Bias; 
    
	return PWM;    
}

/**
 * @brief 使用位置式PID控制器控制电机速度，对积分值进行限幅
 * @param[in] deviation: 和目标值得偏差
 * @param[in] pid: 位置式pid的参数
 * @retval: 调节占空比的一个整形数
 */
int PositionPIDtoSpd_Motor4(int deviation,PID_t pid)
{
	static float Bias,PWM,Integral_bias,Last_Bias,pwmKI=0;
    /* 计算偏差 */
	Bias = deviation; 
    
    /* 求出偏差的积分 */
	Integral_bias+=Bias;
    
	pwmKI=pid.ki*Integral_bias;
    /* 限幅 */
	if(pwmKI>MAX_MOTOR_PWM) Integral_bias=MAX_MOTOR_PWM/pid.ki;
	else if(pwmKI<-MAX_MOTOR_PWM) Integral_bias=-MAX_MOTOR_PWM/pid.ki;
    /* 位置式PID控制器 */
	PWM=pid.kp*Bias+pwmKI+pid.kd*(Bias-Last_Bias);  
    /* PWM限幅 */
    if(PWM>MAX_MOTOR_PWM) PWM = MAX_MOTOR_PWM;
    else if(PWM<-MAX_MOTOR_PWM) PWM = -MAX_MOTOR_PWM;
    /* 保存上一次偏差  */  
	Last_Bias=Bias; 
    
	return PWM;    
}

/**
 * @brief 使用位置式PID控制器小车旋转到目标角度，以编码器测得的角速度值作为反馈
 * @param[in] deviation 与目标值的偏差
 * @param[in] nowEncodeYawSpeed 当前编码器测得的yawSpeed
 * @param[in] pid: 位置式pid的参数
 * @return 调节占空比的一个整形数
 */
int PositionPIDToSpin(float deviation,float nowEncodeYawSpeed,PID_t pid)
{
	float Position_KP=pid.kp,Position_KI=pid.ki,Position_KD=pid.kd;
	static float Bias,Pwm,Integral_bias,Last_Bias;
    /* 计算偏差 */
	Bias=deviation;          
    /* 求出偏差的积分 */    
	Integral_bias+=Bias;
    /* 位置式PID控制器 */
//	Pwm=Position_KP*Bias+Position_KI*Integral_bias+Position_KD*nowEncodeYawSpeed;
    Pwm=Position_KP*Bias+Position_KI*Integral_bias+Position_KD*(Bias-Last_Bias);
    /* PWM限幅 */
    if(Pwm>MAX_MOTOR_PWM) Pwm = MAX_MOTOR_PWM;
    else if(Pwm<-MAX_MOTOR_PWM) Pwm = -MAX_MOTOR_PWM;    
    /* 保存上一次偏差  */  
	Last_Bias=Bias; 
	return Pwm;    
}

/**
 * @brief 使用位置式PID控制器控制小车前进/后退目标距离
 * @param[in] deviation 与目标值的偏差
 * @param[in] nowEncodeYawSpeed 当前编码器测得的yawSpeed
 * @param[in] pid: 位置式pid的参数
 * @return 调节占空比的一个整形数
 */
int PositionPIDToDis(float deviation,PID_t pid)
{
	float Position_KP=pid.kp,Position_KI=pid.ki,Position_KD=pid.kd;
	static float Bias,Pwm,Integral_bias,Last_Bias;
	Bias=deviation;                         		         
	Integral_bias+=Bias;	                                
	Pwm=Position_KP*Bias+Position_KI*Integral_bias+Position_KD*(Bias-Last_Bias); 
	Last_Bias=Bias;
	return Pwm;
}

/**
 * @brief 使用位置式PID控制器控制移动时航向恒定
 * @param[in] deviation 与目标值的偏差
 * @param[in] pid: 位置式pid的参数
 * @return 调节占空比的一个整形数
 */
float PositionPIDToYaw(float deviation,PID_t pid)
{
	float Position_KP=pid.kp,Position_KI=pid.ki,Position_KD=pid.kd;
	static float Bias,Pwm,Integral_bias,Last_Bias;
	Bias=deviation;                         		         
	Integral_bias+=Bias;	                                
	Pwm=Position_KP*Bias+Position_KI*Integral_bias+Position_KD*(Bias-Last_Bias); 
	Last_Bias=Bias;
	return Pwm;
}
