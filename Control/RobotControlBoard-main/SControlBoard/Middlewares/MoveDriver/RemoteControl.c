/*! ----------------------------------------------------------------------------
 * @file    remote_control.c
 * 
 * @version V1.00 
 *
 * @brief   此文件为遥控器控制小车的程序，由航模遥控器遥控底层驱动文件为bsp_rc.c。
 *
 *          使用步骤：
 *          1.将RemoteControlPro_10ms()函数放入10ms周期执行函数中
 *          2.将遥控器的A开关往上拨动
 *          3.开始遥控
 *
 * @Modify
 *
 *      版本号     日期      作者      说明
 *      V1.00   2022/11/29   DSS      发布
 *
 */
#include "bsp_rc.h"
#include "RemoteControl.h"
#include "RobotMove.h"
#include "bsp_print.h"
#include "bsp_motor.h"
#include "math.h"

/* 如果定义使用黄色的遥控器 */
#ifdef YELLOW_CONTROL

/**
  * @brief  获取遥控器的数据
  * @param  None
  * @retval None
  */
RemoteData_t GetRemoteData(void)
{
    RemoteData_t remoteData;
    /* 获取A开关键值 */
    if(Get_RC_Data().aux4 == 1694)
    {
        remoteData.siwtch_A = DOWN;
    }
    else if(Get_RC_Data().aux4 == 306)
    {
        remoteData.siwtch_A = UP;
    }
    /* 获取B开关键值 */
    if(Get_RC_Data().aux5 == 1931)
    {
        remoteData.siwtch_B = DOWN;
    }
    else if(Get_RC_Data().aux5 == 1008)
    {
        remoteData.siwtch_B = MIDDLE;
    }
    else if(Get_RC_Data().aux5 == 352)
    {
        remoteData.siwtch_B = UP;
    }
    /* 获取C开关键值 */
    if(Get_RC_Data().aux9 == 1008)
    {
        remoteData.siwtch_B = 0;
    }
    else if(Get_RC_Data().aux9 == 1931)
    {
        remoteData.siwtch_B = 1;
    }
    /* vy */
    if((Get_RC_Data().aux0 - 962) >0 )
    {
        remoteData.vy = (Get_RC_Data().aux0 - 961)/720.0;
    }
    else 
    {
        remoteData.vy = (Get_RC_Data().aux0 - 961)/650.0;
    }
    /* vx */
    if((Get_RC_Data().aux1 - 962) >0 )
    {
        remoteData.vx = (Get_RC_Data().aux1 - 1078)/683.0;
    }
    else 
    {
        remoteData.vx = (Get_RC_Data().aux1 - 1078)/702.0;
    }
    /* wz */
    if((Get_RC_Data().aux3 - 961) >0 )
    {
        remoteData.wz = -(Get_RC_Data().aux3 - 1025)/724.0*365;
    }
    else 
    {
        remoteData.wz = -(Get_RC_Data().aux3 - 1025)/662.0*365;
    }
    return remoteData;
}

/**
  * @brief  遥控器执行函数,需10ms周期调用
  * @param  None
  * @retval None
  */
void RemoteControlPro_10ms(void)
{
    static uint8_t lastSwitchKey = 100;
    uint8_t nowSwitchKey = 0;
    remoteData = GetRemoteData();
    nowSwitchKey = remoteData.siwtch_A;
    /* 如果遥控开关A拨动变化从上拨状态转换到下拨状态，则将此时控制模式转换为遥控模式 */
    if(nowSwitchKey == UP&&lastSwitchKey == DOWN)
    {
        RobotSetControlMode(REMOTE_CONTROL);
    }
    /* 如果遥控开关A拨动变化从下拨状态转换到上拨状态，则将此时控制模式转换为电机控制模式，且将电机PWM设置为0，避免电机响 */
    else if(nowSwitchKey == DOWN&&lastSwitchKey == UP)
    {
        RobotSetControlMode(MOTOR_CONTROL);
        SetMotorPWM(LeftUp,0);
        SetMotorPWM(LeftDown,0);
        SetMotorPWM(RightUp,0);
        SetMotorPWM(RightDown,0);
    }
    if(RobotGetControlMode()==REMOTE_CONTROL)
    {
        RobotMove(remoteData.vx,remoteData.vy,remoteData.wz);
    }
    /* 储存上一次的开关量 */
    lastSwitchKey = remoteData.siwtch_A;
}
#endif

/* 如果定义使用阿木实验室的遥控器 */
#ifdef AMOVLAB 
/**
  * @brief  获取遥控器的数据
  * @param  None
  * @retval None
  */
RemoteData_t GetRemoteData(void)
{
    RemoteData_t remoteData;
    
    /* 获取C开关键值 */
    if(Get_RC_Data().aux4 == 1807)
    {
        remoteData.siwtch_C = DOWN;
    }
    else if(Get_RC_Data().aux4 == 1024)
    {
        remoteData.siwtch_C = MIDDLE;
    }
    else if(Get_RC_Data().aux4 == 240)
    {
        remoteData.siwtch_C = UP;
    }
    /* 获取D开关键值 */
    if(Get_RC_Data().aux5 == 240)
    {
        remoteData.siwtch_D = UP;
    }
    else if(Get_RC_Data().aux5 == 1807)
    {
        remoteData.siwtch_D = DOWN;
    }
    /* vy */
    if((Get_RC_Data().aux3) <= 1807 &&(Get_RC_Data().aux3) >= 240)
    {
         remoteData.vy = (Get_RC_Data().aux3 - 1024)/783.0;
    }
    /* vx */
    if((Get_RC_Data().aux2) <= 1807 &&(Get_RC_Data().aux2) >= 240)
    {
        remoteData.vx = (Get_RC_Data().aux2 - 1024)/783.0;
    }
    /* wz */
    if((Get_RC_Data().aux1) <= 1807 &&(Get_RC_Data().aux1) >= 240)
    {		
        remoteData.wz = (Get_RC_Data().aux1 - 1024)/783.0*365;
    }
    return remoteData;
}

/**
  * @brief  遥控器执行函数,需10ms周期调用
  * @param  None
  * @retval None
  */
void RemoteControlPro_10ms(void)
{
    static uint8_t lastSwitchKey = 100;
    uint8_t nowSwitchKey = 0;
    remoteData = GetRemoteData();
    nowSwitchKey = remoteData.siwtch_C;
	
    /* 如果遥控开关C拨动变化从上拨状态转换到中间拨动拨状态，则将此时控制模式转换为遥控模式 */
    if(nowSwitchKey == MIDDLE&&lastSwitchKey == UP)
    {
        RobotSetControlMode(REMOTE_CONTROL);
    }
    /* 如果遥控开关A拨动变化从中间拨动拨状态转换到上拨状态，则将此时控制模式转换为电机控制模式，且将电机PWM设置为0，避免电机响 */
    else if(nowSwitchKey == UP&&lastSwitchKey == MIDDLE)
    {
        RobotSetControlMode(MOTOR_CONTROL);
        SetMotorPWM(LeftUp,0);
        SetMotorPWM(LeftDown,0);
        SetMotorPWM(RightUp,0);
        SetMotorPWM(RightDown,0);
    }
    /* 如果当前模式为遥控模式 */
    if(RobotGetControlMode()==REMOTE_CONTROL)
    {
        RobotMove(remoteData.vx,remoteData.vy,remoteData.wz);
    }
    
    /* 储存上一次的开关量 */
    lastSwitchKey = remoteData.siwtch_C;
}

#endif


