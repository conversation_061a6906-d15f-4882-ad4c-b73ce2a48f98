/*! ----------------------------------------------------------------------------
 * @file    MoveDriverPort.c
 * 
 * @version V1.00 
 *
 * @brief   无人车移动接口文件。
 *          接口文件，用于实现电机驱动接口等函数，需用户实现。
 *
 * @Modify
 *
 *      版本号     日期      作者      说明
 *      V1.00   2022/08/02   DSS      发布
 *
 * <AUTHOR>
 */
#include "MoveDriverPort.h"
#include "MovePID.h"
#include "bsp_motor.h"



/**
  * @brief  获取每10ms电机编码器的变化值
  * @param  None
  * @retval None
  */
RobotEncoder10ms_t GetRobotEncoder_10ms(void)
{
    RobotEncoder10ms_t robotEncode;
    robotEncode.leftUp = GetMotorState(LeftUp).EncoderCount;
    robotEncode.leftDown = GetMotorState(LeftDown).EncoderCount;
    robotEncode.rightUP = GetMotorState(RightUp).EncoderCount;
    robotEncode.rightDown = GetMotorState(RightDown).EncoderCount;
    return robotEncode;
}

/**
  * @brief  设置电机PWM调速,需用户实现
  * @param  motorPos :电机位置枚举类型
  *         定义方式：
  *         typedef enum 
  *         {
  *             LeftUp,
  *             LeftDown,
  *             RightUp,
  *             RightDown,
  *         } MotorPos_e;
  *         pwm:控制电机转速占空比，范围（-9999 - +9999）
  *         <0 :电机反转 >0：电机正转   
  *         如实际效果与说明相反， 将 MOTOR_PWM_TURN 宏置1即可
  * @retval None
  */
void SetRobotMotorPWM(MotorPos_e motorPos,int pwm)
{
    SetMotorPWM(motorPos,pwm);
}

