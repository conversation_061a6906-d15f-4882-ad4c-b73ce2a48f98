/*! ----------------------------------------------------------------------------
 * @file    RobotMove.c
 * 
 * @version V1.00 
 *
 * @brief   机器人移动驱动函数，用于控制无人车的移动。
 *          
 *
 * @Modify
 *
 *      版本号     日期      作者      说明
 *      V1.00   2022/08/31   DSS      发布
 *
 */
#include "RobotMove.h"
#include "MovePID.h"
#include "RobotConfig.h"
#include "MoveDriverPort.h"
#include "bsp_print.h"
#include <stdlib.h>
#include <stdio.h>
#include "math.h"
#include "sensor.h"
#include "bsp_ak89xx.h"
#include "arm_math.h"


static float  ForwardRotationEncoder_1M;    
static float  RightRotationEncoder_1M;    
static float ClockWiseRotation_360;


static float RM_kx,RM_ky,RM_kz;
/* 用于设置转速的全局变量 */
RobotStateVector_t stateVector;
static RobotOdometry_t RobotOdometry;
static int leftUpPWM,leftDownPWM,rightUpPWM,rightDownPWM;

/* 设置目标旋转角度的全局变量 */
float targetYaw = 0;

/* 设置目标位置全局变量 */
static float targetPosX,targetPosY;

/* 此标志位置时开启移动控制处理程序，为0时关闭移动控制处理程序 */
static uint8_t RobtoControlMode = MOVE_CONTROL;

/* 机器人投喂计时变量 */
uint32_t FeedRobotTimeCount = 0;

/* 电机转速PID */
PID_t Speedxxxdate=
{
	.kp=4,			
	.ki=0.55,			
	.kd=0			
};

/** 
 * @brief 机器人投喂函数,如果超时投喂则小车停止
 * @param None
 * @return None
 */
void FeedRobot(void)
{
    FeedRobotTimeCount = 0;
}

/** 
 * @brief 机器人投喂处理函数，需放入10ms定时器中处理。实现不发指令给小车，小车停止。
 * @param  None
 * @return None
 */
void FeedRobotPro_10ms(void)
{
    #if ENABLE_ROBOT_FEED
    if(RobotGetControlMode() == MOVE_CONTROL)
    {
        FeedRobotTimeCount++;
        if(FeedRobotTimeCount > MAX_ROBOT_FEED_TIME)
        {
            /* 将小车控制模式设置为电机转速控制模式 */
            RobotSetControlMode(MOTOR_CONTROL);
            /* 如果投喂超时将四个电机PWM设置为0 */
            SetRobotMotorPWM(LeftUp,0);
            SetRobotMotorPWM(LeftDown,0);
            SetRobotMotorPWM(RightUp,0);
            SetRobotMotorPWM(RightDown,0);
            FeedRobotTimeCount = 0;
        }
    }
    #endif
}

/** 
 * @brief 机器人移动初始化函数，用于参数初始计算
 * @param[in] None
 * @return None
 */
void RobotMoveInit(void)
{
    /* 计算小车向前行驶一米，编码器的数值 */
    ForwardRotationEncoder_1M = ONE_CIRCLE_ENCODER/(MECANUM_WHELL_75*3.1415926f); 
    /* 计算小车向左行驶一米，编码器的数值 */    
    RightRotationEncoder_1M = ONE_CIRCLE_ENCODER/(MECANUM_WHELL_75*3.1415926f);    
    /* 计算小车顺时针旋转一圈编码器的数值 */   
    
    ClockWiseRotation_360 = CLOCKWISE_ROTARION_360;
    
    /* 除以100是因为编码器采集数据的时间周期为10ms */
    RM_kx = ForwardRotationEncoder_1M/100.0f;
    RM_ky = RightRotationEncoder_1M/100.0f;
    RM_kz = ClockWiseRotation_360/100.0f;
    RobotSetControlMode(MOVE_CONTROL);
    RobotMove(0,0,0);
}


/** 
 * @brief：根据pid调节左边电机到目标速度
 * @param[in]：EncodeSpdL: 当前左电机编码器测速值
 * @param[in]：TarSpdL:左边电机目标速度,最大速度越1.19m/s
 * @return: 返回左边电机计算后的pwm占空比
 */
int ChangeEncodeSpdMotorL(void)
{
    static uint32_t count;
    int leftUpDEV;
    /* 定义电机编码器当前获取的参数值 */
    RobotEncoder10ms_t nowEncoder;
    int leftUpPWM=0;
    
    int tarspd = 2500;
    
    nowEncoder.leftUp = 0;
    
    /* 读取编码器当前值 */
    nowEncoder = GetRobotEncoder_10ms();
    
    /* 范围0-3000 */
    /* 计算编码器当前值与目标值偏差 */
    leftUpDEV    = tarspd - nowEncoder.leftUp;
    
     /* 获取目标PWM值 */
    leftUpPWM = PositionPIDtoSpd_Motor1(leftUpDEV,Speedxxxdate);

	SetRobotMotorPWM(LeftUp,leftUpPWM);
    count++;
    return 0;
}

/**
  * @brief  机器人移动函数,根据VICON坐标系设置
  * @param  Vx :机器人移动的X轴速度，单位（m/s）,机器人前为+
  * @param  Vy :机器人移动的Y轴速度，单位（m/s）,机器人左为+
  * @param  Wv :机器人的Z轴旋转速度，单位（度/s）,逆时针为+
  * @retval None
  */
void RobotMove(float Vx,float Vy,float Wv)
{
    stateVector.Vx = Vy;
    stateVector.Vy = Vx;
    stateVector.Wv = -Wv;
}

/**
  * @brief 设置机器人目标Yaw角
  * @param[in] tarYaw 目标Yaw角
  * @retval None
  */
void RobotSetTargetYaw(float tarYaw)
{
    targetYaw = tarYaw;
}

/**
  * @brief 获取机器人目标Yaw角
  * @param[in] tarYaw 目标Yaw角
  * @retval None
  */
float RobotGetTargetYaw(void)
{
    return targetYaw;
}

/**
  * @brief 设置机器人目标位置
  * @param[in] tarYaw 目标Yaw角
  * @retval None
  */
void RobotSetTargetPos(float tarX,float tarY)
{
    targetPosX = tarX;
    targetPosY = tarY;
}

/**
  * @brief  机器人获取里程计信息
  * @param  None
  * @retval 里程计数据
  */
RobotOdometry_t RobotGetOdometry(void)
{
    return RobotOdometry;
}

/**
  * @brief  机器人移动函数
  * @param  Vx :机器人移动的X轴速度，单位（m/s）,机器人前为+
  * @param  Vy :机器人移动的Y轴速度，单位（m/s）,机器人右为+
  * @param  Wv :机器人的Z轴旋转速度，单位（度/s）,顺时针为+
  * @retval 返回编码器10ms目标达到值
  */
RobotEncoderTarSpd10ms_t GetEncoderTarSpd10ms(float Vx,float Vy,float Wv)
{
    RobotEncoderTarSpd10ms_t encoderTarSpd10ms;
    /* 使用常规麦克纳姆轮 */
    #ifdef USE_NORMAL_MECANUM
    
    encoderTarSpd10ms.leftUp    = Vx*RM_kx + Vy*RM_ky + Wv*RM_kz*(WheelToX + WheelToY);
    encoderTarSpd10ms.leftDown  = Vx*RM_kx - Vy*RM_ky + Wv*RM_kz*(WheelToX + WheelToY);
    encoderTarSpd10ms.rightUP   = Vx*RM_kx - Vy*RM_ky - Wv*RM_kz*(WheelToX + WheelToY);
    encoderTarSpd10ms.rightDown = Vx*RM_kx + Vy*RM_ky - Wv*RM_kz*(WheelToX + WheelToY);
   
    #endif
    
    /* 使用非对称结麦克纳姆轮 */
    #ifdef USE_ASYMMETRIC_MECANUM
    
    encoderTarSpd10ms.leftUp    = Vx*RM_kx + Vy*RM_ky + Wv*RM_kz*(WheelToX + WheelToY_LEFT_UP);
    encoderTarSpd10ms.leftDown  = Vx*RM_kx - Vy*RM_ky + Wv*RM_kz*(WheelToX + WheelToY_LEFT_DOWN);
    encoderTarSpd10ms.rightUP   = Vx*RM_kx - Vy*RM_ky - Wv*RM_kz*(WheelToX + WheelToY_RIGHT_UP);
    encoderTarSpd10ms.rightDown = Vx*RM_kx + Vy*RM_ky - Wv*RM_kz*(WheelToX + WheelToY_RIGHT_DOWN);
    
    #endif
    return encoderTarSpd10ms;
}


/**
  * @brief  设置机器人控制模式
  * @param  mode 控制模式 来自 RobotControlMode_e 枚举类型
  * @retval None
  */
void RobotSetControlMode(uint8_t mode)
{  
    RobtoControlMode = mode;
}

/**
  * @brief 获取机器人控制模式
  * @param  None
  * @retval 当前机器人控制模式 来自 RobotControlMode_e
  */
uint8_t RobotGetControlMode(void)
{  
    return RobtoControlMode;
}

/**
  * @brief  获取当前电机的PWM占空比输出值
  * @param  None
  * @retval None
  */
RobotMotorPwm_t GetMotorPWM(void)
{
    RobotMotorPwm_t MotorPwm;
    MotorPwm.leftUp = leftUpPWM*100/(float)MOTOR_TIM_PERIOD;
    MotorPwm.leftDown = leftDownPWM*100/(float)MOTOR_TIM_PERIOD;
    MotorPwm.rightUP = rightUpPWM*100/(float)MOTOR_TIM_PERIOD;
    MotorPwm.rightDown = rightDownPWM*100/(float)MOTOR_TIM_PERIOD;
    return MotorPwm;
}

float nowYaw = 0,yawError = 0, yawDev = 0;
float MiddleTarVx = 0, MiddleTarVy = 0 ,MiddleTarVz = 0;
/**
  * @brief  机器人控制处理程序,每10ms处理一次
  * @param  None
  * @retval None
  */
void RobotControlPro_10ms(void)
{
    /* 移动控制模式 */
    if(RobotGetControlMode() == MOVE_CONTROL)
    {
        /* 定义电机编码器数值与目标值偏差 */
        int leftUpDEV = 0,leftDownDEV = 0,rightUpDEV = 0,rightDownDEV = 0;
        /* 定义编码器10ms应该达到的值 */
        RobotEncoderTarSpd10ms_t tarSpd; 
        /* 定义电机编码器当前获取的参数值 */
        RobotEncoder10ms_t nowEncoder;
        /* 定义中间速度目标值 */
		MiddleTarVx = stateVector.Vx;
		MiddleTarVy = stateVector.Vy;
        /* 进行角速度积分得到目标角度 */
        if((stateVector.Wv >= 5.0f) || (stateVector.Wv <= -5.0f))
        {
            targetYaw += stateVector.Wv * 0.01f;
            if(targetYaw <= -180)
            {
                targetYaw = targetYaw + 360;
            }
            if(targetYaw >= 180)
            {
                targetYaw = targetYaw - 360;
            }
        }  
        
        /* 获取当前YAW角数据,根据是否收到VICON数据判断YAW角来自IMU还是VICON */
        if(Sensor_GetStatus().VICON_Status == SENSOR_OK)
        {
            nowYaw = -Sensor_GetVICON().AngleZ * 57.3f;
        }
        else
        {
            nowYaw = Sensor_GetIMU().yaw;
        }  
        
        /* 计算与目标值偏差 */
        yawError = targetYaw - nowYaw;
        
        /* 逆时针旋转偏差为正值，顺时针旋转偏差为负值 */
        if(yawError < -180)  
        {
            yawDev = 360 + yawError;
        }
        else if(yawError > 180) 
        {
            yawDev = yawError - 360;
        }
        else
        {
            yawDev = yawError;
        }
        
        MiddleTarVz = PositionPIDToYaw(yawDev,MoveYawPID);
        
        /* 计算目标编码器速度值 */ 
        tarSpd = GetEncoderTarSpd10ms(MiddleTarVx,MiddleTarVy,MiddleTarVz);
        
        /* 读取编码器当前值 */  
        nowEncoder = GetRobotEncoder_10ms();
        
        /* 计算编码器当前值与目标值偏差 */
        leftUpDEV    = tarSpd.leftUp - nowEncoder.leftUp;
        leftDownDEV  = tarSpd.leftDown - nowEncoder.leftDown;
        rightUpDEV   = tarSpd.rightUP - nowEncoder.rightUP;
        rightDownDEV = tarSpd.rightDown - nowEncoder.rightDown;  
        
        /* 获取目标PWM值 */
        leftUpPWM = PositionPIDtoSpd_Motor1(leftUpDEV,MotorSpeedPID);
        leftDownPWM = PositionPIDtoSpd_Motor2(leftDownDEV,MotorSpeedPID);
        rightUpPWM = PositionPIDtoSpd_Motor3(rightUpDEV,MotorSpeedPID);
        rightDownPWM = PositionPIDtoSpd_Motor4(rightDownDEV,MotorSpeedPID);

        /* 设置电机PWM输出值 */
        SetRobotMotorPWM(LeftUp,leftUpPWM);
        SetRobotMotorPWM(LeftDown,leftDownPWM);
        SetRobotMotorPWM(RightUp,rightUpPWM);
        SetRobotMotorPWM(RightDown,rightDownPWM);
    }
    /* 位置控制模式 */
    else if(RobotGetControlMode()==POS_CONTROL)
    {
        float yawDev,disDev;
        float nowAngle,nowEncodeYawSpeed;
        int yawPWM,disPWM;
        
        /* 获取当前IMU的角度值 */
        nowAngle = Sensor_GetIMU().yaw;
        
        /* 计算与目标值偏差 */
        yawDev = nowAngle - targetYaw;
        
        /* 逆时针旋转偏差为正值，顺时针旋转偏差为负值 */
        if(yawDev<-180)  yawDev = -yawDev-360;
        else if(yawDev>180)  yawDev = 360-yawDev;
        
        /* 获取当前编码器计算的yaw角旋转速度 */
        nowEncodeYawSpeed = RobotGetOdometry().yawSpeed;

        /* 获取yawPWM输出值 */
        yawPWM = PositionPIDToSpin(yawDev,nowEncodeYawSpeed,SpinPID);
        
        /* 计算距离差 */
        disDev = sqrt(pow((targetPosX - Sensor_GetOdometry().disX_UCS),2)+ pow((targetPosY - Sensor_GetOdometry().disY_UCS),2));
        
        /* 获取disPWM输出值 */
        disPWM = PositionPIDToDis(disDev,DisPID);
        
        /* 设置电机PWM输出值 */
        SetRobotMotorPWM(LeftUp,yawPWM+disPWM);
        SetRobotMotorPWM(LeftDown,yawPWM+disPWM);
        SetRobotMotorPWM(RightUp,disPWM-yawPWM);
        SetRobotMotorPWM(RightDown,disPWM-yawPWM);
    } 
    /* 遥控模式 */
    else if(RobotGetControlMode()==REMOTE_CONTROL)
    {
        /* 定义电机编码器数值与目标值偏差 */
        int leftUpDEV,leftDownDEV,rightUpDEV,rightDownDEV;
        /* 定义编码器10ms应该达到的值 */
        RobotEncoderTarSpd10ms_t tarSpd;
        /* 定义电机编码器当前获取的参数值 */
        RobotEncoder10ms_t nowEncoder;
        
        /* 计算目标编码器速度值 */
        tarSpd = GetEncoderTarSpd10ms(stateVector.Vx,stateVector.Vy,stateVector.Wv);
        
        /* 读取编码器当前值 */  
        nowEncoder = GetRobotEncoder_10ms();
        
        /* 计算编码器当前值与目标值偏差 */
        leftUpDEV    = tarSpd.leftUp - nowEncoder.leftUp;
        leftDownDEV  = tarSpd.leftDown - nowEncoder.leftDown;
        rightUpDEV   = tarSpd.rightUP - nowEncoder.rightUP;
        rightDownDEV = tarSpd.rightDown - nowEncoder.rightDown;

        /* 获取目标PWM值 */
        leftUpPWM = PositionPIDtoSpd_Motor1(leftUpDEV,MotorSpeedPID);
        leftDownPWM = PositionPIDtoSpd_Motor2(leftDownDEV,MotorSpeedPID);
        /* 因为小车右边电机与左边电机转向相反，因此需要加负号 */
        rightUpPWM = PositionPIDtoSpd_Motor3(rightUpDEV,MotorSpeedPID);
        rightDownPWM = PositionPIDtoSpd_Motor4(rightDownDEV,MotorSpeedPID);
        							
        /* 设置电机PWM输出值 */
        SetRobotMotorPWM(LeftUp,leftUpPWM);
        SetRobotMotorPWM(LeftDown,leftDownPWM);
        SetRobotMotorPWM(RightUp,rightUpPWM);
        SetRobotMotorPWM(RightDown,rightDownPWM);
    }
}


/**
  * @brief  机器人里程计处理函数，需放入10ms周期执行定时器中
  * @param  None
  * @retval None
  */
void RobotOdometryPro_10ms(void)
{
    Motor_t LeftUpMotor,LeftDownMotor,RightUpMotor,RightDownMotor;
    float LeftUp_dis10ms = 0 ,LeftDown_dis10ms = 0,RightUp_dis10ms = 0,RightDown_dis10ms = 0;
    float nowYaw = 0;
    
    LeftUpMotor = GetMotorState(LeftUp);
    LeftDownMotor = GetMotorState(LeftDown);
    RightUpMotor = GetMotorState(RightUp);
    RightDownMotor = GetMotorState(RightDown);
    
    LeftUp_dis10ms = (LeftUpMotor.EncoderCount/(float)(ONE_CIRCLE_ENCODER))*PI*MECANUM_WHELL_75;
    LeftDown_dis10ms = (LeftDownMotor.EncoderCount/(float)(ONE_CIRCLE_ENCODER))*PI*MECANUM_WHELL_75;
    RightUp_dis10ms = (RightUpMotor.EncoderCount/(float)(ONE_CIRCLE_ENCODER))*PI*MECANUM_WHELL_75;
    RightDown_dis10ms = (RightDownMotor.EncoderCount/(float)(ONE_CIRCLE_ENCODER))*PI*MECANUM_WHELL_75;
    
    /* 计算X轴行驶速度和Y轴行驶速度 */
    RobotOdometry.vx = (LeftUp_dis10ms + LeftDown_dis10ms + RightUp_dis10ms + RightDown_dis10ms) * 100 / 4.0f;
    RobotOdometry.vy = (LeftUp_dis10ms - LeftDown_dis10ms - RightUp_dis10ms + RightDown_dis10ms) * 100 / 4.0f;
    
    /* 计算用户坐标系下X轴行驶里程和Y轴行驶里程 */
    RobotOdometry.disX_UCS += (LeftUp_dis10ms + LeftDown_dis10ms + RightUp_dis10ms + RightDown_dis10ms) / 4.0f;
    RobotOdometry.disY_UCS += (LeftUp_dis10ms - LeftDown_dis10ms - RightUp_dis10ms + RightDown_dis10ms) / 4.0f;
    
    /* 计算世界坐标系下的X轴行驶里程和Y轴行驶里程，先计算当前Yaw角 */
    if(Sensor_GetStatus().VICON_Status == SENSOR_OK)
    {
        nowYaw = -Sensor_GetVICON().AngleZ / 180.0f * PI;
        RobotOdometry.disX_WCS += (RobotOdometry.vx * 0.01f * arm_sin_f32(nowYaw)) + (RobotOdometry.vy * 0.01f * arm_sin_f32(nowYaw));
        RobotOdometry.disY_WCS += (RobotOdometry.vx * 0.01f * arm_cos_f32(nowYaw)) + (RobotOdometry.vy * 0.01f * arm_cos_f32(nowYaw));
    }
    else if(Sensor_GetStatus().VICON_Status == SENSOR_ERROR)
    {
        nowYaw = Sensor_GetIMU().yaw / 180.0f * PI;
        RobotOdometry.disX_WCS += (RobotOdometry.vx * 0.01f * arm_sin_f32(nowYaw)) + (RobotOdometry.vy * 0.01f * arm_sin_f32(nowYaw));
        RobotOdometry.disY_WCS -= (RobotOdometry.vx * 0.01f * arm_cos_f32(nowYaw)) + (RobotOdometry.vy * 0.01f * arm_cos_f32(nowYaw));
    }
    
    /* 计算yaw旋转速度 */
    #ifdef USE_NORMAL_MECANUM
    RobotOdometry.yawSpeed = (LeftUpMotor.EncoderCount + LeftDownMotor.EncoderCount - RightUpMotor.EncoderCount - RightDownMotor.EncoderCount)*100/(4.0f*RM_kz*(WheelToX + WheelToY));
    #endif
    
    #ifdef USE_ASYMMETRIC_MECANUM
    RobotOdometry.yawSpeed = (LeftUpMotor.EncoderCount + LeftDownMotor.EncoderCount - RightUpMotor.EncoderCount - RightDownMotor.EncoderCount)/(4.0f*RM_kz*WheelToX + RM_kz*(WheelToY_LEFT_UP + WheelToY_LEFT_DOWN + WheelToY_RIGHT_UP + WheelToY_RIGHT_DOWN));
    #endif   
    
    /* 计算小车旋转的角度量 */
    RobotOdometry.yawAll += RobotOdometry.yawSpeed;
}




