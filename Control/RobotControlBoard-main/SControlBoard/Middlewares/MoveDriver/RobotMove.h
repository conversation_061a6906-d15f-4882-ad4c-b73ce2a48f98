/*! ----------------------------------------------------------------------------
 * @file    RobotMove.h
 * 
 * @version V1.00 
 *
 * @brief   机器人移动头文件
 *          
 *
 * @Modify
 *
 *      版本号     日期      作者      说明
 *      V1.00   2022/08/02   DSS      发布
 *
 */
#ifndef __ROBOT_MOVE_H__
#define __ROBOT_MOVE_H__

#include <stdint.h>


/* 功能定义 */
#define ENABLE_ROBOT_FEED 1 /* 是否使能机器人投喂功能 */



#define ACC_MAX_SPEED_DEV 0.1f      /* 定义小车加速时，离目标速度的最大差值 */
#define MAX_ROBOT_FEED_TIME 100     /* 定义不发指令给小车，小车停止时间，单位10ms */


/* 定义机器人状态向量结构体 */
typedef struct
{
    float Vx;  /* X轴速度 m/s */ 
    float Vy;  /* Y轴速度 m/s */ 
    float Wv;  /* 旋转角速度 度/s */ 
}RobotStateVector_t;


/* 定义机器人距离移动结构体 */
typedef struct
{
    int speedPWM;    /* 电机速度PWM，范围0-9999，数值越大，动力越强 */ 
    float tarAngle;  /* 目标角度 */ 
    float tarDis;    /* 目标距离 */ 
}RobotMoveDisVector_t;


/* 定义电机速度结构体变量,即每10ms设置一次电机应该达到的速度 */
typedef struct 
{
    int leftUp;     
    int leftDown;
    int rightUP;
    int rightDown;
}RobotEncoderTarSpd10ms_t;

/* 定义里程计结构体 */
typedef struct 
{
    float vx;               /* x轴速度 */
    float vy;               /* y轴速度 */
    float vz;               /* z轴速度 */
    float yawSpeed;         /* yaw角旋转速度 */
    float rollSpeed;        /* roll角旋转速度 */
    float pitchSpeed;       /* yaw角旋转速度 */
    float yawAll;           /* 旋转的角度量 */
    float disX_WCS;         /* x轴行驶的距离,世界坐标系 */
    float disY_WCS;         /* y轴行驶的距离,世界坐标系 */
    float disZ_WCS;         /* z轴行驶的距离,世界坐标系 */
    float disX_UCS;         /* x轴行驶的距离,用户坐标系 */
    float disY_UCS;         /* y轴行驶的距离,世界坐标系 */
    float disZ_UCS;         /* z轴行驶的距离,世界坐标系 */
}RobotOdometry_t;

/* 定义电机输出PWM占空比结构体 */
typedef struct 
{
    float leftUp;   /* PWM输出占空比，范围-100%-100% */    
    float leftDown; /* PWM输出占空比，范围-100%-100% */    
    float rightUP;  /* PWM输出占空比，范围-100%-100% */    
    float rightDown;/* PWM输出占空比，范围-100%-100% */    
}RobotMotorPwm_t;

/* 定义控制模式枚举变量 */
typedef enum
{
    MOTOR_CONTROL=0,    /* 电机控制模式 */
    MOVE_CONTROL,       /* 移动控制模式 */
    YAW_SPEED_CONTROL,  /* Yaw角&速度控制模式 */
    POS_CONTROL,        /* 位置控制模式 */
    REMOTE_CONTROL      /* 遥控模式 */
}RobotControlMode_e;


/* 定义机器人轨迹坐标点结构体 */
typedef struct 
{
    int32_t x;              /* local: x position in meters * 1e4, global: latitude in degrees * 10^7 */   
    int32_t y;              /* local: y position in meters * 1e4, global: latitude in degrees * 10^7 */
    uint8_t autocontinue;   /* Autocontinue to next waypoint. 0: false, 1: true. Set false to pause mission after the item completes. */
    uint16_t stopTime;      /* 到达一个坐标点后的停止时间，单位S */
    float acceptRadius;     /* 可以接收的到达半径，如果距离目标点的距离小于acceptRadius则认为到达了坐标点 */
    float yaw;              /* 到达目标点的期望yaw角 */
    uint8_t current;        /* 是否小车处于当前左边点，是true 否false */
}RobotWayPoint_t;


/* 定义机器人轨迹结构体 */
typedef struct 
{
    uint8_t mavFrame;               /* MavLink定义的坐标系，详细查看 MAV_FRAME 枚举类型 */
    uint8_t count;                  /* 坐标点数量 */
    RobotWayPoint_t wayPoint[100];  /* 定义坐标点数组 */
}RobobtWayPointItem_t;


/* 函数声明 */
void RobotMoveInit(void);
void RobotMove(float Vx,float Vy,float Wv);
void RobotControlPro_10ms(void);
int ChangeEncodeSpdMotorL(void);
RobotOdometry_t RobotGetOdometry(void);
void RobotOdometryPro_10ms(void);
RobotMotorPwm_t GetMotorPWM(void);
void RobotSetControlMode(unsigned char mode);
unsigned char RobotGetControlMode(void);
void RobotSetTargetYaw(float tarYaw);
void FeedRobot(void);
void FeedRobotPro_10ms(void);
float RobotGetTargetYaw(void);

#endif
 



