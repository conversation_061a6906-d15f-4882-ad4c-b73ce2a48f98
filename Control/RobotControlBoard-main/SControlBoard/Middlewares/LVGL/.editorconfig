﻿## 
## PROJECT:   <PERSON>uri Internal Library Essentials
## FILE:      .editorconfig
## PURPOSE:   The root .editorconfig file for Visual Studio C++ Project
## 
## LICENSE:   The MIT License
## 
## DEVELOPER: <PERSON><PERSON><PERSON> (<PERSON>uri_Naruto AT Outlook.com)
## 

root = true

[*]
charset = utf-8-bom
end_of_line = crlf

[*.md]
insert_final_newline = true

[*.{c,c++,cc,cpp,cxx,h,h++,hh,hpp,hxx,idl,inl,ipp,tlh,tli}]
indent_style = space
indent_size = 4
insert_final_newline = true
trim_trailing_whitespace = true
vc_generate_documentation_comments = doxygen_slash_star

[*.{js,json,xml,toml,xaml}]
indent_style = space
indent_size = 2
insert_final_newline = true
trim_trailing_whitespace = true

[*.rc]
charset = utf-16le

[Mile.Project.Properties.Template.h]
charset = utf-16le

[Mile.Project.Properties.h]
charset = utf-16le

[*.bat]
charset = utf-8
insert_final_newline = true
