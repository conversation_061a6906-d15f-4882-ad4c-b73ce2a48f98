@rem 
@rem PROJECT:   Mouri Internal Library Essentials
@rem FILE:      BuildAllTargets.cmd
@rem PURPOSE:   Build all targets script for Visual Studio C++ Project
@rem 
@rem LICENSE:   The MIT License
@rem 
@rem DEVELOPER: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON>_Naruto AT Outlook.com)
@rem 

@setlocal
@echo off

rem Change to the current folder.
cd "%~dp0"

rem Remove the output folder for a fresh compile.
rd /s /q Output

set VisualStudioInstallerFolder="%ProgramFiles(x86)%\Microsoft Visual Studio\Installer"
if %PROCESSOR_ARCHITECTURE%==x86 set VisualStudioInstallerFolder="%ProgramFiles%\Microsoft Visual Studio\Installer"

pushd %VisualStudioInstallerFolder%
for /f "usebackq tokens=*" %%i in (`vswhere -latest -products * -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64 -property installationPath`) do (
  set VisualStudioInstallDir=%%i
)
popd

call "%VisualStudioInstallDir%\VC\Auxiliary\Build\vcvarsall.bat" x86

rem Build all targets
MSBuild -m BuildAllTargets.proj

@endlocal