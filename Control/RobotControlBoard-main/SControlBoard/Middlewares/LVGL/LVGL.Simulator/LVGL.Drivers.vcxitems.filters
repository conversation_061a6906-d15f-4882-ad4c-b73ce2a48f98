﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="lv_drivers">
      <UniqueIdentifier>{10a5c4f0-8965-4603-bad1-c9fd00c303f7}</UniqueIdentifier>
    </Filter>
    <Filter Include="lv_drivers\.github">
      <UniqueIdentifier>{5e4d494e-6840-4466-9e08-9b21458b8408}</UniqueIdentifier>
    </Filter>
    <Filter Include="lv_drivers\display">
      <UniqueIdentifier>{0341e805-0d4d-4fe3-a8c4-eab3b015df79}</UniqueIdentifier>
    </Filter>
    <Filter Include="lv_drivers\docs">
      <UniqueIdentifier>{602eb9b2-e500-4348-a241-863f469f6f32}</UniqueIdentifier>
    </Filter>
    <Filter Include="lv_drivers\gtkdrv">
      <UniqueIdentifier>{ddd8702d-1718-4019-b50e-dd0548202810}</UniqueIdentifier>
    </Filter>
    <Filter Include="lv_drivers\indev">
      <UniqueIdentifier>{653f90c8-7d2a-47a9-aa4d-486b2461325c}</UniqueIdentifier>
    </Filter>
    <Filter Include="lv_drivers\sdl">
      <UniqueIdentifier>{188c7b4c-05a8-4aae-b7d2-a0606ce9f204}</UniqueIdentifier>
    </Filter>
    <Filter Include="lv_drivers\wayland">
      <UniqueIdentifier>{bd0aa873-99c8-4aff-8f3a-22fafb3beb2e}</UniqueIdentifier>
    </Filter>
    <Filter Include="lv_drivers\win32drv">
      <UniqueIdentifier>{de62191d-1058-475d-adfc-ec09b79669b6}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="lv_drivers\display\drm.h">
      <Filter>lv_drivers\display</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\display\fbdev.h">
      <Filter>lv_drivers\display</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\display\GC9A01.h">
      <Filter>lv_drivers\display</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\display\ILI9341.h">
      <Filter>lv_drivers\display</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\display\monitor.h">
      <Filter>lv_drivers\display</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\display\R61581.h">
      <Filter>lv_drivers\display</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\display\SHARP_MIP.h">
      <Filter>lv_drivers\display</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\display\SSD1963.h">
      <Filter>lv_drivers\display</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\display\ST7565.h">
      <Filter>lv_drivers\display</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\display\UC1610.h">
      <Filter>lv_drivers\display</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\gtkdrv\gtkdrv.h">
      <Filter>lv_drivers\gtkdrv</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\indev\AD_touch.h">
      <Filter>lv_drivers\indev</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\indev\evdev.h">
      <Filter>lv_drivers\indev</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\indev\FT5406EE8.h">
      <Filter>lv_drivers\indev</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\indev\keyboard.h">
      <Filter>lv_drivers\indev</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\indev\libinput_drv.h">
      <Filter>lv_drivers\indev</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\indev\mouse.h">
      <Filter>lv_drivers\indev</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\indev\mousewheel.h">
      <Filter>lv_drivers\indev</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\indev\xkb.h">
      <Filter>lv_drivers\indev</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\indev\XPT2046.h">
      <Filter>lv_drivers\indev</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\sdl\sdl.h">
      <Filter>lv_drivers\sdl</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\sdl\sdl_gpu.h">
      <Filter>lv_drivers\sdl</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\wayland\wayland.h">
      <Filter>lv_drivers\wayland</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\win32drv\win32drv.h">
      <Filter>lv_drivers\win32drv</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\lv_drv_conf_template.h">
      <Filter>lv_drivers</Filter>
    </ClInclude>
    <ClInclude Include="lv_drivers\win_drv.h">
      <Filter>lv_drivers</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="lv_drivers\display\drm.c">
      <Filter>lv_drivers\display</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\display\fbdev.c">
      <Filter>lv_drivers\display</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\display\GC9A01.c">
      <Filter>lv_drivers\display</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\display\ILI9341.c">
      <Filter>lv_drivers\display</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\display\R61581.c">
      <Filter>lv_drivers\display</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\display\SHARP_MIP.c">
      <Filter>lv_drivers\display</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\display\SSD1963.c">
      <Filter>lv_drivers\display</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\display\ST7565.c">
      <Filter>lv_drivers\display</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\display\UC1610.c">
      <Filter>lv_drivers\display</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\gtkdrv\gtkdrv.c">
      <Filter>lv_drivers\gtkdrv</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\indev\AD_touch.c">
      <Filter>lv_drivers\indev</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\indev\evdev.c">
      <Filter>lv_drivers\indev</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\indev\FT5406EE8.c">
      <Filter>lv_drivers\indev</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\indev\libinput.c">
      <Filter>lv_drivers\indev</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\indev\xkb.c">
      <Filter>lv_drivers\indev</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\indev\XPT2046.c">
      <Filter>lv_drivers\indev</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\sdl\sdl.c">
      <Filter>lv_drivers\sdl</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\sdl\sdl_gpu.c">
      <Filter>lv_drivers\sdl</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\wayland\wayland.c">
      <Filter>lv_drivers\wayland</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\win32drv\win32drv.c">
      <Filter>lv_drivers\win32drv</Filter>
    </ClCompile>
    <ClCompile Include="lv_drivers\win_drv.c">
      <Filter>lv_drivers</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="lv_drivers\.github\auto-comment.yml">
      <Filter>lv_drivers\.github</Filter>
    </None>
    <None Include="lv_drivers\.github\stale.yml">
      <Filter>lv_drivers\.github</Filter>
    </None>
    <None Include="lv_drivers\docs\astyle_c">
      <Filter>lv_drivers\docs</Filter>
    </None>
    <None Include="lv_drivers\docs\astyle_h">
      <Filter>lv_drivers\docs</Filter>
    </None>
    <None Include="lv_drivers\gtkdrv\broadway.png">
      <Filter>lv_drivers\gtkdrv</Filter>
    </None>
    <None Include="lv_drivers\gtkdrv\README.md">
      <Filter>lv_drivers\gtkdrv</Filter>
    </None>
    <None Include="lv_drivers\wayland\.gitignore">
      <Filter>lv_drivers\wayland</Filter>
    </None>
    <None Include="lv_drivers\wayland\CMakeLists.txt">
      <Filter>lv_drivers\wayland</Filter>
    </None>
    <None Include="lv_drivers\wayland\README.md">
      <Filter>lv_drivers\wayland</Filter>
    </None>
    <None Include="lv_drivers\.git">
      <Filter>lv_drivers</Filter>
    </None>
    <None Include="lv_drivers\.gitignore">
      <Filter>lv_drivers</Filter>
    </None>
    <None Include="lv_drivers\CMakeLists.txt">
      <Filter>lv_drivers</Filter>
    </None>
    <None Include="lv_drivers\library.json">
      <Filter>lv_drivers</Filter>
    </None>
    <None Include="lv_drivers\LICENSE">
      <Filter>lv_drivers</Filter>
    </None>
    <None Include="lv_drivers\lv_drivers.mk">
      <Filter>lv_drivers</Filter>
    </None>
    <None Include="lv_drivers\README.md">
      <Filter>lv_drivers</Filter>
    </None>
  </ItemGroup>
</Project>