﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClInclude Include="lvgl\demos\benchmark\lv_demo_benchmark.h" />
    <ClInclude Include="lvgl\demos\keypad_encoder\lv_demo_keypad_encoder.h" />
    <ClInclude Include="lvgl\demos\music\assets\spectrum_1.h" />
    <ClInclude Include="lvgl\demos\music\assets\spectrum_2.h" />
    <ClInclude Include="lvgl\demos\music\assets\spectrum_3.h" />
    <ClInclude Include="lvgl\demos\music\lv_demo_music.h" />
    <ClInclude Include="lvgl\demos\music\lv_demo_music_list.h" />
    <ClInclude Include="lvgl\demos\music\lv_demo_music_main.h" />
    <ClInclude Include="lvgl\demos\stress\lv_demo_stress.h" />
    <ClInclude Include="lvgl\demos\widgets\lv_demo_widgets.h" />
    <ClInclude Include="lvgl\demos\lv_demos.h" />
    <ClInclude Include="lvgl\env_support\cmsis-pack\lv_conf_cmsis.h" />
    <ClInclude Include="lvgl\env_support\rt-thread\lv_rt_thread_conf.h" />
    <ClInclude Include="lvgl\examples\anim\lv_example_anim.h" />
    <ClInclude Include="lvgl\examples\event\lv_example_event.h" />
    <ClInclude Include="lvgl\examples\get_started\lv_example_get_started.h" />
    <ClInclude Include="lvgl\examples\layouts\flex\lv_example_flex.h" />
    <ClInclude Include="lvgl\examples\layouts\grid\lv_example_grid.h" />
    <ClInclude Include="lvgl\examples\layouts\lv_example_layout.h" />
    <ClInclude Include="lvgl\examples\libs\bmp\lv_example_bmp.h" />
    <ClInclude Include="lvgl\examples\libs\ffmpeg\lv_example_ffmpeg.h" />
    <ClInclude Include="lvgl\examples\libs\freetype\lv_example_freetype.h" />
    <ClInclude Include="lvgl\examples\libs\gif\lv_example_gif.h" />
    <ClInclude Include="lvgl\examples\libs\png\lv_example_png.h" />
    <ClInclude Include="lvgl\examples\libs\qrcode\lv_example_qrcode.h" />
    <ClInclude Include="lvgl\examples\libs\rlottie\lv_example_rlottie.h" />
    <ClInclude Include="lvgl\examples\libs\sjpg\lv_example_sjpg.h" />
    <ClInclude Include="lvgl\examples\libs\lv_example_libs.h" />
    <ClInclude Include="lvgl\examples\others\gridnav\lv_example_gridnav.h" />
    <ClInclude Include="lvgl\examples\others\monkey\lv_example_monkey.h" />
    <ClInclude Include="lvgl\examples\others\snapshot\lv_example_snapshot.h" />
    <ClInclude Include="lvgl\examples\others\lv_example_others.h" />
    <ClInclude Include="lvgl\examples\porting\lv_port_disp_template.h" />
    <ClInclude Include="lvgl\examples\porting\lv_port_fs_template.h" />
    <ClInclude Include="lvgl\examples\porting\lv_port_indev_template.h" />
    <ClInclude Include="lvgl\examples\scroll\lv_example_scroll.h" />
    <ClInclude Include="lvgl\examples\styles\lv_example_style.h" />
    <ClInclude Include="lvgl\examples\widgets\lv_example_widgets.h" />
    <ClInclude Include="lvgl\examples\lv_examples.h" />
    <ClInclude Include="lvgl\src\core\lv_disp.h" />
    <ClInclude Include="lvgl\src\core\lv_event.h" />
    <ClInclude Include="lvgl\src\core\lv_group.h" />
    <ClInclude Include="lvgl\src\core\lv_indev.h" />
    <ClInclude Include="lvgl\src\core\lv_indev_scroll.h" />
    <ClInclude Include="lvgl\src\core\lv_obj.h" />
    <ClInclude Include="lvgl\src\core\lv_obj_class.h" />
    <ClInclude Include="lvgl\src\core\lv_obj_draw.h" />
    <ClInclude Include="lvgl\src\core\lv_obj_pos.h" />
    <ClInclude Include="lvgl\src\core\lv_obj_scroll.h" />
    <ClInclude Include="lvgl\src\core\lv_obj_style.h" />
    <ClInclude Include="lvgl\src\core\lv_obj_style_gen.h" />
    <ClInclude Include="lvgl\src\core\lv_obj_tree.h" />
    <ClInclude Include="lvgl\src\core\lv_refr.h" />
    <ClInclude Include="lvgl\src\core\lv_theme.h" />
    <ClInclude Include="lvgl\src\draw\nxp_pxp\lv_gpu_nxp_pxp.h" />
    <ClInclude Include="lvgl\src\draw\nxp_pxp\lv_gpu_nxp_pxp_osa.h" />
    <ClInclude Include="lvgl\src\draw\nxp_vglite\lv_gpu_nxp_vglite.h" />
    <ClInclude Include="lvgl\src\draw\sdl\lv_draw_sdl.h" />
    <ClInclude Include="lvgl\src\draw\sdl\lv_draw_sdl_composite.h" />
    <ClInclude Include="lvgl\src\draw\sdl\lv_draw_sdl_img.h" />
    <ClInclude Include="lvgl\src\draw\sdl\lv_draw_sdl_mask.h" />
    <ClInclude Include="lvgl\src\draw\sdl\lv_draw_sdl_priv.h" />
    <ClInclude Include="lvgl\src\draw\sdl\lv_draw_sdl_rect.h" />
    <ClInclude Include="lvgl\src\draw\sdl\lv_draw_sdl_stack_blur.h" />
    <ClInclude Include="lvgl\src\draw\sdl\lv_draw_sdl_texture_cache.h" />
    <ClInclude Include="lvgl\src\draw\sdl\lv_draw_sdl_utils.h" />
    <ClInclude Include="lvgl\src\draw\stm32_dma2d\lv_gpu_stm32_dma2d.h" />
    <ClInclude Include="lvgl\src\draw\sw\lv_draw_sw.h" />
    <ClInclude Include="lvgl\src\draw\sw\lv_draw_sw_blend.h" />
    <ClInclude Include="lvgl\src\draw\sw\lv_draw_sw_dither.h" />
    <ClInclude Include="lvgl\src\draw\sw\lv_draw_sw_gradient.h" />
    <ClInclude Include="lvgl\src\draw\lv_draw.h" />
    <ClInclude Include="lvgl\src\draw\lv_draw_arc.h" />
    <ClInclude Include="lvgl\src\draw\lv_draw_img.h" />
    <ClInclude Include="lvgl\src\draw\lv_draw_label.h" />
    <ClInclude Include="lvgl\src\draw\lv_draw_line.h" />
    <ClInclude Include="lvgl\src\draw\lv_draw_mask.h" />
    <ClInclude Include="lvgl\src\draw\lv_draw_rect.h" />
    <ClInclude Include="lvgl\src\draw\lv_draw_triangle.h" />
    <ClInclude Include="lvgl\src\draw\lv_img_buf.h" />
    <ClInclude Include="lvgl\src\draw\lv_img_cache.h" />
    <ClInclude Include="lvgl\src\draw\lv_img_decoder.h" />
    <ClInclude Include="lvgl\src\extra\layouts\flex\lv_flex.h" />
    <ClInclude Include="lvgl\src\extra\layouts\grid\lv_grid.h" />
    <ClInclude Include="lvgl\src\extra\layouts\lv_layouts.h" />
    <ClInclude Include="lvgl\src\extra\libs\bmp\lv_bmp.h" />
    <ClInclude Include="lvgl\src\extra\libs\ffmpeg\lv_ffmpeg.h" />
    <ClInclude Include="lvgl\src\extra\libs\freetype\lv_freetype.h" />
    <ClInclude Include="lvgl\src\extra\libs\fsdrv\lv_fsdrv.h" />
    <ClInclude Include="lvgl\src\extra\libs\gif\gifdec.h" />
    <ClInclude Include="lvgl\src\extra\libs\gif\lv_gif.h" />
    <ClInclude Include="lvgl\src\extra\libs\png\lodepng.h" />
    <ClInclude Include="lvgl\src\extra\libs\png\lv_png.h" />
    <ClInclude Include="lvgl\src\extra\libs\qrcode\lv_qrcode.h" />
    <ClInclude Include="lvgl\src\extra\libs\qrcode\qrcodegen.h" />
    <ClInclude Include="lvgl\src\extra\libs\rlottie\lv_rlottie.h" />
    <ClInclude Include="lvgl\src\extra\libs\sjpg\lv_sjpg.h" />
    <ClInclude Include="lvgl\src\extra\libs\sjpg\tjpgd.h" />
    <ClInclude Include="lvgl\src\extra\libs\sjpg\tjpgdcnf.h" />
    <ClInclude Include="lvgl\src\extra\libs\lv_libs.h" />
    <ClInclude Include="lvgl\src\extra\others\gridnav\lv_gridnav.h" />
    <ClInclude Include="lvgl\src\extra\others\monkey\lv_monkey.h" />
    <ClInclude Include="lvgl\src\extra\others\snapshot\lv_snapshot.h" />
    <ClInclude Include="lvgl\src\extra\others\lv_others.h" />
    <ClInclude Include="lvgl\src\extra\themes\basic\lv_theme_basic.h" />
    <ClInclude Include="lvgl\src\extra\themes\default\lv_theme_default.h" />
    <ClInclude Include="lvgl\src\extra\themes\mono\lv_theme_mono.h" />
    <ClInclude Include="lvgl\src\extra\themes\lv_themes.h" />
    <ClInclude Include="lvgl\src\extra\widgets\animimg\lv_animimg.h" />
    <ClInclude Include="lvgl\src\extra\widgets\calendar\lv_calendar.h" />
    <ClInclude Include="lvgl\src\extra\widgets\calendar\lv_calendar_header_arrow.h" />
    <ClInclude Include="lvgl\src\extra\widgets\calendar\lv_calendar_header_dropdown.h" />
    <ClInclude Include="lvgl\src\extra\widgets\chart\lv_chart.h" />
    <ClInclude Include="lvgl\src\extra\widgets\colorwheel\lv_colorwheel.h" />
    <ClInclude Include="lvgl\src\extra\widgets\imgbtn\lv_imgbtn.h" />
    <ClInclude Include="lvgl\src\extra\widgets\keyboard\lv_keyboard.h" />
    <ClInclude Include="lvgl\src\extra\widgets\led\lv_led.h" />
    <ClInclude Include="lvgl\src\extra\widgets\list\lv_list.h" />
    <ClInclude Include="lvgl\src\extra\widgets\menu\lv_menu.h" />
    <ClInclude Include="lvgl\src\extra\widgets\meter\lv_meter.h" />
    <ClInclude Include="lvgl\src\extra\widgets\msgbox\lv_msgbox.h" />
    <ClInclude Include="lvgl\src\extra\widgets\span\lv_span.h" />
    <ClInclude Include="lvgl\src\extra\widgets\spinbox\lv_spinbox.h" />
    <ClInclude Include="lvgl\src\extra\widgets\spinner\lv_spinner.h" />
    <ClInclude Include="lvgl\src\extra\widgets\tabview\lv_tabview.h" />
    <ClInclude Include="lvgl\src\extra\widgets\tileview\lv_tileview.h" />
    <ClInclude Include="lvgl\src\extra\widgets\win\lv_win.h" />
    <ClInclude Include="lvgl\src\extra\widgets\lv_widgets.h" />
    <ClInclude Include="lvgl\src\extra\lv_extra.h" />
    <ClInclude Include="lvgl\src\font\lv_font.h" />
    <ClInclude Include="lvgl\src\font\lv_font_fmt_txt.h" />
    <ClInclude Include="lvgl\src\font\lv_font_loader.h" />
    <ClInclude Include="lvgl\src\font\lv_symbol_def.h" />
    <ClInclude Include="lvgl\src\hal\lv_hal.h" />
    <ClInclude Include="lvgl\src\hal\lv_hal_disp.h" />
    <ClInclude Include="lvgl\src\hal\lv_hal_indev.h" />
    <ClInclude Include="lvgl\src\hal\lv_hal_tick.h" />
    <ClInclude Include="lvgl\src\misc\lv_anim.h" />
    <ClInclude Include="lvgl\src\misc\lv_anim_timeline.h" />
    <ClInclude Include="lvgl\src\misc\lv_area.h" />
    <ClInclude Include="lvgl\src\misc\lv_assert.h" />
    <ClInclude Include="lvgl\src\misc\lv_async.h" />
    <ClInclude Include="lvgl\src\misc\lv_bidi.h" />
    <ClInclude Include="lvgl\src\misc\lv_color.h" />
    <ClInclude Include="lvgl\src\misc\lv_fs.h" />
    <ClInclude Include="lvgl\src\misc\lv_gc.h" />
    <ClInclude Include="lvgl\src\misc\lv_ll.h" />
    <ClInclude Include="lvgl\src\misc\lv_log.h" />
    <ClInclude Include="lvgl\src\misc\lv_lru.h" />
    <ClInclude Include="lvgl\src\misc\lv_math.h" />
    <ClInclude Include="lvgl\src\misc\lv_mem.h" />
    <ClInclude Include="lvgl\src\misc\lv_printf.h" />
    <ClInclude Include="lvgl\src\misc\lv_style.h" />
    <ClInclude Include="lvgl\src\misc\lv_style_gen.h" />
    <ClInclude Include="lvgl\src\misc\lv_templ.h" />
    <ClInclude Include="lvgl\src\misc\lv_timer.h" />
    <ClInclude Include="lvgl\src\misc\lv_tlsf.h" />
    <ClInclude Include="lvgl\src\misc\lv_txt.h" />
    <ClInclude Include="lvgl\src\misc\lv_txt_ap.h" />
    <ClInclude Include="lvgl\src\misc\lv_types.h" />
    <ClInclude Include="lvgl\src\misc\lv_utils.h" />
    <ClInclude Include="lvgl\src\widgets\lv_arc.h" />
    <ClInclude Include="lvgl\src\widgets\lv_bar.h" />
    <ClInclude Include="lvgl\src\widgets\lv_btn.h" />
    <ClInclude Include="lvgl\src\widgets\lv_btnmatrix.h" />
    <ClInclude Include="lvgl\src\widgets\lv_canvas.h" />
    <ClInclude Include="lvgl\src\widgets\lv_checkbox.h" />
    <ClInclude Include="lvgl\src\widgets\lv_dropdown.h" />
    <ClInclude Include="lvgl\src\widgets\lv_img.h" />
    <ClInclude Include="lvgl\src\widgets\lv_label.h" />
    <ClInclude Include="lvgl\src\widgets\lv_line.h" />
    <ClInclude Include="lvgl\src\widgets\lv_objx_templ.h" />
    <ClInclude Include="lvgl\src\widgets\lv_roller.h" />
    <ClInclude Include="lvgl\src\widgets\lv_slider.h" />
    <ClInclude Include="lvgl\src\widgets\lv_switch.h" />
    <ClInclude Include="lvgl\src\widgets\lv_table.h" />
    <ClInclude Include="lvgl\src\widgets\lv_textarea.h" />
    <ClInclude Include="lvgl\src\lvgl.h" />
    <ClInclude Include="lvgl\src\lv_api_map.h" />
    <ClInclude Include="lvgl\src\lv_conf_internal.h" />
    <ClInclude Include="lvgl\src\lv_conf_kconfig.h" />
    <ClInclude Include="lvgl\tests\src\lv_test_conf.h" />
    <ClInclude Include="lvgl\tests\src\lv_test_helpers.h" />
    <ClInclude Include="lvgl\tests\src\lv_test_indev.h" />
    <ClInclude Include="lvgl\tests\src\lv_test_init.h" />
    <ClInclude Include="lvgl\tests\unity\unity.h" />
    <ClInclude Include="lvgl\tests\unity\unity_internals.h" />
    <ClInclude Include="lvgl\tests\unity\unity_support.h" />
    <ClInclude Include="lvgl\lvgl.h" />
    <ClInclude Include="lvgl\lv_conf_template.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="lvgl\demos\benchmark\assets\img_benchmark_cogwheel_alpha16.c" />
    <ClCompile Include="lvgl\demos\benchmark\assets\img_benchmark_cogwheel_argb.c" />
    <ClCompile Include="lvgl\demos\benchmark\assets\img_benchmark_cogwheel_chroma_keyed.c" />
    <ClCompile Include="lvgl\demos\benchmark\assets\img_benchmark_cogwheel_indexed16.c" />
    <ClCompile Include="lvgl\demos\benchmark\assets\img_benchmark_cogwheel_rgb.c" />
    <ClCompile Include="lvgl\demos\benchmark\assets\lv_font_bechmark_montserrat_12_compr_az.c.c" />
    <ClCompile Include="lvgl\demos\benchmark\assets\lv_font_bechmark_montserrat_16_compr_az.c.c" />
    <ClCompile Include="lvgl\demos\benchmark\assets\lv_font_bechmark_montserrat_28_compr_az.c.c" />
    <ClCompile Include="lvgl\demos\benchmark\lv_demo_benchmark.c" />
    <ClCompile Include="lvgl\demos\keypad_encoder\lv_demo_keypad_encoder.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_btn_corner_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_btn_list_pause.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_btn_list_pause_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_btn_list_play.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_btn_list_play_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_btn_loop.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_btn_loop_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_btn_next.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_btn_next_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_btn_pause.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_btn_pause_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_btn_play.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_btn_play_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_btn_prev.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_btn_prev_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_btn_rnd.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_btn_rnd_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_corner_left.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_corner_left_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_corner_right.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_corner_right_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_cover_1.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_cover_1_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_cover_2.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_cover_2_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_cover_3.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_cover_3_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_icon_1.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_icon_1_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_icon_2.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_icon_2_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_icon_3.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_icon_3_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_icon_4.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_icon_4_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_list_border.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_list_border_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_logo.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_slider_knob.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_slider_knob_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_wave_bottom.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_wave_bottom_large.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_wave_top.c" />
    <ClCompile Include="lvgl\demos\music\assets\img_lv_demo_music_wave_top_large.c" />
    <ClCompile Include="lvgl\demos\music\lv_demo_music.c" />
    <ClCompile Include="lvgl\demos\music\lv_demo_music_list.c" />
    <ClCompile Include="lvgl\demos\music\lv_demo_music_main.c" />
    <ClCompile Include="lvgl\demos\stress\assets\lv_font_montserrat_12_compr_az.c" />
    <ClCompile Include="lvgl\demos\stress\assets\lv_font_montserrat_16_compr_az.c" />
    <ClCompile Include="lvgl\demos\stress\assets\lv_font_montserrat_28_compr_az.c" />
    <ClCompile Include="lvgl\demos\stress\lv_demo_stress.c" />
    <ClCompile Include="lvgl\demos\widgets\assets\img_clothes.c" />
    <ClCompile Include="lvgl\demos\widgets\assets\img_demo_widgets_avatar.c" />
    <ClCompile Include="lvgl\demos\widgets\assets\img_lvgl_logo.c" />
    <ClCompile Include="lvgl\demos\widgets\lv_demo_widgets.c" />
    <ClCompile Include="lvgl\env_support\rt-thread\lv_rt_thread_port.c" />
    <ClCompile Include="lvgl\examples\anim\lv_example_anim_1.c" />
    <ClCompile Include="lvgl\examples\anim\lv_example_anim_2.c" />
    <ClCompile Include="lvgl\examples\anim\lv_example_anim_3.c" />
    <ClCompile Include="lvgl\examples\anim\lv_example_anim_timeline_1.c" />
    <ClCompile Include="lvgl\examples\assets\animimg001.c" />
    <ClCompile Include="lvgl\examples\assets\animimg002.c" />
    <ClCompile Include="lvgl\examples\assets\animimg003.c" />
    <ClCompile Include="lvgl\examples\assets\imgbtn_left.c" />
    <ClCompile Include="lvgl\examples\assets\imgbtn_mid.c" />
    <ClCompile Include="lvgl\examples\assets\imgbtn_right.c" />
    <ClCompile Include="lvgl\examples\assets\img_caret_down.c" />
    <ClCompile Include="lvgl\examples\assets\img_cogwheel_alpha16.c" />
    <ClCompile Include="lvgl\examples\assets\img_cogwheel_argb.c" />
    <ClCompile Include="lvgl\examples\assets\img_cogwheel_chroma_keyed.c" />
    <ClCompile Include="lvgl\examples\assets\img_cogwheel_indexed16.c" />
    <ClCompile Include="lvgl\examples\assets\img_cogwheel_rgb.c" />
    <ClCompile Include="lvgl\examples\assets\img_hand.c" />
    <ClCompile Include="lvgl\examples\assets\img_skew_strip.c" />
    <ClCompile Include="lvgl\examples\assets\img_star.c" />
    <ClCompile Include="lvgl\examples\event\lv_example_event_1.c" />
    <ClCompile Include="lvgl\examples\event\lv_example_event_2.c" />
    <ClCompile Include="lvgl\examples\event\lv_example_event_3.c" />
    <ClCompile Include="lvgl\examples\event\lv_example_event_4.c" />
    <ClCompile Include="lvgl\examples\get_started\lv_example_get_started_1.c" />
    <ClCompile Include="lvgl\examples\get_started\lv_example_get_started_2.c" />
    <ClCompile Include="lvgl\examples\get_started\lv_example_get_started_3.c" />
    <ClCompile Include="lvgl\examples\layouts\flex\lv_example_flex_1.c" />
    <ClCompile Include="lvgl\examples\layouts\flex\lv_example_flex_2.c" />
    <ClCompile Include="lvgl\examples\layouts\flex\lv_example_flex_3.c" />
    <ClCompile Include="lvgl\examples\layouts\flex\lv_example_flex_4.c" />
    <ClCompile Include="lvgl\examples\layouts\flex\lv_example_flex_5.c" />
    <ClCompile Include="lvgl\examples\layouts\flex\lv_example_flex_6.c" />
    <ClCompile Include="lvgl\examples\layouts\grid\lv_example_grid_1.c" />
    <ClCompile Include="lvgl\examples\layouts\grid\lv_example_grid_2.c" />
    <ClCompile Include="lvgl\examples\layouts\grid\lv_example_grid_3.c" />
    <ClCompile Include="lvgl\examples\layouts\grid\lv_example_grid_4.c" />
    <ClCompile Include="lvgl\examples\layouts\grid\lv_example_grid_5.c" />
    <ClCompile Include="lvgl\examples\layouts\grid\lv_example_grid_6.c" />
    <ClCompile Include="lvgl\examples\libs\bmp\lv_example_bmp_1.c" />
    <ClCompile Include="lvgl\examples\libs\ffmpeg\lv_example_ffmpeg_1.c" />
    <ClCompile Include="lvgl\examples\libs\ffmpeg\lv_example_ffmpeg_2.c" />
    <ClCompile Include="lvgl\examples\libs\freetype\lv_example_freetype_1.c" />
    <ClCompile Include="lvgl\examples\libs\gif\img_bulb_gif.c" />
    <ClCompile Include="lvgl\examples\libs\gif\lv_example_gif_1.c" />
    <ClCompile Include="lvgl\examples\libs\png\img_wink_png.c" />
    <ClCompile Include="lvgl\examples\libs\png\lv_example_png_1.c" />
    <ClCompile Include="lvgl\examples\libs\qrcode\lv_example_qrcode_1.c" />
    <ClCompile Include="lvgl\examples\libs\rlottie\lv_example_rlottie_1.c" />
    <ClCompile Include="lvgl\examples\libs\rlottie\lv_example_rlottie_2.c" />
    <ClCompile Include="lvgl\examples\libs\rlottie\lv_example_rlottie_approve.c" />
    <ClCompile Include="lvgl\examples\libs\sjpg\lv_example_sjpg_1.c" />
    <ClCompile Include="lvgl\examples\others\gridnav\lv_example_gridnav_1.c" />
    <ClCompile Include="lvgl\examples\others\gridnav\lv_example_gridnav_2.c" />
    <ClCompile Include="lvgl\examples\others\gridnav\lv_example_gridnav_3.c" />
    <ClCompile Include="lvgl\examples\others\gridnav\lv_example_gridnav_4.c" />
    <ClCompile Include="lvgl\examples\others\monkey\lv_example_monkey_1.c" />
    <ClCompile Include="lvgl\examples\others\monkey\lv_example_monkey_2.c" />
    <ClCompile Include="lvgl\examples\others\monkey\lv_example_monkey_3.c" />
    <ClCompile Include="lvgl\examples\others\snapshot\lv_example_snapshot_1.c" />
    <ClCompile Include="lvgl\examples\porting\lv_port_disp_template.c" />
    <ClCompile Include="lvgl\examples\porting\lv_port_fs_template.c" />
    <ClCompile Include="lvgl\examples\porting\lv_port_indev_template.c" />
    <ClCompile Include="lvgl\examples\scroll\lv_example_scroll_1.c" />
    <ClCompile Include="lvgl\examples\scroll\lv_example_scroll_2.c" />
    <ClCompile Include="lvgl\examples\scroll\lv_example_scroll_3.c" />
    <ClCompile Include="lvgl\examples\scroll\lv_example_scroll_4.c" />
    <ClCompile Include="lvgl\examples\scroll\lv_example_scroll_5.c" />
    <ClCompile Include="lvgl\examples\scroll\lv_example_scroll_6.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_1.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_10.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_11.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_12.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_13.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_14.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_2.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_3.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_4.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_5.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_6.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_7.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_8.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_9.c" />
    <ClCompile Include="lvgl\examples\widgets\animimg\lv_example_animimg_1.c" />
    <ClCompile Include="lvgl\examples\widgets\arc\lv_example_arc_1.c" />
    <ClCompile Include="lvgl\examples\widgets\arc\lv_example_arc_2.c" />
    <ClCompile Include="lvgl\examples\widgets\bar\lv_example_bar_1.c" />
    <ClCompile Include="lvgl\examples\widgets\bar\lv_example_bar_2.c" />
    <ClCompile Include="lvgl\examples\widgets\bar\lv_example_bar_3.c" />
    <ClCompile Include="lvgl\examples\widgets\bar\lv_example_bar_4.c" />
    <ClCompile Include="lvgl\examples\widgets\bar\lv_example_bar_5.c" />
    <ClCompile Include="lvgl\examples\widgets\bar\lv_example_bar_6.c" />
    <ClCompile Include="lvgl\examples\widgets\btn\lv_example_btn_1.c" />
    <ClCompile Include="lvgl\examples\widgets\btn\lv_example_btn_2.c" />
    <ClCompile Include="lvgl\examples\widgets\btn\lv_example_btn_3.c" />
    <ClCompile Include="lvgl\examples\widgets\btnmatrix\lv_example_btnmatrix_1.c" />
    <ClCompile Include="lvgl\examples\widgets\btnmatrix\lv_example_btnmatrix_2.c" />
    <ClCompile Include="lvgl\examples\widgets\btnmatrix\lv_example_btnmatrix_3.c" />
    <ClCompile Include="lvgl\examples\widgets\calendar\lv_example_calendar_1.c" />
    <ClCompile Include="lvgl\examples\widgets\canvas\lv_example_canvas_1.c" />
    <ClCompile Include="lvgl\examples\widgets\canvas\lv_example_canvas_2.c" />
    <ClCompile Include="lvgl\examples\widgets\chart\lv_example_chart_1.c" />
    <ClCompile Include="lvgl\examples\widgets\chart\lv_example_chart_2.c" />
    <ClCompile Include="lvgl\examples\widgets\chart\lv_example_chart_3.c" />
    <ClCompile Include="lvgl\examples\widgets\chart\lv_example_chart_4.c" />
    <ClCompile Include="lvgl\examples\widgets\chart\lv_example_chart_5.c" />
    <ClCompile Include="lvgl\examples\widgets\chart\lv_example_chart_6.c" />
    <ClCompile Include="lvgl\examples\widgets\chart\lv_example_chart_7.c" />
    <ClCompile Include="lvgl\examples\widgets\chart\lv_example_chart_8.c" />
    <ClCompile Include="lvgl\examples\widgets\chart\lv_example_chart_9.c" />
    <ClCompile Include="lvgl\examples\widgets\checkbox\lv_example_checkbox_1.c" />
    <ClCompile Include="lvgl\examples\widgets\checkbox\lv_example_checkbox_2.c" />
    <ClCompile Include="lvgl\examples\widgets\colorwheel\lv_example_colorwheel_1.c" />
    <ClCompile Include="lvgl\examples\widgets\dropdown\lv_example_dropdown_1.c" />
    <ClCompile Include="lvgl\examples\widgets\dropdown\lv_example_dropdown_2.c" />
    <ClCompile Include="lvgl\examples\widgets\dropdown\lv_example_dropdown_3.c" />
    <ClCompile Include="lvgl\examples\widgets\img\lv_example_img_1.c" />
    <ClCompile Include="lvgl\examples\widgets\img\lv_example_img_2.c" />
    <ClCompile Include="lvgl\examples\widgets\img\lv_example_img_3.c" />
    <ClCompile Include="lvgl\examples\widgets\img\lv_example_img_4.c" />
    <ClCompile Include="lvgl\examples\widgets\imgbtn\lv_example_imgbtn_1.c" />
    <ClCompile Include="lvgl\examples\widgets\keyboard\lv_example_keyboard_1.c" />
    <ClCompile Include="lvgl\examples\widgets\label\lv_example_label_1.c" />
    <ClCompile Include="lvgl\examples\widgets\label\lv_example_label_2.c" />
    <ClCompile Include="lvgl\examples\widgets\label\lv_example_label_3.c" />
    <ClCompile Include="lvgl\examples\widgets\label\lv_example_label_4.c" />
    <ClCompile Include="lvgl\examples\widgets\led\lv_example_led_1.c" />
    <ClCompile Include="lvgl\examples\widgets\line\lv_example_line_1.c" />
    <ClCompile Include="lvgl\examples\widgets\list\lv_example_list_1.c" />
    <ClCompile Include="lvgl\examples\widgets\list\lv_example_list_2.c" />
    <ClCompile Include="lvgl\examples\widgets\menu\lv_example_menu_1.c" />
    <ClCompile Include="lvgl\examples\widgets\menu\lv_example_menu_2.c" />
    <ClCompile Include="lvgl\examples\widgets\menu\lv_example_menu_3.c" />
    <ClCompile Include="lvgl\examples\widgets\menu\lv_example_menu_4.c" />
    <ClCompile Include="lvgl\examples\widgets\menu\lv_example_menu_5.c" />
    <ClCompile Include="lvgl\examples\widgets\meter\lv_example_meter_1.c" />
    <ClCompile Include="lvgl\examples\widgets\meter\lv_example_meter_2.c" />
    <ClCompile Include="lvgl\examples\widgets\meter\lv_example_meter_3.c" />
    <ClCompile Include="lvgl\examples\widgets\meter\lv_example_meter_4.c" />
    <ClCompile Include="lvgl\examples\widgets\msgbox\lv_example_msgbox_1.c" />
    <ClCompile Include="lvgl\examples\widgets\obj\lv_example_obj_1.c" />
    <ClCompile Include="lvgl\examples\widgets\obj\lv_example_obj_2.c" />
    <ClCompile Include="lvgl\examples\widgets\roller\lv_example_roller_1.c" />
    <ClCompile Include="lvgl\examples\widgets\roller\lv_example_roller_2.c" />
    <ClCompile Include="lvgl\examples\widgets\roller\lv_example_roller_3.c" />
    <ClCompile Include="lvgl\examples\widgets\slider\lv_example_slider_1.c" />
    <ClCompile Include="lvgl\examples\widgets\slider\lv_example_slider_2.c" />
    <ClCompile Include="lvgl\examples\widgets\slider\lv_example_slider_3.c" />
    <ClCompile Include="lvgl\examples\widgets\span\lv_example_span_1.c" />
    <ClCompile Include="lvgl\examples\widgets\spinbox\lv_example_spinbox_1.c" />
    <ClCompile Include="lvgl\examples\widgets\spinner\lv_example_spinner_1.c" />
    <ClCompile Include="lvgl\examples\widgets\switch\lv_example_switch_1.c" />
    <ClCompile Include="lvgl\examples\widgets\table\lv_example_table_1.c" />
    <ClCompile Include="lvgl\examples\widgets\table\lv_example_table_2.c" />
    <ClCompile Include="lvgl\examples\widgets\tabview\lv_example_tabview_1.c" />
    <ClCompile Include="lvgl\examples\widgets\tabview\lv_example_tabview_2.c" />
    <ClCompile Include="lvgl\examples\widgets\textarea\lv_example_textarea_1.c" />
    <ClCompile Include="lvgl\examples\widgets\textarea\lv_example_textarea_2.c" />
    <ClCompile Include="lvgl\examples\widgets\textarea\lv_example_textarea_3.c" />
    <ClCompile Include="lvgl\examples\widgets\tileview\lv_example_tileview_1.c" />
    <ClCompile Include="lvgl\examples\widgets\win\lv_example_win_1.c" />
    <ClCompile Include="lvgl\src\core\lv_disp.c" />
    <ClCompile Include="lvgl\src\core\lv_event.c" />
    <ClCompile Include="lvgl\src\core\lv_group.c" />
    <ClCompile Include="lvgl\src\core\lv_indev.c" />
    <ClCompile Include="lvgl\src\core\lv_indev_scroll.c" />
    <ClCompile Include="lvgl\src\core\lv_obj.c" />
    <ClCompile Include="lvgl\src\core\lv_obj_class.c" />
    <ClCompile Include="lvgl\src\core\lv_obj_draw.c" />
    <ClCompile Include="lvgl\src\core\lv_obj_pos.c" />
    <ClCompile Include="lvgl\src\core\lv_obj_scroll.c" />
    <ClCompile Include="lvgl\src\core\lv_obj_style.c" />
    <ClCompile Include="lvgl\src\core\lv_obj_style_gen.c" />
    <ClCompile Include="lvgl\src\core\lv_obj_tree.c" />
    <ClCompile Include="lvgl\src\core\lv_refr.c" />
    <ClCompile Include="lvgl\src\core\lv_theme.c" />
    <ClCompile Include="lvgl\src\draw\nxp_pxp\lv_gpu_nxp_pxp.c" />
    <ClCompile Include="lvgl\src\draw\nxp_pxp\lv_gpu_nxp_pxp_osa.c" />
    <ClCompile Include="lvgl\src\draw\nxp_vglite\lv_gpu_nxp_vglite.c" />
    <ClCompile Include="lvgl\src\draw\sdl\lv_draw_sdl.c" />
    <ClCompile Include="lvgl\src\draw\sdl\lv_draw_sdl_arc.c" />
    <ClCompile Include="lvgl\src\draw\sdl\lv_draw_sdl_bg.c" />
    <ClCompile Include="lvgl\src\draw\sdl\lv_draw_sdl_composite.c" />
    <ClCompile Include="lvgl\src\draw\sdl\lv_draw_sdl_img.c" />
    <ClCompile Include="lvgl\src\draw\sdl\lv_draw_sdl_label.c" />
    <ClCompile Include="lvgl\src\draw\sdl\lv_draw_sdl_line.c" />
    <ClCompile Include="lvgl\src\draw\sdl\lv_draw_sdl_mask.c" />
    <ClCompile Include="lvgl\src\draw\sdl\lv_draw_sdl_polygon.c" />
    <ClCompile Include="lvgl\src\draw\sdl\lv_draw_sdl_rect.c" />
    <ClCompile Include="lvgl\src\draw\sdl\lv_draw_sdl_stack_blur.c" />
    <ClCompile Include="lvgl\src\draw\sdl\lv_draw_sdl_texture_cache.c" />
    <ClCompile Include="lvgl\src\draw\sdl\lv_draw_sdl_utils.c" />
    <ClCompile Include="lvgl\src\draw\stm32_dma2d\lv_gpu_stm32_dma2d.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw_arc.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw_blend.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw_dither.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw_gradient.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw_img.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw_letter.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw_line.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw_polygon.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw_rect.c" />
    <ClCompile Include="lvgl\src\draw\lv_draw.c" />
    <ClCompile Include="lvgl\src\draw\lv_draw_arc.c" />
    <ClCompile Include="lvgl\src\draw\lv_draw_img.c" />
    <ClCompile Include="lvgl\src\draw\lv_draw_label.c" />
    <ClCompile Include="lvgl\src\draw\lv_draw_line.c" />
    <ClCompile Include="lvgl\src\draw\lv_draw_mask.c" />
    <ClCompile Include="lvgl\src\draw\lv_draw_rect.c" />
    <ClCompile Include="lvgl\src\draw\lv_draw_triangle.c" />
    <ClCompile Include="lvgl\src\draw\lv_img_buf.c" />
    <ClCompile Include="lvgl\src\draw\lv_img_cache.c" />
    <ClCompile Include="lvgl\src\draw\lv_img_decoder.c" />
    <ClCompile Include="lvgl\src\extra\layouts\flex\lv_flex.c" />
    <ClCompile Include="lvgl\src\extra\layouts\grid\lv_grid.c" />
    <ClCompile Include="lvgl\src\extra\libs\bmp\lv_bmp.c" />
    <ClCompile Include="lvgl\src\extra\libs\ffmpeg\lv_ffmpeg.c" />
    <ClCompile Include="lvgl\src\extra\libs\freetype\lv_freetype.c" />
    <ClCompile Include="lvgl\src\extra\libs\fsdrv\lv_fs_fatfs.c" />
    <ClCompile Include="lvgl\src\extra\libs\fsdrv\lv_fs_posix.c" />
    <ClCompile Include="lvgl\src\extra\libs\fsdrv\lv_fs_stdio.c" />
    <ClCompile Include="lvgl\src\extra\libs\fsdrv\lv_fs_win32.c" />
    <ClCompile Include="lvgl\src\extra\libs\gif\gifdec.c" />
    <ClCompile Include="lvgl\src\extra\libs\gif\lv_gif.c" />
    <ClCompile Include="lvgl\src\extra\libs\png\lodepng.c" />
    <ClCompile Include="lvgl\src\extra\libs\png\lv_png.c" />
    <ClCompile Include="lvgl\src\extra\libs\qrcode\lv_qrcode.c" />
    <ClCompile Include="lvgl\src\extra\libs\qrcode\qrcodegen.c" />
    <ClCompile Include="lvgl\src\extra\libs\rlottie\lv_rlottie.c" />
    <ClCompile Include="lvgl\src\extra\libs\sjpg\lv_sjpg.c" />
    <ClCompile Include="lvgl\src\extra\libs\sjpg\tjpgd.c" />
    <ClCompile Include="lvgl\src\extra\others\gridnav\lv_gridnav.c" />
    <ClCompile Include="lvgl\src\extra\others\monkey\lv_monkey.c" />
    <ClCompile Include="lvgl\src\extra\others\snapshot\lv_snapshot.c" />
    <ClCompile Include="lvgl\src\extra\themes\basic\lv_theme_basic.c" />
    <ClCompile Include="lvgl\src\extra\themes\default\lv_theme_default.c" />
    <ClCompile Include="lvgl\src\extra\themes\mono\lv_theme_mono.c" />
    <ClCompile Include="lvgl\src\extra\widgets\animimg\lv_animimg.c" />
    <ClCompile Include="lvgl\src\extra\widgets\calendar\lv_calendar.c" />
    <ClCompile Include="lvgl\src\extra\widgets\calendar\lv_calendar_header_arrow.c" />
    <ClCompile Include="lvgl\src\extra\widgets\calendar\lv_calendar_header_dropdown.c" />
    <ClCompile Include="lvgl\src\extra\widgets\chart\lv_chart.c" />
    <ClCompile Include="lvgl\src\extra\widgets\colorwheel\lv_colorwheel.c" />
    <ClCompile Include="lvgl\src\extra\widgets\imgbtn\lv_imgbtn.c" />
    <ClCompile Include="lvgl\src\extra\widgets\keyboard\lv_keyboard.c" />
    <ClCompile Include="lvgl\src\extra\widgets\led\lv_led.c" />
    <ClCompile Include="lvgl\src\extra\widgets\list\lv_list.c" />
    <ClCompile Include="lvgl\src\extra\widgets\menu\lv_menu.c" />
    <ClCompile Include="lvgl\src\extra\widgets\meter\lv_meter.c" />
    <ClCompile Include="lvgl\src\extra\widgets\msgbox\lv_msgbox.c" />
    <ClCompile Include="lvgl\src\extra\widgets\span\lv_span.c" />
    <ClCompile Include="lvgl\src\extra\widgets\spinbox\lv_spinbox.c" />
    <ClCompile Include="lvgl\src\extra\widgets\spinner\lv_spinner.c" />
    <ClCompile Include="lvgl\src\extra\widgets\tabview\lv_tabview.c" />
    <ClCompile Include="lvgl\src\extra\widgets\tileview\lv_tileview.c" />
    <ClCompile Include="lvgl\src\extra\widgets\win\lv_win.c" />
    <ClCompile Include="lvgl\src\extra\lv_extra.c" />
    <ClCompile Include="lvgl\src\font\lv_font.c" />
    <ClCompile Include="lvgl\src\font\lv_font_dejavu_16_persian_hebrew.c" />
    <ClCompile Include="lvgl\src\font\lv_font_fmt_txt.c" />
    <ClCompile Include="lvgl\src\font\lv_font_loader.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_10.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_12.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_12_subpx.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_14.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_16.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_18.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_20.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_22.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_24.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_26.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_28.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_28_compressed.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_30.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_32.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_34.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_36.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_38.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_40.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_42.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_44.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_46.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_48.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_8.c" />
    <ClCompile Include="lvgl\src\font\lv_font_simsun_16_cjk.c" />
    <ClCompile Include="lvgl\src\font\lv_font_unscii_16.c" />
    <ClCompile Include="lvgl\src\font\lv_font_unscii_8.c" />
    <ClCompile Include="lvgl\src\hal\lv_hal_disp.c" />
    <ClCompile Include="lvgl\src\hal\lv_hal_indev.c" />
    <ClCompile Include="lvgl\src\hal\lv_hal_tick.c" />
    <ClCompile Include="lvgl\src\misc\lv_anim.c" />
    <ClCompile Include="lvgl\src\misc\lv_anim_timeline.c" />
    <ClCompile Include="lvgl\src\misc\lv_area.c" />
    <ClCompile Include="lvgl\src\misc\lv_async.c" />
    <ClCompile Include="lvgl\src\misc\lv_bidi.c" />
    <ClCompile Include="lvgl\src\misc\lv_color.c" />
    <ClCompile Include="lvgl\src\misc\lv_fs.c" />
    <ClCompile Include="lvgl\src\misc\lv_gc.c" />
    <ClCompile Include="lvgl\src\misc\lv_ll.c" />
    <ClCompile Include="lvgl\src\misc\lv_log.c" />
    <ClCompile Include="lvgl\src\misc\lv_lru.c" />
    <ClCompile Include="lvgl\src\misc\lv_math.c" />
    <ClCompile Include="lvgl\src\misc\lv_mem.c" />
    <ClCompile Include="lvgl\src\misc\lv_printf.c" />
    <ClCompile Include="lvgl\src\misc\lv_style.c" />
    <ClCompile Include="lvgl\src\misc\lv_style_gen.c" />
    <ClCompile Include="lvgl\src\misc\lv_templ.c" />
    <ClCompile Include="lvgl\src\misc\lv_timer.c" />
    <ClCompile Include="lvgl\src\misc\lv_tlsf.c" />
    <ClCompile Include="lvgl\src\misc\lv_txt.c" />
    <ClCompile Include="lvgl\src\misc\lv_txt_ap.c" />
    <ClCompile Include="lvgl\src\misc\lv_utils.c" />
    <ClCompile Include="lvgl\src\widgets\lv_arc.c" />
    <ClCompile Include="lvgl\src\widgets\lv_bar.c" />
    <ClCompile Include="lvgl\src\widgets\lv_btn.c" />
    <ClCompile Include="lvgl\src\widgets\lv_btnmatrix.c" />
    <ClCompile Include="lvgl\src\widgets\lv_canvas.c" />
    <ClCompile Include="lvgl\src\widgets\lv_checkbox.c" />
    <ClCompile Include="lvgl\src\widgets\lv_dropdown.c" />
    <ClCompile Include="lvgl\src\widgets\lv_img.c" />
    <ClCompile Include="lvgl\src\widgets\lv_label.c" />
    <ClCompile Include="lvgl\src\widgets\lv_line.c" />
    <ClCompile Include="lvgl\src\widgets\lv_objx_templ.c" />
    <ClCompile Include="lvgl\src\widgets\lv_roller.c" />
    <ClCompile Include="lvgl\src\widgets\lv_slider.c" />
    <ClCompile Include="lvgl\src\widgets\lv_switch.c" />
    <ClCompile Include="lvgl\src\widgets\lv_table.c" />
    <ClCompile Include="lvgl\src\widgets\lv_textarea.c" />
    <ClCompile Include="lvgl\tests\src\test_cases\test_arc.c" />
    <ClCompile Include="lvgl\tests\src\test_cases\test_bar.c" />
    <ClCompile Include="lvgl\tests\src\test_cases\test_checkbox.c" />
    <ClCompile Include="lvgl\tests\src\test_cases\test_config.c" />
    <ClCompile Include="lvgl\tests\src\test_cases\test_demo_stress.c" />
    <ClCompile Include="lvgl\tests\src\test_cases\test_demo_widgets.c" />
    <ClCompile Include="lvgl\tests\src\test_cases\test_dropdown.c" />
    <ClCompile Include="lvgl\tests\src\test_cases\test_event.c" />
    <ClCompile Include="lvgl\tests\src\test_cases\test_font_loader.c" />
    <ClCompile Include="lvgl\tests\src\test_cases\test_obj_tree.c" />
    <ClCompile Include="lvgl\tests\src\test_cases\test_snapshot.c" />
    <ClCompile Include="lvgl\tests\src\test_cases\test_style.c" />
    <ClCompile Include="lvgl\tests\src\test_cases\test_switch.c" />
    <ClCompile Include="lvgl\tests\src\test_cases\test_txt.c" />
    <ClCompile Include="lvgl\tests\src\test_cases\_test_template.c" />
    <ClCompile Include="lvgl\tests\src\test_fonts\font_1.c" />
    <ClCompile Include="lvgl\tests\src\test_fonts\font_2.c" />
    <ClCompile Include="lvgl\tests\src\test_fonts\font_3.c" />
    <ClCompile Include="lvgl\tests\src\lv_test_indev.c" />
    <ClCompile Include="lvgl\tests\src\lv_test_init.c" />
    <ClCompile Include="lvgl\tests\unity\unity.c" />
    <ClCompile Include="lvgl\tests\unity\unity_support.c" />
  </ItemGroup>
  <ItemGroup>
    <None Include="lvgl\.github\ISSUE_TEMPLATE\bug-report.md" />
    <None Include="lvgl\.github\ISSUE_TEMPLATE\config.yml" />
    <None Include="lvgl\.github\ISSUE_TEMPLATE\dev-discussion.md" />
    <None Include="lvgl\.github\workflows\arduino.yml" />
    <None Include="lvgl\.github\workflows\build_micropython.yml" />
    <None Include="lvgl\.github\workflows\ccpp.yml" />
    <None Include="lvgl\.github\workflows\check_conf.yml" />
    <None Include="lvgl\.github\workflows\check_style.yml" />
    <None Include="lvgl\.github\workflows\close_old_issues.yml" />
    <None Include="lvgl\.github\workflows\compile_docs.yml" />
    <None Include="lvgl\.github\workflows\esp_upload_component.yml" />
    <None Include="lvgl\.github\workflows\main.yml" />
    <None Include="lvgl\.github\workflows\release.yml" />
    <None Include="lvgl\.github\auto-comment.yml" />
    <None Include="lvgl\.github\FUNDING.yml" />
    <None Include="lvgl\.github\pull_request_template.md" />
    <None Include="lvgl\demos\benchmark\assets\img_cogwheel_argb.png" />
    <None Include="lvgl\demos\benchmark\assets\img_cogwheel_chroma_keyed.png" />
    <None Include="lvgl\demos\benchmark\assets\img_cogwheel_indexed16.png" />
    <None Include="lvgl\demos\benchmark\assets\img_cogwheel_rgb.png" />
    <None Include="lvgl\demos\benchmark\README.md" />
    <None Include="lvgl\demos\benchmark\screenshot1.png" />
    <None Include="lvgl\demos\benchmark\screenshot2.png" />
    <None Include="lvgl\demos\keypad_encoder\README.md" />
    <None Include="lvgl\demos\keypad_encoder\screenshot1.gif" />
    <None Include="lvgl\demos\keypad_encoder\screenshot1.png" />
    <None Include="lvgl\demos\music\assets\spectrum.py" />
    <None Include="lvgl\demos\music\README.md" />
    <None Include="lvgl\demos\music\screenshot1.gif" />
    <None Include="lvgl\demos\stress\README.md" />
    <None Include="lvgl\demos\stress\screenshot1.gif" />
    <None Include="lvgl\demos\stress\screenshot1.png" />
    <None Include="lvgl\demos\widgets\assets\avatar.png" />
    <None Include="lvgl\demos\widgets\assets\clothes.png" />
    <None Include="lvgl\demos\widgets\assets\lvgl_logo.png" />
    <None Include="lvgl\demos\widgets\lv_demo_widgets.py" />
    <None Include="lvgl\demos\widgets\screenshot1.gif" />
    <None Include="lvgl\demos\widgets\screenshot1.png" />
    <None Include="lvgl\demos\lv_demos.mk" />
    <None Include="lvgl\demos\README.md" />
    <None Include="lvgl\docs\get-started\arduino.md" />
    <None Include="lvgl\docs\get-started\cmake.md" />
    <None Include="lvgl\docs\get-started\espressif.md" />
    <None Include="lvgl\docs\get-started\index.md" />
    <None Include="lvgl\docs\get-started\micropython.md" />
    <None Include="lvgl\docs\get-started\nuttx.md" />
    <None Include="lvgl\docs\get-started\nxp.md" />
    <None Include="lvgl\docs\get-started\pc-simulator.md" />
    <None Include="lvgl\docs\get-started\quick-overview.md" />
    <None Include="lvgl\docs\get-started\rt-thread.md" />
    <None Include="lvgl\docs\get-started\stm32.md" />
    <None Include="lvgl\docs\get-started\tasmota-berry.md" />
    <None Include="lvgl\docs\intro\index.md" />
    <None Include="lvgl\docs\layouts\flex.md" />
    <None Include="lvgl\docs\layouts\grid.md" />
    <None Include="lvgl\docs\layouts\index.md" />
    <None Include="lvgl\docs\libs\bmp.md" />
    <None Include="lvgl\docs\libs\ffmpeg.md" />
    <None Include="lvgl\docs\libs\freetype.md" />
    <None Include="lvgl\docs\libs\fsdrv.md" />
    <None Include="lvgl\docs\libs\gif.md" />
    <None Include="lvgl\docs\libs\index.md" />
    <None Include="lvgl\docs\libs\png.md" />
    <None Include="lvgl\docs\libs\qrcode.md" />
    <None Include="lvgl\docs\libs\rlottie.md" />
    <None Include="lvgl\docs\libs\sjpg.md" />
    <None Include="lvgl\docs\misc\align.png" />
    <None Include="lvgl\docs\misc\anim-timeline.png" />
    <None Include="lvgl\docs\misc\bidi.png" />
    <None Include="lvgl\docs\misc\boxmodel.png" />
    <None Include="lvgl\docs\misc\btn_example.png" />
    <None Include="lvgl\docs\misc\button_style_example.gif" />
    <None Include="lvgl\docs\misc\button_style_example.png" />
    <None Include="lvgl\docs\misc\codeblocks.jpg" />
    <None Include="lvgl\docs\misc\eclipse.jpg" />
    <None Include="lvgl\docs\misc\layers.png" />
    <None Include="lvgl\docs\misc\par_child1.png" />
    <None Include="lvgl\docs\misc\par_child2.png" />
    <None Include="lvgl\docs\misc\par_child3.png" />
    <None Include="lvgl\docs\misc\platformio.jpg" />
    <None Include="lvgl\docs\misc\qtcreator.jpg" />
    <None Include="lvgl\docs\misc\simple_button_example.gif" />
    <None Include="lvgl\docs\misc\simple_button_example.png" />
    <None Include="lvgl\docs\misc\symbols.png" />
    <None Include="lvgl\docs\misc\sys.png" />
    <None Include="lvgl\docs\misc\visualstudio.jpg" />
    <None Include="lvgl\docs\others\gridnav.md" />
    <None Include="lvgl\docs\others\index.md" />
    <None Include="lvgl\docs\others\monkey.md" />
    <None Include="lvgl\docs\others\snapshot.md" />
    <None Include="lvgl\docs\overview\animation.md" />
    <None Include="lvgl\docs\overview\color.md" />
    <None Include="lvgl\docs\overview\coords.md" />
    <None Include="lvgl\docs\overview\display.md" />
    <None Include="lvgl\docs\overview\drawing.md" />
    <None Include="lvgl\docs\overview\event.md" />
    <None Include="lvgl\docs\overview\file-system.md" />
    <None Include="lvgl\docs\overview\font.md" />
    <None Include="lvgl\docs\overview\image.md" />
    <None Include="lvgl\docs\overview\indev.md" />
    <None Include="lvgl\docs\overview\index.md" />
    <None Include="lvgl\docs\overview\layer.md" />
    <None Include="lvgl\docs\overview\new_widget.md" />
    <None Include="lvgl\docs\overview\object.md" />
    <None Include="lvgl\docs\overview\scroll.md" />
    <None Include="lvgl\docs\overview\style-props.md" />
    <None Include="lvgl\docs\overview\style.md" />
    <None Include="lvgl\docs\overview\timer.md" />
    <None Include="lvgl\docs\porting\display.md" />
    <None Include="lvgl\docs\porting\gpu.md" />
    <None Include="lvgl\docs\porting\indev.md" />
    <None Include="lvgl\docs\porting\index.md" />
    <None Include="lvgl\docs\porting\log.md" />
    <None Include="lvgl\docs\porting\os.md" />
    <None Include="lvgl\docs\porting\project.md" />
    <None Include="lvgl\docs\porting\sleep.md" />
    <None Include="lvgl\docs\porting\task-handler.md" />
    <None Include="lvgl\docs\porting\tick.md" />
    <None Include="lvgl\docs\widgets\core\arc.md" />
    <None Include="lvgl\docs\widgets\core\bar.md" />
    <None Include="lvgl\docs\widgets\core\btn.md" />
    <None Include="lvgl\docs\widgets\core\btnmatrix.md" />
    <None Include="lvgl\docs\widgets\core\canvas.md" />
    <None Include="lvgl\docs\widgets\core\checkbox.md" />
    <None Include="lvgl\docs\widgets\core\dropdown.md" />
    <None Include="lvgl\docs\widgets\core\img.md" />
    <None Include="lvgl\docs\widgets\core\index.md" />
    <None Include="lvgl\docs\widgets\core\label.md" />
    <None Include="lvgl\docs\widgets\core\line.md" />
    <None Include="lvgl\docs\widgets\core\roller.md" />
    <None Include="lvgl\docs\widgets\core\slider.md" />
    <None Include="lvgl\docs\widgets\core\switch.md" />
    <None Include="lvgl\docs\widgets\core\table.md" />
    <None Include="lvgl\docs\widgets\core\textarea.md" />
    <None Include="lvgl\docs\widgets\extra\animimg.md" />
    <None Include="lvgl\docs\widgets\extra\calendar.md" />
    <None Include="lvgl\docs\widgets\extra\chart.md" />
    <None Include="lvgl\docs\widgets\extra\colorwheel.md" />
    <None Include="lvgl\docs\widgets\extra\imgbtn.md" />
    <None Include="lvgl\docs\widgets\extra\index.md" />
    <None Include="lvgl\docs\widgets\extra\keyboard.md" />
    <None Include="lvgl\docs\widgets\extra\led.md" />
    <None Include="lvgl\docs\widgets\extra\list.md" />
    <None Include="lvgl\docs\widgets\extra\menu.md" />
    <None Include="lvgl\docs\widgets\extra\meter.md" />
    <None Include="lvgl\docs\widgets\extra\msgbox.md" />
    <None Include="lvgl\docs\widgets\extra\span.md" />
    <None Include="lvgl\docs\widgets\extra\spinbox.md" />
    <None Include="lvgl\docs\widgets\extra\spinner.md" />
    <None Include="lvgl\docs\widgets\extra\tabview.md" />
    <None Include="lvgl\docs\widgets\extra\tileview.md" />
    <None Include="lvgl\docs\widgets\extra\win.md" />
    <None Include="lvgl\docs\widgets\index.md" />
    <None Include="lvgl\docs\widgets\obj.md" />
    <None Include="lvgl\docs\_ext\lv_example.py" />
    <None Include="lvgl\docs\_static\css\custom.css" />
    <None Include="lvgl\docs\_static\css\fontawesome.min.css" />
    <None Include="lvgl\docs\_static\img\home_1.png" />
    <None Include="lvgl\docs\_static\img\home_2.png" />
    <None Include="lvgl\docs\_static\img\home_3.png" />
    <None Include="lvgl\docs\_static\img\home_4.png" />
    <None Include="lvgl\docs\_static\img\home_5.png" />
    <None Include="lvgl\docs\_static\img\home_6.png" />
    <None Include="lvgl\docs\_static\img\home_banner.jpg" />
    <None Include="lvgl\docs\_templates\layout.html" />
    <None Include="lvgl\docs\_templates\page.html" />
    <None Include="lvgl\docs\build.py" />
    <None Include="lvgl\docs\CHANGELOG.md" />
    <None Include="lvgl\docs\CODE_OF_CONDUCT.md" />
    <None Include="lvgl\docs\CODING_STYLE.md" />
    <None Include="lvgl\docs\conf.py" />
    <None Include="lvgl\docs\CONTRIBUTING.md" />
    <None Include="lvgl\docs\example_list.py" />
    <None Include="lvgl\docs\favicon.png" />
    <None Include="lvgl\docs\header.rst" />
    <None Include="lvgl\docs\index.md" />
    <None Include="lvgl\docs\logo_lvgl.png" />
    <None Include="lvgl\docs\requirements.txt" />
    <None Include="lvgl\docs\ROADMAP.md" />
    <None Include="lvgl\env_support\cmake\custom.cmake" />
    <None Include="lvgl\env_support\cmake\esp.cmake" />
    <None Include="lvgl\env_support\cmake\micropython.cmake" />
    <None Include="lvgl\env_support\cmake\zephyr.cmake" />
    <None Include="lvgl\env_support\cmsis-pack\gen_pack.sh" />
    <None Include="lvgl\env_support\cmsis-pack\LVGL.lvgl.1.0.0.pack" />
    <None Include="lvgl\env_support\cmsis-pack\LVGL.lvgl.pdsc" />
    <None Include="lvgl\env_support\cmsis-pack\lv_cmsis_pack.txt" />
    <None Include="lvgl\env_support\cmsis-pack\README.md" />
    <None Include="lvgl\env_support\rt-thread\SConscript" />
    <None Include="lvgl\env_support\zephyr\module.yml" />
    <None Include="lvgl\examples\anim\index.rst" />
    <None Include="lvgl\examples\anim\lv_example_anim_1.py" />
    <None Include="lvgl\examples\anim\lv_example_anim_2.py" />
    <None Include="lvgl\examples\anim\lv_example_anim_3.py" />
    <None Include="lvgl\examples\anim\lv_example_anim_timeline_1.py" />
    <None Include="lvgl\examples\arduino\LVGL_Arduino\LVGL_Arduino.ino" />
    <None Include="lvgl\examples\assets\font\lv_font_simsun_16_cjk.fnt" />
    <None Include="lvgl\examples\assets\font\montserrat-16.fnt" />
    <None Include="lvgl\examples\assets\font\montserrat-22.fnt" />
    <None Include="lvgl\examples\assets\animimg001.png" />
    <None Include="lvgl\examples\assets\animimg002.png" />
    <None Include="lvgl\examples\assets\animimg003.png" />
    <None Include="lvgl\examples\assets\caret_down.png" />
    <None Include="lvgl\examples\assets\imgbtn_left.png" />
    <None Include="lvgl\examples\assets\imgbtn_mid.png" />
    <None Include="lvgl\examples\assets\imgbtn_right.png" />
    <None Include="lvgl\examples\assets\img_caret_down.png" />
    <None Include="lvgl\examples\assets\img_cogwheel_argb.png" />
    <None Include="lvgl\examples\assets\img_cogwheel_chroma_keyed.png" />
    <None Include="lvgl\examples\assets\img_cogwheel_indexed16.png" />
    <None Include="lvgl\examples\assets\img_cogwheel_rgb.png" />
    <None Include="lvgl\examples\assets\img_hand_hour.png" />
    <None Include="lvgl\examples\assets\img_hand_min.png" />
    <None Include="lvgl\examples\assets\img_skew_strip.png" />
    <None Include="lvgl\examples\assets\img_skew_strip_80x20_argb8888.fnt" />
    <None Include="lvgl\examples\assets\img_star.png" />
    <None Include="lvgl\examples\assets\img_strip.png" />
    <None Include="lvgl\examples\event\index.rst" />
    <None Include="lvgl\examples\event\lv_example_event_1.py" />
    <None Include="lvgl\examples\event\lv_example_event_2.py" />
    <None Include="lvgl\examples\event\lv_example_event_3.py" />
    <None Include="lvgl\examples\get_started\index.rst" />
    <None Include="lvgl\examples\get_started\lv_example_get_started_1.py" />
    <None Include="lvgl\examples\get_started\lv_example_get_started_2.py" />
    <None Include="lvgl\examples\get_started\lv_example_get_started_3.py" />
    <None Include="lvgl\examples\layouts\flex\index.rst" />
    <None Include="lvgl\examples\layouts\flex\lv_example_flex_1.py" />
    <None Include="lvgl\examples\layouts\flex\lv_example_flex_2.py" />
    <None Include="lvgl\examples\layouts\flex\lv_example_flex_3.py" />
    <None Include="lvgl\examples\layouts\flex\lv_example_flex_4.py" />
    <None Include="lvgl\examples\layouts\flex\lv_example_flex_5.py" />
    <None Include="lvgl\examples\layouts\flex\lv_example_flex_6.py" />
    <None Include="lvgl\examples\layouts\grid\index.rst" />
    <None Include="lvgl\examples\layouts\grid\lv_example_grid_1.py" />
    <None Include="lvgl\examples\layouts\grid\lv_example_grid_2.py" />
    <None Include="lvgl\examples\layouts\grid\lv_example_grid_3.py" />
    <None Include="lvgl\examples\layouts\grid\lv_example_grid_4.py" />
    <None Include="lvgl\examples\layouts\grid\lv_example_grid_5.py" />
    <None Include="lvgl\examples\layouts\grid\lv_example_grid_6.py" />
    <None Include="lvgl\examples\libs\bmp\example_16bit.bmp" />
    <None Include="lvgl\examples\libs\bmp\example_24bit.bmp" />
    <None Include="lvgl\examples\libs\bmp\example_32bit.bmp" />
    <None Include="lvgl\examples\libs\bmp\index.rst" />
    <None Include="lvgl\examples\libs\bmp\lv_example_bmp_1.py" />
    <None Include="lvgl\examples\libs\ffmpeg\birds.mp4" />
    <None Include="lvgl\examples\libs\ffmpeg\ffmpeg.png" />
    <None Include="lvgl\examples\libs\ffmpeg\index.rst" />
    <None Include="lvgl\examples\libs\freetype\arial.ttf" />
    <None Include="lvgl\examples\libs\freetype\index.rst" />
    <None Include="lvgl\examples\libs\freetype\lv_example_freetype_1.py" />
    <None Include="lvgl\examples\libs\gif\bulb.gif" />
    <None Include="lvgl\examples\libs\gif\img_bulb_gif.py" />
    <None Include="lvgl\examples\libs\gif\index.rst" />
    <None Include="lvgl\examples\libs\gif\lv_example_gif_1.py" />
    <None Include="lvgl\examples\libs\png\img_wink_png.py" />
    <None Include="lvgl\examples\libs\png\index.rst" />
    <None Include="lvgl\examples\libs\png\lv_example_png_1.py" />
    <None Include="lvgl\examples\libs\png\wink.png" />
    <None Include="lvgl\examples\libs\qrcode\index.rst" />
    <None Include="lvgl\examples\libs\qrcode\lv_example_qrcode_1.py" />
    <None Include="lvgl\examples\libs\rlottie\index.rst" />
    <None Include="lvgl\examples\libs\rlottie\lv_example_rlottie_1.py" />
    <None Include="lvgl\examples\libs\rlottie\lv_example_rlottie_2.py" />
    <None Include="lvgl\examples\libs\rlottie\lv_example_rlottie_approve.json" />
    <None Include="lvgl\examples\libs\rlottie\lv_example_rlottie_approve.py" />
    <None Include="lvgl\examples\libs\sjpg\index.rst" />
    <None Include="lvgl\examples\libs\sjpg\lv_example_sjpg_1.py" />
    <None Include="lvgl\examples\libs\sjpg\small_image.sjpg" />
    <None Include="lvgl\examples\others\gridnav\index.rst" />
    <None Include="lvgl\examples\others\monkey\index.rst" />
    <None Include="lvgl\examples\others\snapshot\index.rst" />
    <None Include="lvgl\examples\others\snapshot\lv_example_snapshot_1.py" />
    <None Include="lvgl\examples\scroll\index.rst" />
    <None Include="lvgl\examples\scroll\lv_example_scroll_1.py" />
    <None Include="lvgl\examples\scroll\lv_example_scroll_2.py" />
    <None Include="lvgl\examples\scroll\lv_example_scroll_3.py" />
    <None Include="lvgl\examples\scroll\lv_example_scroll_4.py" />
    <None Include="lvgl\examples\scroll\lv_example_scroll_5.py" />
    <None Include="lvgl\examples\scroll\lv_example_scroll_6.py" />
    <None Include="lvgl\examples\styles\index.rst" />
    <None Include="lvgl\examples\styles\lv_example_style_1.py" />
    <None Include="lvgl\examples\styles\lv_example_style_10.py" />
    <None Include="lvgl\examples\styles\lv_example_style_11.py" />
    <None Include="lvgl\examples\styles\lv_example_style_12.py" />
    <None Include="lvgl\examples\styles\lv_example_style_13.py" />
    <None Include="lvgl\examples\styles\lv_example_style_14.py" />
    <None Include="lvgl\examples\styles\lv_example_style_2.py" />
    <None Include="lvgl\examples\styles\lv_example_style_3.py" />
    <None Include="lvgl\examples\styles\lv_example_style_4.py" />
    <None Include="lvgl\examples\styles\lv_example_style_5.py" />
    <None Include="lvgl\examples\styles\lv_example_style_6.py" />
    <None Include="lvgl\examples\styles\lv_example_style_7.py" />
    <None Include="lvgl\examples\styles\lv_example_style_8.py" />
    <None Include="lvgl\examples\styles\lv_example_style_9.py" />
    <None Include="lvgl\examples\widgets\animimg\index.rst" />
    <None Include="lvgl\examples\widgets\animimg\lv_example_animimg_1.py" />
    <None Include="lvgl\examples\widgets\arc\index.rst" />
    <None Include="lvgl\examples\widgets\arc\lv_example_arc_1.py" />
    <None Include="lvgl\examples\widgets\arc\lv_example_arc_2.py" />
    <None Include="lvgl\examples\widgets\bar\index.rst" />
    <None Include="lvgl\examples\widgets\bar\lv_example_bar_1.py" />
    <None Include="lvgl\examples\widgets\bar\lv_example_bar_2.py" />
    <None Include="lvgl\examples\widgets\bar\lv_example_bar_3.py" />
    <None Include="lvgl\examples\widgets\bar\lv_example_bar_4.py" />
    <None Include="lvgl\examples\widgets\bar\lv_example_bar_5.py" />
    <None Include="lvgl\examples\widgets\bar\lv_example_bar_6.py" />
    <None Include="lvgl\examples\widgets\bar\test.py" />
    <None Include="lvgl\examples\widgets\btn\index.rst" />
    <None Include="lvgl\examples\widgets\btn\lv_example_btn_1.py" />
    <None Include="lvgl\examples\widgets\btn\lv_example_btn_2.py" />
    <None Include="lvgl\examples\widgets\btn\lv_example_btn_3.py" />
    <None Include="lvgl\examples\widgets\btnmatrix\index.rst" />
    <None Include="lvgl\examples\widgets\btnmatrix\lv_example_btnmatrix_1.py" />
    <None Include="lvgl\examples\widgets\btnmatrix\lv_example_btnmatrix_2.py" />
    <None Include="lvgl\examples\widgets\btnmatrix\lv_example_btnmatrix_3.py" />
    <None Include="lvgl\examples\widgets\calendar\index.rst" />
    <None Include="lvgl\examples\widgets\calendar\lv_example_calendar_1.py" />
    <None Include="lvgl\examples\widgets\canvas\index.rst" />
    <None Include="lvgl\examples\widgets\canvas\lv_example_canvas_1.py" />
    <None Include="lvgl\examples\widgets\canvas\lv_example_canvas_2.py" />
    <None Include="lvgl\examples\widgets\chart\index.rst" />
    <None Include="lvgl\examples\widgets\chart\lv_example_chart_1.py" />
    <None Include="lvgl\examples\widgets\chart\lv_example_chart_2.py" />
    <None Include="lvgl\examples\widgets\chart\lv_example_chart_3.py" />
    <None Include="lvgl\examples\widgets\chart\lv_example_chart_4.py" />
    <None Include="lvgl\examples\widgets\chart\lv_example_chart_5.py" />
    <None Include="lvgl\examples\widgets\chart\lv_example_chart_6.py" />
    <None Include="lvgl\examples\widgets\chart\lv_example_chart_7.py" />
    <None Include="lvgl\examples\widgets\chart\lv_example_chart_8.py" />
    <None Include="lvgl\examples\widgets\chart\lv_example_chart_9.py" />
    <None Include="lvgl\examples\widgets\checkbox\index.rst" />
    <None Include="lvgl\examples\widgets\checkbox\lv_example_checkbox_1.py" />
    <None Include="lvgl\examples\widgets\colorwheel\index.rst" />
    <None Include="lvgl\examples\widgets\colorwheel\lv_example_colorwheel_1.py" />
    <None Include="lvgl\examples\widgets\dropdown\index.rst" />
    <None Include="lvgl\examples\widgets\dropdown\lv_example_dropdown_1.py" />
    <None Include="lvgl\examples\widgets\dropdown\lv_example_dropdown_2.py" />
    <None Include="lvgl\examples\widgets\dropdown\lv_example_dropdown_3.py" />
    <None Include="lvgl\examples\widgets\img\index.rst" />
    <None Include="lvgl\examples\widgets\img\lv_example_img_1.py" />
    <None Include="lvgl\examples\widgets\img\lv_example_img_2.py" />
    <None Include="lvgl\examples\widgets\img\lv_example_img_3.py" />
    <None Include="lvgl\examples\widgets\img\lv_example_img_4.py" />
    <None Include="lvgl\examples\widgets\imgbtn\index.rst" />
    <None Include="lvgl\examples\widgets\imgbtn\lv_example_imgbtn_1.py" />
    <None Include="lvgl\examples\widgets\keyboard\index.rst" />
    <None Include="lvgl\examples\widgets\keyboard\lv_example_keyboard_1.py" />
    <None Include="lvgl\examples\widgets\label\index.rst" />
    <None Include="lvgl\examples\widgets\label\lv_example_label_1.py" />
    <None Include="lvgl\examples\widgets\label\lv_example_label_2.py" />
    <None Include="lvgl\examples\widgets\label\lv_example_label_3.py" />
    <None Include="lvgl\examples\widgets\led\index.rst" />
    <None Include="lvgl\examples\widgets\led\lv_example_led_1.py" />
    <None Include="lvgl\examples\widgets\line\index.rst" />
    <None Include="lvgl\examples\widgets\line\lv_example_line_1.py" />
    <None Include="lvgl\examples\widgets\list\index.rst" />
    <None Include="lvgl\examples\widgets\list\lv_example_list_1.py" />
    <None Include="lvgl\examples\widgets\list\lv_example_list_2.py" />
    <None Include="lvgl\examples\widgets\list\test.py" />
    <None Include="lvgl\examples\widgets\menu\index.rst" />
    <None Include="lvgl\examples\widgets\menu\lv_example_menu_1.py" />
    <None Include="lvgl\examples\widgets\menu\lv_example_menu_2.py" />
    <None Include="lvgl\examples\widgets\menu\lv_example_menu_3.py" />
    <None Include="lvgl\examples\widgets\menu\lv_example_menu_4.py" />
    <None Include="lvgl\examples\widgets\meter\index.rst" />
    <None Include="lvgl\examples\widgets\meter\lv_example_meter_1.py" />
    <None Include="lvgl\examples\widgets\meter\lv_example_meter_2.py" />
    <None Include="lvgl\examples\widgets\meter\lv_example_meter_3.py" />
    <None Include="lvgl\examples\widgets\meter\lv_example_meter_4.py" />
    <None Include="lvgl\examples\widgets\msgbox\index.rst" />
    <None Include="lvgl\examples\widgets\msgbox\lv_example_msgbox_1.py" />
    <None Include="lvgl\examples\widgets\obj\index.rst" />
    <None Include="lvgl\examples\widgets\obj\lv_example_obj_1.py" />
    <None Include="lvgl\examples\widgets\obj\lv_example_obj_2.py" />
    <None Include="lvgl\examples\widgets\roller\index.rst" />
    <None Include="lvgl\examples\widgets\roller\lv_example_roller_1.py" />
    <None Include="lvgl\examples\widgets\roller\lv_example_roller_2.py" />
    <None Include="lvgl\examples\widgets\roller\lv_example_roller_3.py" />
    <None Include="lvgl\examples\widgets\slider\index.rst" />
    <None Include="lvgl\examples\widgets\slider\lv_example_slider_1.py" />
    <None Include="lvgl\examples\widgets\slider\lv_example_slider_2.py" />
    <None Include="lvgl\examples\widgets\slider\lv_example_slider_3.py" />
    <None Include="lvgl\examples\widgets\span\index.rst" />
    <None Include="lvgl\examples\widgets\span\lv_example_span_1.py" />
    <None Include="lvgl\examples\widgets\spinbox\index.rst" />
    <None Include="lvgl\examples\widgets\spinbox\lv_example_spinbox_1.py" />
    <None Include="lvgl\examples\widgets\spinner\index.rst" />
    <None Include="lvgl\examples\widgets\spinner\lv_example_spinner_1.py" />
    <None Include="lvgl\examples\widgets\switch\index.rst" />
    <None Include="lvgl\examples\widgets\switch\lv_example_switch_1.py" />
    <None Include="lvgl\examples\widgets\table\index.rst" />
    <None Include="lvgl\examples\widgets\table\lv_example_table_1.py" />
    <None Include="lvgl\examples\widgets\table\lv_example_table_2.py" />
    <None Include="lvgl\examples\widgets\tabview\index.rst" />
    <None Include="lvgl\examples\widgets\tabview\lv_example_tabview_1.py" />
    <None Include="lvgl\examples\widgets\tabview\lv_example_tabview_2.py" />
    <None Include="lvgl\examples\widgets\textarea\index.rst" />
    <None Include="lvgl\examples\widgets\textarea\lv_example_textarea_1.py" />
    <None Include="lvgl\examples\widgets\textarea\lv_example_textarea_2.py" />
    <None Include="lvgl\examples\widgets\textarea\lv_example_textarea_3.py" />
    <None Include="lvgl\examples\widgets\tileview\index.rst" />
    <None Include="lvgl\examples\widgets\tileview\lv_example_tileview_1.py" />
    <None Include="lvgl\examples\widgets\win\index.rst" />
    <None Include="lvgl\examples\widgets\win\lv_example_win_1.py" />
    <None Include="lvgl\examples\examples.mk" />
    <None Include="lvgl\examples\header.py" />
    <None Include="lvgl\examples\test_ex.sh" />
    <None Include="lvgl\scripts\built_in_font\built_in_font_gen.py" />
    <None Include="lvgl\scripts\built_in_font\DejaVuSans.ttf" />
    <None Include="lvgl\scripts\built_in_font\FontAwesome5-Solid+Brands+Regular.woff" />
    <None Include="lvgl\scripts\built_in_font\generate_all.py" />
    <None Include="lvgl\scripts\built_in_font\Montserrat-Medium.ttf" />
    <None Include="lvgl\scripts\built_in_font\SimSun.woff" />
    <None Include="lvgl\scripts\built_in_font\unscii-8.ttf" />
    <None Include="lvgl\scripts\release\com.py" />
    <None Include="lvgl\scripts\release\commits.txt" />
    <None Include="lvgl\scripts\release\patch.py" />
    <None Include="lvgl\scripts\release\release.py" />
    <None Include="lvgl\scripts\.gitignore" />
    <None Include="lvgl\scripts\build_html_examples.sh" />
    <None Include="lvgl\scripts\changelog-template.hbs" />
    <None Include="lvgl\scripts\changelog_gen.sh" />
    <None Include="lvgl\scripts\code-format.cfg" />
    <None Include="lvgl\scripts\code-format.py" />
    <None Include="lvgl\scripts\cppcheck_run.sh" />
    <None Include="lvgl\scripts\Doxyfile" />
    <None Include="lvgl\scripts\filetohex.py" />
    <None Include="lvgl\scripts\find_version.sh" />
    <None Include="lvgl\scripts\genexamplelist.sh" />
    <None Include="lvgl\scripts\infer_run.sh" />
    <None Include="lvgl\scripts\install-prerequisites.sh" />
    <None Include="lvgl\scripts\jpg_to_sjpg.py" />
    <None Include="lvgl\scripts\lv_conf_internal_gen.py" />
    <None Include="lvgl\scripts\style_api_gen.py" />
    <None Include="lvgl\src\core\lv_core.mk" />
    <None Include="lvgl\src\draw\sdl\lv_draw_sdl.mk" />
    <None Include="lvgl\src\draw\sdl\README.md" />
    <None Include="lvgl\src\draw\sw\lv_draw_sw.mk" />
    <None Include="lvgl\src\draw\lv_draw.mk" />
    <None Include="lvgl\src\extra\libs\freetype\arial.ttf" />
    <None Include="lvgl\src\extra\extra.mk" />
    <None Include="lvgl\src\extra\README.md" />
    <None Include="lvgl\src\font\korean.ttf" />
    <None Include="lvgl\src\font\lv_font.mk" />
    <None Include="lvgl\src\gpu\lv_gpu.mk" />
    <None Include="lvgl\src\hal\lv_hal.mk" />
    <None Include="lvgl\src\misc\lv_misc.mk" />
    <None Include="lvgl\src\widgets\lv_widgets.mk" />
    <None Include="lvgl\tests\ref_imgs\dropdown_1.png" />
    <None Include="lvgl\tests\ref_imgs\dropdown_2.png" />
    <None Include="lvgl\tests\ref_imgs\scr1.png" />
    <None Include="lvgl\tests\src\test_fonts\font_1.fnt" />
    <None Include="lvgl\tests\src\test_fonts\font_2.fnt" />
    <None Include="lvgl\tests\src\test_fonts\font_3.fnt" />
    <None Include="lvgl\tests\unity\generate_test_runner.rb" />
    <None Include="lvgl\tests\unity\run_test.erb" />
    <None Include="lvgl\tests\unity\type_sanitizer.rb" />
    <None Include="lvgl\tests\.gitignore" />
    <None Include="lvgl\tests\CMakeLists.txt" />
    <None Include="lvgl\tests\config.yml" />
    <None Include="lvgl\tests\main.py" />
    <None Include="lvgl\tests\README.md" />
    <None Include="lvgl\.codecov.yml" />
    <None Include="lvgl\.editorconfig" />
    <None Include="lvgl\.git" />
    <None Include="lvgl\.gitignore" />
    <None Include="lvgl\CMakeLists.txt" />
    <None Include="lvgl\component.mk" />
    <None Include="lvgl\idf_component.yml" />
    <None Include="lvgl\Kconfig" />
    <None Include="lvgl\library.json" />
    <None Include="lvgl\library.properties" />
    <None Include="lvgl\LICENCE.txt" />
    <None Include="lvgl\lvgl.mk" />
    <None Include="lvgl\README.md" />
    <None Include="lvgl\README_zh.md" />
    <None Include="lvgl\SConscript" />
  </ItemGroup>
</Project>