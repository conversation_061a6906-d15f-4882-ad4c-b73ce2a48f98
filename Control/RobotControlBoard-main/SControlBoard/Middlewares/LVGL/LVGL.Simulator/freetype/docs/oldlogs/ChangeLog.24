2013-05-08  <PERSON>  <<EMAIL>>

	* Version 2.4.12 released.
	==========================


	Tag sources with `VER-2-4-12'.

	* docs/VERSION.DLL: Update documentation and bump version number to
	2.4.12.

	* <PERSON><PERSON><PERSON><PERSON>, Jam<PERSON><PERSON> (RefDoc),
	builds/win32/vc2005/freetype.vcproj, builds/win32/vc2005/index.html,
	builds/win32/vc2008/freetype.vcproj, builds/win32/vc2008/index.html,
	builds/win32/vc2010/freetype.vcxproj, builds/win32/vc2010/index.html,
	builds/win32/visualc/freetype.dsp,
	builds/win32/visualc/freetype.vcproj,
	builds/win32/visualc/index.html, builds/win32/visualce/freetype.dsp,
	builds/win32/visualce/freetype.vcproj,
	builds/win32/visualce/index.html,
	builds/wince/vc2005-ce/freetype.vcproj,
	builds/wince/vc2005-ce/index.html,
	builds/wince/vc2008-ce/freetype.vcproj,
	builds/wince/vc2008-ce/index.html: s/2.4.11/2.4.12/, s/2411/2412/.

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 12.

	* builds/unix/configure.raw (version_info): Set to 16:1:10.

2013-05-08  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.

2013-05-08  Werner Lemberg  <<EMAIL>>

	* src/autofit/aflatin.c (af_latin_metrics_scale_dim): Typo.

2013-05-05  Werner Lemberg  <<EMAIL>>

	Synchronize `ftconfig.h'.

	* builds/unix/ftconfig.in: Updated.

2013-05-05  Werner Lemberg  <<EMAIL>>

	Fix compilation with C++.

	* src/base/md5.c (body): Use proper cast.

2013-05-05  Werner Lemberg  <<EMAIL>>

	Fix 64bit compilation issues.

	* include/freetype/config/ftconfig.h [FT_LONG64]: Typedef
	`FT_Int64' here.

	* src/base/ftcalc.c: Remove typedef of `FT_Int64'.
	(FT_DivFix): Fix cast.
	* src/base/fttrigon.c: Remove typedef of `FT_Int64'.

2013-05-05  Werner Lemberg  <<EMAIL>>

	[raster] Fix clang issues.

	Fix suggested by <<EMAIL>>.

	* src/raster/ftraster.c (ULong): New typedef.
	(SCALED): Add proper cast.

2013-05-04  Werner Lemberg  <<EMAIL>>

	Fix clang fixes.

	* src/base/fttrigon.c (ft_trig_prenorm, FT_Vector_Rotate): Use
	correct types.

	* src/cff/cf2intrp.c (cf2_interpT2CharString) <default>: Force
	unsigned for computations.
	* src/cff/cffgload.c (cff_decoder_parse_charstrings): Ditto.
	* src/cff/cffparse.c (cff_parse_integer): Ditto.

	* src/psaux/t1decode.c (t1_decoder_parse_charstrings): Ditto.

2013-05-04  Werner Lemberg  <<EMAIL>>

	[cff] Make Adobe CFF engine work correctly on 64bit hosts.

	Reported by numerous people on the `freetype-devel' list.  Without
	this fix, glyphs aren't properly aligned on a common baseline.

	On 64bit systems, `FT_Pos' expands to `long int', having a width of
	64bit.  `CF2_Fixed' expands to `int' which is normally 32bit wide on
	64bit hosts also.  Wrong casts filled up the blues arrays with
	incorrect values.  Note that all blues values are accessed with the
	`cf2_blueToFixed' macro which handles the 64bit to 32bit conversion.

	* src/cff/cf2ft.h (cf2_getBlueValues, cf2_getOtherBlues,
	cf2_getFamilyBlues, cf2_getFamilyOtherBlues): Use `FT_Pos' for
	`data', not `CF2_Fixed'.
	* src/cff/cf2ft.c (cf2_getBlueValues, cf2_getOtherBlues,
	cf2_getFamilyBlues, cf2_getFamilyOtherBlues): Updated.
	* src/cff/cf2blues.c (cf2_blues_init): Updated.

2013-05-04  Werner Lemberg  <<EMAIL>>

	More fixes for clang's `sanitize' feature.

	* src/base/ftcalc.c (FT_DivFix): Use unsigned values for
	computations which use the left shift operator and convert to signed
	as the last step.
	* src/base/fttrigon.c (ft_trig_prenorm, FT_Vector_Rotate,
	FT_Vector_Length, FT_Vector_Polarize): Ditto.

	* src/cff/cffgload.c (cff_decoder_parse_charstrings): Simplify.
	* src/cff/cffload.c (cff_subfont_load): Fix constant.
	* src/cff/cffparse.c (cff_parse_integer, cff_parse_real, do_fixed,
	cff_parse_fixed_dynamic): Use unsigned values for computations which
	use the left shift operator and convert to signed as the last step.

	* src/cid/cidload.c (cid_get_offset): Ditto.

	* src/psaux/psconv.c (PS_Conv_ToFixed): Ditto.
	* src/psaux/t1decode.c (t1_decoder_parse_charstrings): Ditto.

	* src/truetype/ttinterp.c (TT_MulFix14, TT_DotFix14): Ditto.

2013-05-04  Werner Lemberg  <<EMAIL>>

	Fix errors reported by clang's `sanitize' feature.

	* include/freetype/internal/ftstream.h: Simplify and fix integer
	extraction macros.
	(FT_INT8_, FT_BYTE_I16, FT_BYTE_I32, FT_INT8_I16, FT_INT8_I32,
	FT_INT8_I32, FT_INT8_U32): Removed.
	(FT_PEEK_SHORT, FT_PEEK_LONG, FT_PEEK_OFF3, FT_PEEK_SHORT_LE,
	FT_PEEK_LONG_LE, FT_PEEK_OFF3_LE): Use unsigned values for
	computations and convert to signed as the last step.

	* src/cff/cf2fixed.h (cf2_intToFixed, cf2_fixedToInt,
	cf2_fracToFixed): Avoid shifts of negative values.
	(cf2_intToFrac, cf2_fixedToFrac, cf2_fixedTo26Dot6): Removed,
	unused.

	* src/cff/cf2intrp.c (cf2_interpT2CharString) <cf2_cmdEXTENDEDNMBR,
	default>: Use unsigned values for computations and convert to signed
	as the last step.
	Use proper types in tracing messages.

	* src/cff/cffgload.c (cff_decoder_parse_charstrings): Use unsigned
	values for computation of operands and convert to signed as the last
	step.
	Use proper type in tracing message.

2013-05-03  Werner Lemberg  <<EMAIL>>

	* src/cff/cf2blues.c: Remove dead code.

2013-05-02  Chris Liddell  <<EMAIL>>

	* src/cff/cffgload.c: Include FT_CFF_DRIVER_H.

2013-04-27  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.
	* README: Improved.

2013-04-13  Werner Lemberg  <<EMAIL>>

	[cff] Add a new Type 2 interpreter and hinter.

	This work, written by Dave Arnold <<EMAIL>> and fully
	integrated into FreeType by me, is a donation by Adobe in
	collaboration with Google.  It is vastly superior to the old CFF
	engine, and it will replace it soon.  Right now, it is still off by
	default, and you have to explicitly select it using the new
	`hinting-engine' property of the cff driver.

	For convenience, (most of) the new files are committed separately.

	* include/freetype/config/ftheader.h (FT_CFF_DRIVER_H): New macro.
	* include/freetype/ftcffdrv.h: New file to access CFF driver
	properties.
	* include/freetype/fterrdef.h (FT_Err_Glyph_Too_Big): New error
	code.
	* include/freetype/internal/fttrace.h: Add `cf2blues', `cf2hints',
	and `cf2interp'.

	* src/cff/cffgload.h (CFF_SubFont): New member `current_subfont'.
	* src/cff/cffobjs.h (CFF_DriverRec): New members `hinting_engine'
	and `no_stem_darkening'.
	* src/cff/cfftypes.h (CFF_FontRec): New member `cf2_instance'.

	* src/cff/cff.c: Include new files.
	* src/cff/cffdrivr.c (cff_property_set, cff_property_get): Handle
	`hinting-engine' and `no-stem-darkening' properties (only the Adobe
	engine listens to them).
	* src/cff/cffgload.c: Include `cf2ft.h'.
	(cff_decoder_prepare): Initialize `current_subfont'.
	(cff_build_add_point): Handle Adobe engine which uses 16.16
	coordinates.
	(cff_slot_load): Handle FT_LOAD_NO_SCALE and FT_LOAD_NO_HINTING
	separately.
	Choose rendering engine based on `hinting_engine' property.
	* src/cff/cffload.c (cff_font_done): Call finalizer of the Adobe
	engine.
	* src/cff/cffobjs.c: Include FT_CFF_DRIVER_H.
	(cff_driver_init): Set default property values.

	* src/cff/rules.mk (CFF_DRV_SRC, CFF_DRV_H): Add new files.

	* src/cff/cf2*.*: New files, containing the Adobe engine.

2013-04-12  Werner Lemberg  <<EMAIL>>

	[cff] Minor code administration issues.

	* src/cff/cffgload.c (check_points): Rename to...
	(cff_check_points): ...this and make it FT_LOCAL.
	(cff_builder_add_point, cff_builder_add_point1,
	cff_builder_start_point, cff_builder_close_contour,
	cff_lookup_glyph_by_stdcharcode, cff_get_glyph_data,
	cff_free_glyph_data): Make them FT_LOCAL.

	* src/cff/cffgload.h: Updated.

2013-04-12  Werner Lemberg  <<EMAIL>>

	Add output bitmap checksums.

	Use `FT2_DEBUG=bitmap:3' for tracing.

	* src/base/md5.c, src/base/md5.h: New files, taken from

	  https://openwall.info/wiki/people/solar/software/public-domain-source-code/md5

	* include/freetype/internal/fttrace.h: Add `bitmap'.

	* src/base/ftobjs.c [FT_DEBUG_LEVEL_TRACE]: Include `md5.c'

	(FT_Render_Glyph_Internal) [FT_DEBUG_LEVEL_TRACE]: For tracing,
	convert resulting bitmap to a uniform format and compute a checksum.
	Use `bitmap' category for the tracing message.

	* src/base/rules.mk (BASE_H): Updated.

	* docs/LICENSE.TXT: Updated.

2013-04-12  Werner Lemberg  <<EMAIL>>

	[cff] Add framework for CFF properties.

	* include/freetype/internal/ftserv.h (FT_DEFINE_SERVICEDESCREC7):
	New macro.

	* src/cff/cffdrivr.c: Include FT_SERVICE_PROPERTIES_H.
	(cff_property_set, cff_property_get): New functions, still empty.
	Define `cff_service_properties' service.
	Update `cff_services'.

	* src/cff/cffpic.h: Include FT_SERVICE_PROPERTIES_H.
	(CFF_SERVICE_PROPERTIES_GET): New macro.
	(CffModulePIC): Add `cff_service_properties'.

2013-04-03  Werner Lemberg  <<EMAIL>>

	[bdf] Fix Savannah bug #38589.

	* src/bdf/bdflib.c (_bdf_readstream): Thinko.

2013-03-31  Werner Lemberg  <<EMAIL>>

	* configure: Use egrep, not grep.

	Problem reported Mojca Miklavec <<EMAIL>>.

2013-03-29  Werner Lemberg  <<EMAIL>>

	* include/freetype/ftlcdfil.h: Add description of color filtering.

	Based on a contribution from Antti S. Lankila <<EMAIL>>
	(Savannah bug #38607).

2013-03-23  Werner Lemberg  <<EMAIL>>

	[autofit] Minor.

	* src/autofit/afmodule.c (af_property_set): Typo.
	(af_autofitter_init, af_autofitter_done): Use cast.

2013-03-21  Werner Lemberg  <<EMAIL>>

	* configure: Automatically test for `gmake' also.

	Suggested by Mojca Miklavec <<EMAIL>>.

2013-03-21  Peter Breitenlohner  <<EMAIL>>

	Respect CONFIG_SHELL from the environment.

	Some large packages using FreeType have to use a broken (deficient)
	/bin/sh.  The configure scripts (as generated by Autoconf) are
	clever enough to find a better shell and put that one into the
	environment variable CONFIG_SHELL.  If that environment variable is
	already set the script skips the test and assumes to be already
	running under a good shell.

	* builds/unix/detect.mk: Honour CONFIG_SHELL.
	* builds/unix/unix-def.in (SHELL): Define.

2013-03-21  Werner Lemberg  <<EMAIL>>

	Fix Savannah patch #7971.

	* configure: Handle MAKE environment variable also.

2013-03-17  Werner Lemberg  <<EMAIL>>

	Fix Savannah bug #38538.

	* builds/amiga/src/base/ftdebug.c, builds/win32/ftdebug.c,
	builds/wince/ftdebug.c (FT_Throw): Add function.

2013-03-17  Werner Lemberg  <<EMAIL>>

	[raster] Remove dead code.

	* src/raster/rastpic.c (ft_raster1_renderer_class_pic_init)
	src/smooth/ftspic.c (ft_smooth_renderer_class_pic_init): Do it.

2013-03-17  Werner Lemberg  <<EMAIL>>

	* src/pshinter/pshpic.h (GET_PIC): Use correct container.

2013-03-15  Werner Lemberg  <<EMAIL>>

	* include/freetype/ftmoderr.h: Fix commit from 2013-03-11.

	The previous version was not backward compatible.  Reported by
	Behdad.

2013-03-14  Werner Lemberg  <<EMAIL>>

	*/*: Use FT_ERR_EQ, FT_ERR_NEQ, and FT_ERR where appropriate.

	FT_Err_XXX and friends are no longer directly used in the source
	code.

2013-03-14  Werner Lemberg  <<EMAIL>>

	New error management macros.

	* include/freetype/fterrors.h (FT_ERR_XCAT, FT_ERR_CAT): Move to...
	* include/freetype/fttypes.h: ... this file.
	(FT_ERR, FT_ERR_EQ, FT_ERR_NEQ, FT_MODERR_EQ, FT_MODERR_NEQ): New
	macros.

	* include/freetype/freetype.h: Updated.

2013-03-14  Werner Lemberg  <<EMAIL>>

	*/*: Use FT_Err_Ok only.

	This is a purely mechanical conversion.

2013-03-14  Werner Lemberg  <<EMAIL>>

	*/*: Use `FT_THROW'.

	This is essentially a mechanical conversion, adding inclusion of
	`FT_INTERNAL_DEBUG_H' where necessary, and providing the macros for
	stand-alone compiling modes of the rasterizer modules.

	To convert the remaining occurrences of FT_Err_XXX and friends it is
	necessary to rewrite the code.  Note, however, that it doesn't harm
	if some cases are not handled since FT_THROW is a no-op.

2013-03-13  Werner Lemberg  <<EMAIL>>

	Introduce `FT_THROW' macro.

	The idea is to replace code like

	  return FT_Err_Foo_Bar;

	or

	  return CFF_Err_Foo_Bar;

	with

	  return FT_THROW( Foo_Bar );

	The FT_THROW macro has two functions:

	  . It hides the module specific prefix.

	  . In debug mode, it calls the empty function `FT_Throw' which can
	    be thus used to set a breakpoint.

	* include/freetype/internal/ftdebug.h (FT_THROW): New macro.
	(FT_Throw): New prototype.
	* src/base/ftdebug.c (FT_Throw): New function.

2013-03-12  Werner Lemberg  <<EMAIL>>

	Remove `FT_KEEP_ERR_PREFIX'.

	The idea is to always have FT_ERR_PREFIX available internally.

	* include/freetype/fterrors.h: Use FT2_BUILD_LIBRARY to guard
	undefinition of FT_ERR_PREFIX

	* src/gxvalid/gxverror.h, src/otvalid/otverror.h,
	src/sfnt/sferrors.h: Updated.

2013-03-11  Werner Lemberg  <<EMAIL>>

	[gxvalid] Fix module error.

	* src/gxvalid/gxverror.h (FT_ERR_BASE): Define as
	FT_Mod_Err_GXvalid.
	* include/freetype/ftmoderr.h: Add module error for `GXvalid'.

2013-03-11  Werner Lemberg  <<EMAIL>>

	Always use module related error codes.

	* src/cff/cffobjs.c (cff_face_init), src/type1/t1objs.c
	(T1_Face_Init), src/type42/t42objs.c (T42_Face_Init): Use
	`FT_ERROR_BASE'.

	* src/type1/t1load.c (parse_encoding): Use
	T1_Err_Unknown_File_Format.

2013-03-08  Werner Lemberg  <<EMAIL>>

	[cff] Set `linear{Hori,Vert}Advance' for embedded bitmaps also.

	Problem reported by Khaled Hosny <<EMAIL>>.

	* src/cff/cffgload.c (cff_slot_load): Implement it.

2013-02-23  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Fix commit ab02d9e8.

	* src/base/ftbbox.c (BBox_Cubic_Check): Change scaling to msb of 22.

2013-02-19  Alexei Podtelezhnikov  <<EMAIL>>

	[base] New bisecting BBox_Cubic_Check (disabled).

	* src/base/ftbbox.c (BBox_Cubic_Check): New bisecting algorithm
	for extremum search built around simple condition that defines
	which half contains the extremum.

2013-02-18  Alexei Podtelezhnikov  <<EMAIL>>

	[tools] Update BBox testing tool.

	* src/tools/test_bbox.c: Add another cubic outline with exact BBox.
	(REPEAT): Increase the number of benchmarking cycles.
	(profile_outline): Tweak output formatting.

2013-02-02  Werner Lemberg  <<EMAIL>>

	Fix Savannah bug #38235.

	* builds/unix/configure.raw: Don't generate `freetype-config' and
	`freetype.pc'.

	* builds/unix/unix-def.in (FT2_EXTRA_LIBS, LIBBZ2, LIBZ,
	build_libtool_libs, ft_version): New variables to be substituted.
	(freetype-config, freetype.pc): New rules to generate those files.

	* builds/unix/freetype-config.in: Remove code for handling `rpath'.
	The use of $rpath has been accidentally removed in a patch from
	2009-12-22, and apparently noone has missed it since.
	Use `%' instead of `@' as a variable substitution marker.
	Use quotes.

	* builds/unix/freetype.in: Use `%' instead of `@' as a variable
	substitution marker.
	Use quotes.

2013-02-07  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttobjs.c (tt_size_run_prep): Reset more GS variables.

	BTW, Greg agrees that the OpenType specification is missing the list
	of GS variables which will always be reset to the default values
	after the `prep' table has been executed.

2013-02-06  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttobjs.c (tt_size_run_prep): Reset reference points.

	Up to now, we simply took a snapshot of the Graphics State after the
	`prep' table has been executed, and right before a glyph's bytecode
	was run it got reloaded.  However, as Greg Hitchcock has told us in
	private communication, reference points get reset to zero in the MS
	rasterizer and we follow in due course.  While reasonable, this is
	undocumented behaviour.

	Most notably, this fixes the rendering of Arial's `x' glyph in
	subpixel hinting mode.

2013-02-05  Werner Lemberg  <<EMAIL>>

	[truetype] A better fix for Savannah bug #38211.

	* src/truetype/ttinterp.c (Ins_IP): Implement identical behaviour to
	MS rasterizer if rp1 == rp2 (confirmed by Greg Hitchcock).

2013-02-01  Alexei Podtelezhnikov  <<EMAIL>>

	[pcf] Streamline parsing of PCF encoding table.

	* src/pcf/pcfread.c (pcf_get_encodings): Use simpler double for-loop.
	Reallocate array instead of using temporary storage.

2013-02-01  Werner Lemberg  <<EMAIL>>

	Fix Savannah bug #38227.

	* builds/unix/freetype-config.in: Set LC_ALL.

2013-02-01  Werner Lemberg  <<EMAIL>>

	Fix Savannah bug #38221.

	This complements commit 83c0ebab.

	* src/base/ftcalc.c (FT_MulDiv_No_Round): Don't enclose with
	`TT_USE_BYTECODE_INTERPRETER'.

2013-02-01  Werner Lemberg  <<EMAIL>>

	[truetype] Fix Savannah bug #38211.

	* src/truetype/ttinterp.c (Ins_IP): Make FreeType behave identical
	to other interpreters if rp1 == rp2 (which is invalid).

2013-01-28  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Small optimization of BBox calculation.

	* src/base/ftbbox.c (BBox_Cubic_Check): Use FT_MSB function in
	scaling algorithm.

2013-01-26  Infinality  <<EMAIL>>

	[truetype] Minor formatting fix.

	* src/truetype/ttinterp.c: Updated.
	(DO_RS): Fix indentation.

2013-01-26  Infinality  <<EMAIL>>

	[truetype] Fix rasterizer_version logic in sph.

	* src/truetype/ttsubpix.c: Updated.
	(ALWAYS_SKIP_DELTAP_Rules): Remove rule for Trebuchet MS.
	(sph_set_tweaks): Fix `rasterizer_version' logic.

2013-01-26  Infinality  <<EMAIL>>

	[truetype] Align more to ClearType whitepaper for sph.

	* include/freetype/internal/tttypes.h (TT_FaceRec): Add flags
	for detected opcode patterns and compatibility mode.

	* src/truetype/ttgload.c (tt_loader_init): Complete conditional.

	* src/truetype/ttinterp.c: Updated.
	Remove SPH_DEBUG and replace with FT_TRACE7.
	(DO_RS): More conditions.
	(Ins_FDEF): Add more opcode detection patterns.
	More specific conditions when flagging an fdef.
	Make compatibility mode only turn on when delta fdefs are found.
	(Ins_CALL, Ins_LOOPCALL): Set flags for currently executed fdef.
	(Ins_SHPIX): Remove logic to handle ttfautohinted fonts.
	Simplify conditionals where possible.
	Use `&' instead of `%' operator for dumb compilers.
	(Ins_MIAP): Adjust twilight zone conditional.
	Ensure `ignore_x_mode' is on when testing sph conditionals.
	(Ins_MIRP): Ensure `ignore_x_mode' is on when testing sph
	conditionals.
	Do cvt cutin always when `ignore_x_mode' is active.
	Remove test for ttfautohinted fonts.
	(Ins_DELTAP): Ensure `ignore_x_mode' is on when testing sph
	conditionals.
	Do cvt cutin always when `ignore_x_mode' is active.
	Remove test for ttfautohinted fonts.
	Use `&' instead of `%' operator for dumb compilers.
	(Ins_GETINFO): Remove SPH_DEBUG and replace with FT_TRACE7.

	* src/truetype/ttinterp.h: Updated.
	(TT_ExecContextRec): Remove compatibility_mode variable.
	Add variable to indicate when executing in special fdefs for sph.

	* src/truetype/ttobjs.h: Updated.
	(TT_DefRecord): Add flags to identify special fdefs for sph.
	(TT_SizeRec): Remove unnecessary ttfautohinted variable.

	* src/truetype/ttsubpix.c: Updated.
	(COMPATIBILITY_MODE_Rules): Remove all.  Auto-detected now.
	(PIXEL_HINTING_Rules): Remove all.  Unnecessary after fixes.
	(SKIP_NONPIXEL_Y_MOVES_Rules): Remove Ubuntu.
	(SKIP_NONPIXEL_Y_MOVES_Rules_Exceptions): Add Arial Bold `N'.
	(SKIP_OFFPIXEL_Y_MOVES_Rules): Remove all.  Happens automatically
	now.
	(ROUND_NONPIXEL_Y_MOVES_Rules): Remove Ubuntu.
	(ROUND_NONPIXEL_Y_MOVES_Rules_Exceptions): Remove all.
	(NORMAL_ROUND_Rules): Remove Verdana.
	(NO_DELTAP_AFTER_IUP_Rules): Remove all.
	(sph_set_tweaks): Performance fix.  Don't run prep always.
	Adjust conditional for sph_compatibility_mode.

	* src/truetype/ttsubpix.h: Add new fdef flags for sph.

2013-01-23  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Fix broken emboldening at small sizes.

	* src/base/ftoutln.c (FT_Outline_EmboldenXY): Do not attempt to
	normalize zero-length vectors.

2013-01-25  Werner Lemberg  <<EMAIL>>

	Fix Savannah bug #38167.

	This fixes commit 83c0ebab from 2012-06-27.

	* src/truetype/ttinterp.h:
	s/TT_CONFIG_OPTION_BYTECODE_INTERPRETER/TT_USE_BYTECODE_INTERPRETER/.

2013-01-25  Xi Wang  <<EMAIL>>

	[sfnt] Fix broken pointer overflow checks.

	Many compilers such as gcc and clang optimize away pointer overflow
	checks `p + n < p', because pointer overflow is undefined behavior.
	Use a safe form `n > p_limit - p' instead.

	Also avoid possible integer overflow issues, for example, using
	`num_glyphs > ( p_limit - p ) / 2' rather than `num_glyphs * 2'
	given a large `num_glyphs'.

	* src/sfnt/ttsbit0.c (tt_sbit_decoder_load_image): Implement it.

2013-01-25  Werner Lemberg  <<EMAIL>>

	[base] Fix `make multi'.

	* src/base/ftoutln.c, src/base/fttrigon.c: Include
	FT_INTERNAL_CALC_H.

2013-01-25  David 'Digit' Turner  <<EMAIL>>

	[truetype] Fix C++ compilation.

	* src/truetype/ttsubpix.h: Updated.
	(SPH_X_SCALING_RULES_SIZE): Moved and renamed to...
	* src/truetype/ttsubpix.c (X_SCALING_RULES_SIZE): This.
	(sph_X_SCALING_Rules): Removed.
	(scale_test_tweak): Make function static.
	(sph_test_tweak_x_scaling): New function.

	* src/truetype/ttgload.c (TT_Process_Simple_Glyph): Updated.

2013-01-23  Werner Lemberg  <<EMAIL>>

	[base] Make `FT_Hypot' really internal.

	* include/freetype/fttrigon.h (FT_Hypot): Move to...
	* include/freetype/internal/ftcalc.h: This file.

	* src/base/fttrigon.c (FT_Hypot): Move to...
	* src/base/ftcalc.c: This file.
	Include FT_TRIGONOMETRY_H.

	* src/truetype/ttgload.c: Don't include FT_TRIGONOMETRY_H.

2013-01-23  Werner Lemberg  <<EMAIL>>

	[truetype] Revert change from 2013-01-22.

	FreeType's `height' value is the baseline-to-baseline distance...

	* src/truetype/ttobjs.c (tt_size_reset): Undo.

2013-01-23  Alexei Podtelezhnikov  <<EMAIL>>

	[base, truetype] New internal `FT_Hypot' function.

	* include/freetype/fttrigon.h (FT_Hypot): Declare it.
	* src/base/fttrigon.c (FT_Hypot): Define it.
	* src/truetype/ttgload.c (TT_Process_Composite_Component): Use it
	instead of explicit expressions.
	* src/truetype/ttinterp.c (Current_Ratio, Normalize): Use it instead
	of TT_VecLen.
	(TT_VecLen): Removed.

2013-01-23  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Fix integer overflow.

	* src/base/ftoutln.c (FT_Outline_EmboldenXY): Normalize incoming and
	outgoing vectors and use fixed point arithmetic.

2013-01-23  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Fix integer overflow.

	* src/base/ftoutln.c (FT_Outline_Get_Orientation): Scale the
	coordinates down to avoid overflow.

2013-01-23  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Split out MSB function.

	* src/base/fttrigon.c (ft_trig_prenorm): Borrow from here.
	* include/freetype/internal/ftcalc.h (FT_MSB): Declare here.
	* src/base/ftcalc.c (FT_MSB): Define here.

2013-01-22  Werner Lemberg  <<EMAIL>>

	[truetype] Fix font height.

	* src/truetype/ttobjs.c (tt_size_reset): The Windows rendering
	engine uses rounded values of the ascender and descender to compute
	the TrueType font height.

2013-01-16  Behdad Esfahbod  <<EMAIL>>

	[sfnt] Fix optimized sbit loader.

	It was not taking bit_depth into consideration when blitting!

	* src/sfnt/ttsbit0.c (tt_sbit_decoder_load_byte_aligned,
	* tt_sbit_decoder_load_bit_aligned): Handle bit
	depth.

2013-01-16  David 'Digit' Turner  <<EMAIL>>

	[truetype] Improve subpixel code.

	This patches fixes many issues with the ttsubpix implementation.

	1. Data tables are defined, instead of declared, in the header, and
	   thus copied into each source file that includes it.

	2. These tables were defined as global, mutable, visible variables,
	   and thus costing private RAM to every process that loads the
	   library (> 50 KB / process, this is huge!).

	   Additionally, this also made the library export the symbols
	   completely needlessly.

	3. Missing `sph_' and `SPH_' prefixes to some of the definitions.

	Note that this doesn't try to fix the incredibly inefficient storage
	format for the data tables used by the code.  This one will require
	another pass in the future.

	* src/truetype/ttinterp.h (MAX_NAME_SIZE, MAX_CLASS_MEMBERS):
	Renamed to...
	(SPH_MAX_NAME_SIZE, SPH_MAX_CLASS_MEMBERS): This.
	Update all users.

	(SPH_TweakRule, SPH_ScaleRule): Decorate with `const' where
	appropriate.

	(Font_Class): Rename to...
	(SPH_Font_Class): This.  Decorate with `const' where appropriate.

	* src/truetype/ttsubpix.h (scale_test_tweak, sph_test_tweak):
	Decorate arguments with `const' where appropriate.

	Move font tweaking tables to...

	* src/truetype/ttsubpix.c: This file and decorate them with `static'
	and `const' where appropriate.

	(X_SCALING_Rules, X_SCALING_RULES_SIZE): Renamed to...
	(sph_X_SCALING_Rules, SPH_X_SCALING_RULES_SIZE): This.
	Update all users.

2013-01-12  Alexei Podtelezhnikov  <<EMAIL>>

	[truetype] Improve accuracy of normalization of short vectors.

	Unit vector components are stored as 2.14 fixed-point numbers. In
	order to calculate all 14 bits accurately, a short vector to be
	normalized has to be upscaled to at least 14 bits before its length
	is calculated. This has been safe since accurate CORDIC algorithms
	were adopted.

	* src/truetype/ttinterp.c (Normalize): Scale short vectors by 0x4000.

2013-01-12  Alexei Podtelezhnikov  <<EMAIL>>

	[truetype] Kill very old vector normalization hacks.

	Back in the days, vector length calculations were not very accurate
	and the vector normalization function, Normalize, had to meticulously
	correct the errors for long vectors [commit b7ef2b096867]. It was no
	longer necessary after accurate CORDIC algorithms were adopted, but
	the code remained. It is time to kill it.

	* src/truetype/ttinterp.c (Normalize): Remove error compensation.
	(TT_VecLen): Remove any mention of old less accurate implementation.

2013-01-11  Werner Lemberg  <<EMAIL>>

	Disable FT_CONFIG_OPTION_OLD_INTERNALS.

	After the next release we are going to remove the code completely.

	* devel/ftoption.h, include/freetype/config/ftoption.h
	(FT_CONFIG_OPTION_OLD_INTERNALS): Comment out.
	* docs/CHANGES: Document it.

2013-01-10  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Update the overflow protection bit.

	The recent optimizations of CORDIC iterations drastically reduce the
	expansion factor.  Vector components with MSB of 29 are now safe
	from overflow.

	* src/base/fttrigon.c (FT_TRIG_SAFE_MSB): New macro.
	(ft_trig_prenorm): Use it and remove dead code.

2013-01-09  Alexei Podtelezhnikov  <<EMAIL>>

	[base, pshinter] Use FT_ABS, FT_MIN, and FT_MAX for readability.

	* src/base/ftbbox.c: Updated.
	* src/base/ftobjs.c: Updated.
	* src/base/fttrigon.c: Updated.
	* src/pshinter/pshalgo.c: Updated.
	* src/pshinter/pshrec.c: Updated.

2013-01-08  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Clean up trigonometric core.

	* src/base/fttrigon.c: Document the algorithm in a large comment.
	(FT_TRIG_COSCALE): Remove macro.
	(FT_Tan: Use `FT_TRIG_SCALE' instead.
	(FT_Cos, FT_Vector_Unit): Ditto and round the return values.

2013-01-02  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Use rounding in CORDIC iterations.

	* src/base/fttrigon.c (ft_trig_pseudo_rotate,
	ft_trig_pseudo_polarize): Improve accuracy by rounding.

2013-01-02  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Reduce trigonometric algorithms.

	After we get within 45 degrees by means of true 90-degree rotations,
	we can remove initial 45-degree CORDIC iteration and start from
	atan(1/2) pseudorotation, reducing expansion factor thereby.

	* src/base/fttrigon.c (FT_TRIG_SCALE, FT_TRIG_COSCALE): Update macros.
	(ft_trig_pseudo_rotate, ft_trig_pseudo_polarize): Update.

	* src/tools/cordic.py: Bring up to date with trigonometric core.

	* docs/CHANGES: Old typo.

2013-01-02  Alexei Podtelezhnikov  <<EMAIL>>

	* src/pshinter/pshalgo.h: Remove unused code.

2012-12-27  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgload.c (tt_loader_init): Add more tracing.

2012-12-23  Werner Lemberg  <<EMAIL>>

	[type1] Fix handling of /FontBBox in MM fonts.
	Problem reported by Del Merritt <<EMAIL>>

	If we have

	  /FontBBox { { 11 12 13 14 15 16 17 18 }
	              { 21 22 23 24 25 26 27 28 }
	              { 31 32 33 34 35 36 37 38 }
	              { 41 42 43 44 45 46 47 48 } }

	in the /Blend dictionary,  then the first BBox is { 11 21 31 41 },
	the second { 12 22 32 42 }, etc.

	* include/freetype/internal/psaux.h (T1_FieldType): Add
	`T1_FIELD_TYPE_MM_BBOX' (for temporary use).

	* src/psaux/psobjs.c (ps_parser_load_field) <T1_FIELD_TYPE_MM_BBOX>:
	Implement it.

2012-12-21  Alexei Podtelezhnikov  <<EMAIL>>

	* src/tools/cordic.py: Bring up to date with trigonometric core.

2012-12-21  Werner Lemberg  <<EMAIL>>

	Check parameters of `FT_Outline_New'.
	Problem reported by Robin Watts <<EMAIL>>.

	* src/base/ftoutln.c (FT_Outline_New_Internal): Ensure that
	`numContours' and `numPoints' fit into FT_Outline's `n_points' and
	`n_contours', respectively.

2012-12-20  Werner Lemberg  <<EMAIL>>

	* Version 2.4.11 released.
	==========================


	Tag sources with `VER-2-4-11'.

	* docs/CHANGES, docs/release: Updated.

	* docs/VERSION.DLL: Update documentation and bump version number to
	2.4.11.

	* README, Jamfile (RefDoc),
	builds/win32/vc2005/freetype.vcproj, builds/win32/vc2005/index.html,
	builds/win32/vc2008/freetype.vcproj, builds/win32/vc2008/index.html,
	builds/win32/vc2010/freetype.vcxproj, builds/win32/vc2010/index.html,
	builds/win32/visualc/freetype.dsp,
	builds/win32/visualc/freetype.vcproj,
	builds/win32/visualc/index.html, builds/win32/visualce/freetype.dsp,
	builds/win32/visualce/freetype.vcproj,
	builds/win32/visualce/index.html,
	builds/wince/vc2005-ce/freetype.vcproj,
	builds/wince/vc2005-ce/index.html,
	builds/wince/vc2008-ce/freetype.vcproj,
	builds/wince/vc2008-ce/index.html: s/2.4.10/2.4.11/, s/2410/2411/.

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 11.

	* builds/unix/configure.raw (version_info): Set to 16:0:10.

	* builds/toplevel.mk (dist): Don't include `.mailmap'.

2012-12-20  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Improve trigonometric core.

	FreeType used to rely on a 24-step iteration CORDIC algorithm to
	calculate trigonometric functions and rotate vectors. It turns out
	that once the vector is in the right half-plane, the initial rotation
	by 63 degrees is not necessary. The algorithm is perfectly capable
	to converge to any angle starting from the second 45 degree rotation.
	This patch removes the first rotation and makes it a 23-step CORDIC
	algorithm.

	* src/base/fttrigon.c (FT_TRIG_SCALE, FT_TRIG_COSCALE): Update macro
	values.
	(ft_trig_pseudo_rotate, ft_trig_pseudo_polarize): Remove initial
	rotation.

2012-12-19  Werner Lemberg  <<EMAIL>>

	* src/base/ftobjs.c (ft_property_do): Fix compiler warning.

2012-12-19  Alexei Podtelezhnikov  <<EMAIL>>

	* src/base/ftrfork.c (FT_Raccess_Guess): Switch to FT_Int counters.

2012-12-19  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Clean up trigonometric core.

	* src/base/fttrigon.c (ft_trig_pseudo_polarize): Align algorithm
	with `ft_trig_pseudo_rotate'.

2012-12-18  Infinality  <<EMAIL>>

	[truetype] Minor performance enhancement.

	* src/truetype/ttgload.c: (TT_Process_Simple_Glyph): Use FT_MulFix
	instead of FT_MulDiv.

2012-12-17  Infinality  <<EMAIL>>

	[truetype] Remove unused code and variables.

	* src/truetype/ttinterp.c: Updated.
	(Ins_FDEF): Remove opcode patterns that are not being used.

2012-12-16  Werner Lemberg  <<EMAIL>>

	Various compiler warning fixes.

	* include/freetype/internal/ftserv.h (FT_SERVICE_UNAVAILABLE): Use
	`logical not' operator instead of negation.  The idea is that `~'
	returns exactly the data type enforced by the cast to a pointer (be
	it 32bit or 64bit or whatever), while a negative integer has not
	this flexibility.
	* src/cache/ftccmap.c (FTC_CMAP_UNKNOWN): Ditto.
	* src/truetype/ttgxvar.c (ALL_POINTS, TT_Get_MM_Var): Ditto.
	* src/type/t1load.c (T1_Get_MM_Var): Ditto.
	(parse_blend_axis_types): Use cast.
	* src/bdf/bdflib.c (_bdf_readstream): Use cast.

2012-12-16  Infinality  <<EMAIL>>

	[truetype] Remove unused code and variables.  Add minor fixes.

	* src/truetype/ttsubpix.h: Updated.
	(SKIP_NONPIXEL_Y_MOVES_Rules_Exceptions): Add Trebuchet MS.
	(ALLOW_X_DMOVEX_Rules): Remove Arial characters.
	(ALLOW_X_DMOVE_Rules): Remove Arial characters.
	(RASTERIZER_35_Rules): Verdana no longer needs to be here.
	(SKIP_IUP_Rules): Formatting fix.
	(DELTAP_SKIP_EXAGGERATED_VALUES_Rules): Remove Segoe UI.
	(COMPATIBLE_WIDTHS_Rules): Add Monaco and Trebuchet MS.
	(X_SCALING_Rules): Add misc. corrective fixes.

	* src/truetype/ttgload.c: (TT_Process_Simple_Glyph): Adjust correction
	factor for emboldening during scaling.

	* src/truetype/ttinterp.h: Updated.
	(TT_ExecContextRec): Remove unused variables.

	* src/truetype/ttobjs.h: Updated.
	(TT_SizeRec): Add ttfautohinted variable.

	* src/truetype/ttinterp.c: Updated.
	(Ins_FDEF): Rework code to fix bugs and add more detection.
	(Ins_CALL): Remove unused code.
	(Ins_LOOPCALL): Remove unused code.
	(TT_RunIns): Remove unused code.
	(Ins_SHPIX): Add logic to handle ttfautohinted fonts.
	(Ins_MIRP): Don't round x in cut-in calculation.  Add logic to handle
	ttfautohinted fonts.

2012-12-16  Werner Lemberg  <<EMAIL>>

	[sfnt] Fix Savannah bug #37936.

	* src/sfnt/ttload.c (tt_face_load_gasp): Avoid memory leak.

2012-12-15  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Fix 11-year old bug.

	Since the initial commit (ebe85f59) the value of FT_TRIG_SCALE has
	always been slightly less than the correct value, which has been
	given in the comment as a hexadecimal. As a result, vector lengths
	were underestimated and rotated vectors were shortened.

	* src/base/fttrigon.c (FT_TRIG_SCALE): Fix macro value.

2012-12-15  Werner Lemberg  <<EMAIL>>

	[bdf] Fix Savannah bug #37907.

	* src/bdf/bdflib.c (_bdf_parse_glyphs) <ENCODING>: Normalize
	negative second parameter of `ENCODING' field also.

2012-12-15  Werner Lemberg  <<EMAIL>>

	[bdf] Fix Savannah bug #37906.

	* src/bdf/bdflib.c (_bdf_parse_glyphs) <ENCODING>: Use correct array
	size for checking `glyph_enc'.

2012-12-15  Werner Lemberg  <<EMAIL>>

	[bdf] Fix Savannah bug #37905.

	* src/bdf/bdflib.c (_bdf_parse_start) <STARTPROPERTIES>: Reset
	`props_size' to zero in case of allocation error; this value gets
	used in a loop in `bdf_free_font'.

2012-12-10  Alexei Podtelezhnikov  <<EMAIL>>

	[truetype] Scale F_dot_P down.

	The dot product between freeVector and projVector or cosine of
	the angle between these FT_F2Dot14 unit vectors used to be scaled up
	by 4 and routinely occupied 32 bits in an FT_Long field F_dot_P.
	This patch scales the value down by 2^14 instead, which simplifies
	its use throughout the bytecode interpreter.

	This does not lead to the loss of precision because the lower bits
	are unreliable anyway. Consider two unit vectors (1,0) and (.6,.8)
	for which the true value of F_dot_P is .6 * 0x40000000 = 0x26666666.
	These vectors are stored as (0x4000,0) and (0x2666,0x3333) after
	rounding and F_dot_P is assigned 0x26660000. The lower bits were
	already lost while rounding the unit vector components.

	Besides code simplification, this change can lead to better
	performance when FT_MulDiv with the scaled-down F_dot_P is less
	likely to use the costly 64-bit path. We are not changing the type
	of F_dot_P to FT_F2Dot14 at this point.

	* src/truetype/ttinterp.c (Compute_Funcs): Scale F_dot_P down by 14
	bits and modify its use accordingly.
	(Direct_Move, Direct_Move_Orig, Compute_Point_Displacement): Modify
	the use of F_dot_P field.
	* src/truetype/ttobjs.c (tt_size_run_fpgm): Change arbitrary
	assignment of F_dot_P to its theoretical maximum in case we decide
	to scale back its type later.

2012-12-09  Johnson Y. Yan  <<EMAIL>>

	[type1] Another fix for 2012-09-17 commit.

	* src/type1/t1parse.c (T1_Get_Private_Dict) <found>: Correctly set
	`limit' value.

2012-12-06  Alexei Podtelezhnikov  <<EMAIL>>

	[truetype] Tweak the previous commit.

	* src/truetype/ttinterp.c (Current_Ratio): Put unit vector
	components as the second TT_MulFix14 arguments. This is required
	on 16-bit systems.

2012-12-06  Alexei Podtelezhnikov  <<EMAIL>>

	[truetype] Microoptimizations in bytecode interpreter.

	* src/truetype/ttinterp.c (TT_DivFix14): New macro.
	(Normalize): Use it here.
	(Current_Ratio): Use TT_MulFix14 instead of FT_MulDiv.
	(Ins_SHPIX): Cancel out two TT_MulFix14 calls.

2012-12-05  Alexei Podtelezhnikov  <<EMAIL>>

	[truetype] Cosmetic improvement in bytecode interpreter.

	* src/truetype/ttinterp.c: Use explicit calls to FT_MulDiv,
	FT_MulFix, and FT_DivFix instead of macros.

2012-12-03  John Tytgat  <<EMAIL>>

	[pshinter] Clamp BlueScale value.

	This is Savannah bug #37856.

	* src/pshinter/pshglob.c (psh_calc_max_height): New function.
	(psh_globals_new): Use it to limit BlueScale value to
	`1 / max_of_blue_zone_heights'.

2012-12-01  Alexei Podtelezhnikov  <<EMAIL>>

	[truetype, type1] Revise the use of FT_MulDiv.

	* src/truetype/ttgxvar.c: Updated.
	* src/truetype/ttobjs.c: Updated.
	* src/type1/t1load.c: Updated.

2012-11-30  Werner Lemberg  <<EMAIL>>

	[configure] Preserve customized `ftoption.h'.

	Problem reported by Del Merritt <<EMAIL>>.

	* builds/unix/configure.raw <cpp computation of bit length>: Don't
	remove existing FreeType configuration files.

2012-11-29  John Tytgat  <<EMAIL>>

	[type1] Fix Savannah bug #37831.

	The bug report also contains a patch.

	* src/type1/t1parse.c (T1_Get_Private_Dict) <found>: Really fix
	change from 2012-09-17.

2012-11-28  Alexei Podtelezhnikov  <<EMAIL>>

	[truetype] Fix formatting and typo.

2012-11-27  Alexei Podtelezhnikov  <<EMAIL>>

	[cid, type1, type42] Clean up units_per_EM calculations.

	* src/cid/cidload.c (cid_parse_font_matrix): Updated.
	* src/type1/t1load.c (t1_parse_font_matrix): Updated.
	* src/type42/t42parse.c (t42_parse_font_matrix): Updated.

2012-11-27  Alexei Podtelezhnikov  <<EMAIL>>

	[ftstroke] Minor improvement.

	* src/base/ftstroke.c: Replace nested FT_DivFix and FT_MulFix with
	FT_MulDiv.

2012-11-17  Werner Lemberg  <<EMAIL>>

	* src/base/fttrigon.c (ft_trig_downscale): Make 64bit version work.

2012-11-15  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Fix integer overflows in dd5718c7d67a.

	* src/base/ftoutln.c (FT_Outline_EmboldenXY): Use FT_MulDiv.

2012-11-15  Werner Lemberg  <<EMAIL>>

	[autofit] Trace stem widths.

	* src/autofit/aflatin.c (af_latin_metrics_init_widths): Add some
	FT_TRACE calls.

2012-11-13  Werner Lemberg  <<EMAIL>>

	[cff] Add support for OpenType Collections (OTC).

	* src/cff/cffload.c (cff_font_load): Separate subfont and face
	index handling to load both pure CFFs with multiple subfonts and
	OTCs (with multiple faces where each face holds exactly one
	subfont).
	* src/cff/cffobjs.c (cff_face_init): Updated.

2012-11-12  Werner Lemberg  <<EMAIL>>

	[autofit] Minor improvement.

	* src/autofit/aflatin.c (af_latin_hints_compute_blue_edges): Fix
	loop.

2012-11-10  Werner Lemberg  <<EMAIL>>

	[autofit] Improve tracing.

	* src/autofit/aflatin.c (af_latin_hint_edges)
	[FT_DEBUG_LEVEL_TRACE]: Count number of actions and emit something
	if there weren't any.

2012-11-04  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Fortify emboldening code against egregious distortions.

	* src/base/ftoutln.c (FT_Outline_EmboldenXY): Threshold emboldening
	strength when it leads to segment collapse.

2012-11-03  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Clean up emboldening code and improve comments there.

	* src/base/ftoutln.c (FT_Outline_EmboldenXY): Replace sequential
	calls to FT_MulFix and FT_DivFix with FT_MulDiv.
	Mention that bisectors are used to figure out the shift direction.

2012-10-24  Werner Lemberg  <<EMAIL>>

	[autofit] Add standard character to `AF_ScriptClassRec' structure.

	* src/autofit/aftypes.h (AF_ScriptClassRec): Add `standard_char'
	member.
	(AF_DEFINE_SCRIPT_CLASS): Updated.

	* src/autofit/aflatin.c (af_latin_metrics_init_widths): Use it.
	(af_latin_metrics_init, af_latin_script_class): Updated.

	* src/autofit/aflatin.c (af_latin2_metrics_init_widths): Use it.
	(af_latin2_metrics_init, af_latin2_script_class): Updated.

	* src/autofit/afcjk.c (af_cjk_metrics_init_widths): Use it.
	(af_cjk_metrics_init, af_cjk_script_class): Updated.

	* src/autofit/afindic.c (af_indic_metrics_init,
	af_indic_script_class): Updated.

	* src/autofit/afcjk.h, src/autofit/aflatin.h: Updated.

	* src/autofit/afdummy.c: Updated.

2012-10-24  Werner Lemberg  <<EMAIL>>

	[autofit] Only use Unicode CMap.

	* src/autofit/aflatin.c (af_latin_metrics_init): Implement it, to be
	in sync with `af_face_globals_compute_script_coverage'.

2012-10-21  Werner Lemberg  <<EMAIL>>

	[psaux] Improve parsing of invalid numbers.

	* src/psaux/psconv.c (PS_Conv_Strtol): Always parse complete number,
	even in case of overflow.
	(PS_Conv_ToInt): Only increase cursor if parsing was successful.
	(PS_Conv_ToFixed): Ditto.
	Trace underflow and data error.

2012-10-21  Werner Lemberg  <<EMAIL>>

	[smooth] Improve tracing.

	* src/smooth/ftgrays.c (gray_sweep): Trace last sweep line of
	current band also.

2012-10-20  Alexei Podtelezhnikov  <<EMAIL>>

	[truetype] Cheaper way to threshold angles between vectors.

	* src/truetype/ttinterp.c (Ins_ISECT): Thresholding tangent is a lot
	cheaper than thresholding sine.

2012-10-20  Werner Lemberg  <<EMAIL>>

	[cff] Improve parsing of invalid real numbers.

	* src/cff/cffparse.c (cff_parse_real): Always parse complete number,
	even in case of overflow or underflow.
	Also trace one more underflow.

2012-10-20  Andreas Pehnack  <<EMAIL>>

	[sfnt] Load pure CFF fonts wrapped in SFNT container.

	Such fonts only have a `cmap' and a `CFF' table.

	* src/sfnt/ttload.c (tt_face_load_font_dir): Don't call
	`check_table_dir' if font signature is `OTTO'.

2012-10-20  Werner Lemberg  <<EMAIL>>

	[psaux] Fix some value overflows and improve tracing.

	* src/psaux/psconv.c: Include FT_INTERNAL_DEBUG_H.
	(FT_COMPONENT): Define.
	(PS_Conv_Strtol): Return FT_Long.
	Handle bad data and overflow.
	Emit some tracing messages in case of error.
	(PS_Conv_ToInt): Return FT_Long.
	(PS_Conv_ToFixed): Updated.
	* src/psaux/psconv.h: Updated.

	* include/freetype/internal/fttrace.h: Add `psconv'.

2012-10-20  Werner Lemberg  <<EMAIL>>

	[autofit] Fix `make multi CC=c++'.

	* src/autofit/aflatin.c, src/autofit/aflatin2.c: Include
	`afglobal.h'.
	* src/autofit/afloader.c: Fix order of header files.
	* src/autofit/afmodule.c: Include `afglobal.h' and `aferrors.h'.

2012-10-19  Werner Lemberg  <<EMAIL>>

	[cff] Fix more value errors and improve tracing.

	* src/cff/cffparse.c (cff_parse_integer): Emit tracing message in
	case of error.
	(cff_parse_real): Handle and trace overflow, underflow, and bad data
	consistently.
	(do_fixed): New helper function, handling and tracing overflow.
	(cff_parse_fixed, cff_parse_fixed_scaled): Use `do_fixed'.

2012-10-17  Werner Lemberg  <<EMAIL>>

	[psaux] Fix some value overflows.

	* src/psaux/psconv.c (PS_Conv_ToFixed): Implement it.

2012-10-17  Bram Tassyns  <<EMAIL>>

	[cff] Fix value overflow.

	* src/cff/cffparse.c (cff_parse_fixed_scaled): Implement it.

2012-10-17  Werner Lemberg  <<EMAIL>>

	[truetype] Fix Savannah bug #37572.

	* src/truetype/ttinterp.c (Ins_ISECT): Use angle between vectors to
	avoid grazing intersections.  The previous threshold was too coarse,
	incorrectly rejecting short but valid vectors.

2012-09-30  Gilles Espinasse  <<EMAIL>>

	Remove useless `rm' detection.

	`rm -f' is directly used in the `configure' script created by
	autoconf, thus no availability test is necessary.

	* builds/unix/configure.raw (RMF): Remove test.
	* builds/unix/unix-def.in (DELETE): Updated.

2012-09-29  Werner Lemberg  <<EMAIL>>

	[autofit] Minor optimization.

	* src/autofit/afglobal.c (af_face_globals_compute_script_coverage):
	Add loop condition.

2012-09-29  Werner Lemberg  <<EMAIL>>

	[autofit] Fix thinko.

	* src/autofit/aftypes.h (AF_SCRIPT):
	s/AF_SCRIPT_NONE/AF_SCRIPT_DUMMY/.  We already use `AF_SCRIPT_NONE'
	as a bit mask.

	* src/autofit/afdummy.c: Updated.

2012-09-18  Werner Lemberg  <<EMAIL>>

	[autofit] Implement `increase-x-height' property.

	* include/freetype/ftautoh.h (FT_Prop_IncreaseXHeight): New
	structure.

	* include/autofit/afmodule.c (af_property_get_face_globals): New
	function, re-using code from `af_property_get'.
	(af_property_set, af_property_get): Handle `increase-x-height'.
	Updated.

2012-09-18  Werner Lemberg  <<EMAIL>>

	[autofit] Implement Infinality's `increase glyph heights'.

	This is an improved version of a similar fix contained in the
	so-called `Infinality patch', taken from

	  http://www.infinality.net/fedora/linux/zips/freetype-infinality-2.4.10-20120616_01-x86_64.tar.bz2

	which addresses various enhancements of the auto-hinter.  Without
	properties to control a module's metadata it wasn't possible to
	adapt the patches because everything was originally controlled by
	environment variables which I consider not suitable in general.

	A patch to control `increase_x_height' follows.

	* src/autofit/afglobal.h (AF_PROP_INCREASE_X_HEIGHT_MIN,
	AF_PROP_INCREASE_X_HEIGHT_MAX): New macros.
	(AF_FaceGlobalsRec): Add `increase_x_height' member.
	* src/autofit/afglobal.c (af_face_globals_new): Initialize it.

	* src/autofit/aflatin.c (af_latin_metrics_scale_dim),
	* src/autofit/aflatin2.c (af_latin2_metrics_scale_dim): Implement
	handling of `increase_x_height'.

2012-09-18  Werner Lemberg  <<EMAIL>>

	[autofit] Add hierarchical property access to some structures.

	* src/autofit/afglobal.h: Include `afmodule.h'.
	(AF_FaceGlobalsRec): Add `module' member.
	(AF_FaceGlobals): Typedef moved to...
	* src/autofit/aftypes.h: Here.
	(AF_ScriptMetricsRec): Add `globals' member.

	* src/autofit/afglobal.c (af_face_globals_new,
	af_face_globals_compute_script_coverage,
	af_face_globals_get_metrics): Updated.

	* src/autofit/afloader.c (af_loader_reset), src/autofit/afmodule.c
	(af_property_get): Updated.

2012-09-17  Werner Lemberg  <<EMAIL>>

	[type1] Fix Savannah bug #37350.

	* src/type1/t1parse.c (T1_Get_Private_Dict) <found>: Check for ASCII
	storage only if we actually have at least four bytes.

2012-09-15  Werner Lemberg  <<EMAIL>>

	[autofit] Implement `fallback-script' property.

	* src/autofit/afglobal.c: s/default_script/fallback_script/.
	* src/autofit/afglobal.h: s/AF_SCRIPT_DEFAULT/AF_SCRIPT_FALLBACK/.

	* src/autofit/afmodule.c: s/default_script/fallback_script/.
	(af_property_set, af_property_get): Implement `fallback-script'.
	* src/autofit/afmodule.h: s/default_script/fallback_script/.

	* include/freetype/ftautoh.h: Document it.

2012-09-15  Werner Lemberg  <<EMAIL>>

	[autofit] Correct previous Unicode 6.1.0 change.

	The auto-hinter's latin module only handles latin ligatures in the
	`Alphabetical Presentation Forms' block.

	* src/autofit/aflatin.c (af_latin_uniranges): Fix it.

2012-09-15  Werner Lemberg  <<EMAIL>>

	* src/autofit/afmodule.c: s/FT_Err_/AF_Err_/.

2012-09-15  Werner Lemberg  <<EMAIL>>

	[autofit] Make default script a global property.

	* src/autofit/afmodule.h (AF_ModuleRec): Add `default_script' field.

	* src/autofit/afglobal.c (af_face_globals_compute_script_coverage,
	af_face_globals_new), src/autofit/afloader.c (af_loader_reset),
	src/autofit/afmodule.c (af_property_get) <glyph-to-script-map>,
	af_autofitter_init:
	Handle default script.

	* src/autofit/afglobal.h: Updated.

2012-09-15  Werner Lemberg  <<EMAIL>>

	Use `FT_Module' instead of `FT_Library' argument in property funcs.

	This internal change simplifies access to global module data.

	* include/freetype/internal/services/svprop.h
	(FT_Properties_SetFunc, FT_Properties_GetFunc): Change accordingly.

	* src/base/ftobjs.c (ft_property_do), src/autofit/afmodule.c
	(af_property_set, af_property_get): Updated.

2012-09-14  Werner Lemberg  <<EMAIL>>

	[autofit] Update to Unicode 6.1.0.

	* src/autofit/afcjk.c (af_cjk_uniranges), src/autofit/aflatin.c
	(af_latin_uniranges): Add and fix ranges.

2012-09-14  Werner Lemberg  <<EMAIL>>

	[autofit] Pass `AF_Module' instead of `AF_Loader'.

	We want to access the (not yet existing) module's global data later
	on.

	* src/autofit/afloader.c: Include `afmodule.h'.
	(af_loader_init, af_loader_reset, af_loader_done,
	af_loader_load_glyph): Change accordingly.
	* src/autofit/afmodule.c (AF_ModuleRec): Move to `afmodule.h'.
	Updated.

	* src/autofit/afmodule.h: Include `afloader.h'.
	(AF_ModuleRec): Define here.
	* src/autofit/afloader.h (AF_Module): Define here.
	Updated.

2012-09-14  Werner Lemberg  <<EMAIL>>

	[autofit] Fix `make multi'.

	* include/freetype/internal/fttrace.h: Add `afmodule'.
	* src/autofit/afmodule.c: Include FT_INTERNAL_DEBUG_H.
	(FT_COMPONENT): Define.

2012-09-14  Werner Lemberg  <<EMAIL>>

	* src/autofit/afmodule.c: s/FT_Autofitter/AF_Module/.

2012-09-12  Werner Lemberg  <<EMAIL>>

	[autofit] Minor reorganization.

	* src/autofit/afglobal.c (AF_SCRIPT_LIST_DEFAULT,
	AF_SCRIPT_LIST_NONE, AF_DIGIT): Move to...
	* src/autofit/afglobal.h (AF_SCRIPT_DEFAULT, AF_SCRIPT_LIST_NONE,
	AF_DIGIT): This and update code.

2012-09-01  Werner Lemberg  <<EMAIL>>

	[autofit] Implement `glyph-to-script-map' property.

	* include/freetype/ftautoh.h: New public header file.
	* include/freetype/config/ftheader.h (FT_AUTOHINTER_H): New macro.

	* src/autofit/afglobal.c (AF_FaceGlobalsRec): Move structure to...
	* src/autofit/afglobal.h: This header file.
	* src/autofit/afmodule.c: Include FT_AUTOHINTER_H.
	(af_property_get): Handle `glyph-to-script-map'.

2012-08-31  Werner Lemberg  <<EMAIL>>

	[autofit] Implement properties service framework.

	No properties are added yet.

	* src/autofit/afmodule.c: Include FT_SERVICE_PROPERTIES_H.
	(af_property_set, af_property_get): New dummy functions.
	(af_service_properties, af_services, af_get_interface): Provide
	service setup.
	(autofit_moduleclass): Add service interface.

	* src/autofit/afpic.c: Add necessary forward declarations.
	(autofit_module_class_pic_init): Add code for service addition.
	(autofit_module_pic_free): Add code for service removal.
	* src/autofit/afpic.h (AF_SERVICES_GET, AF_SERVICE_PROPERTIES_GET):
	New macros which provide necessary syntactical sugar for PIC
	support.

2012-08-30  Werner Lemberg  <<EMAIL>>

	Implement properties to control FreeType modules.

	* include/freetype/fterrdef.h (FT_Err_Missing_Property): New error
	code.
	* include/freetype/ftmodapi.h (FT_Property_Set, FT_Property_Get):
	New API.

	* include/freetype/internal/services/svprop.h: New file.
	* include/freetype/internal/ftserv.h (FT_SERVICE_PROPERTIES_H): New
	macro.

	* src/base/ftobjs.c: Include FT_SERVICE_PROPERTIES_H.
	(ft_property_do, FT_Property_Set, FT_Property_Get): New functions.

2012-08-29  Werner Lemberg  <<EMAIL>>

	[docmaker] Allow `-' in tags and identifiers.

	* src/tools/docmaker/content.py (re_identifier),
	src/tools/docmaker/sources.py (re_markup_tag1, re_markup_tag2,
	re_crossref): Add `-' in patterns.

2012-08-27  Werner Lemberg  <<EMAIL>>

	[FT_CONFIG_OPTION_PIC] Fix g++ 4.6.2 compiler warnings.

	* include/freetype/internal/ftdriver.h (FT_DEFINE_DRIVER),
	include/freetype/internal/ftobjs.h (FT_DEFINE_RENDERER,
	FT_DEFINE_MODULE), include/freetype/internal/ftserv.h
	(FT_DEFINE_SERVICEDESCREC1, FT_DEFINE_SERVICEDESCREC2,
	FT_DEFINE_SERVICEDESCREC3, FT_DEFINE_SERVICEDESCREC4,
	FT_DEFINE_SERVICEDESCREC5, FT_DEFINE_SERVICEDESCREC6),
	src/autofit/afpic.c (autofit_module_class_pic_init),
	src/base/basepic.c (ft_base_pic_init), src/base/ftinit.c
	(ft_create_default_module_classes), src/cff/cffparse.c
	(FT_Create_Class_cff_field_handlers), src/cff/cffpic.c
	(cff_driver_class_pic_init), src/pshinter/pshpic.c
	(pshinter_module_class_pic_init), src/psnames/pspic.c
	(psnames_module_class_pic_init), src/raster/rastpic.c
	(ft_raster1_renderer_class_pic_init), src/sfnt/sfntpic.c
	(sfnt_module_class_pic_init), src/sfnt/ttcmap.c
	(FT_Create_Class_tt_cmap_classes), src/smooth/ftspic.c
	(ft_smooth_renderer_class_pic_init), src/truetype/ttpic.c
	(tt_driver_class_pic_init): Initialize allocation variable.

2012-08-27  Werner Lemberg  <<EMAIL>>

	[truetype] Fix compilation warning.

	* src/truetype/ttgload.c (IS_HINTED): Move macro to...
	* src/truetype/ttobjs.h: This header file.

2012-08-27  Werner Lemberg  <<EMAIL>>

	[autofit, cff, pshinter, psnames] More renamings for orthogonality.

	* src/autofit/afmodule.c, src/autofit/afpic.h:
	s/AF_AUTOFITTER_/AF_/.

	* src/cff/cffdrivr.c, src/cff/cffobjs.c, src/cff/cffparse.c,
	src/cff/cffpic.h: s/FT_CFF_/CFF_/.

	* src/pshinter/pshmod.c, src/pshinter/pshpic.h:
	s/FT_PSHINTER_/PSHINTER_/.

	* src/psnames/psmodule.c, src/psnames/pspic.h:
	s/FT_PSCMAPS/PSCMAPS_/.

2012-08-27  Werner Lemberg  <<EMAIL>>

	[sfnt, truetype] More renamings for orthogonality.

	* src/sfnt/sfdriver.c, src/sfnt/sfntpic.h, src/sfnt/ttcmap.c,
	src/truetype/ttdriver.c, src/truetype/ttpic.h: s/FT_SFNT_/SFNT_/,
	s/FT_TT_/TT_/, s/GET_CMAP_INFO_GET/CMAP_INFO_GET/.

2012-08-27  Werner Lemberg  <<EMAIL>>

	[autofit] Some macro and variable renamings for orthogonality.

	* include/freetype/internal/autohint.h, src/base/ftobjs.c,
	src/autofit/afmodule.c, src/autofit/afpic.c, src/autofit/afpic.h:
	s/SERVICE/INTERFACE/, s/service/interface/, s/Service/Interface/.

2012-08-26  Werner Lemberg  <<EMAIL>>

	Fix Savannah bug #37178.

	* src/base/ftobjs.c (FT_Open_Face): Initialize `error' with
	`FT_Err_Missing_Module' before loop to indicate `no valid drivers'.

2012-08-17  Werner Lemberg  <<EMAIL>>

	* src/base/ftsynth.c (FT_GlyphSlot_Oblique): Fix shear angle.

	The old value was far too large (more than 20°).  The new one
	corresponds to 12°, quite common in typography.

2012-08-12  Alexei Podtelezhnikov  <<EMAIL>>

	[smooth] Fix Savannah bug #37017.

	* src/smooth/ftgrays.c (gray_render_cubic): Use a different set of
	checks when detecting super curvy splines to be split.

2012-08-05  Werner Lemberg  <<EMAIL>>

	[autofit] Improve recognition of flat segments.

	Problem reported by Brad Dunzer <<EMAIL>>.

	* src/autofit/aflatin.c (af_latin_metrics_init_blues): We have
	a flat segment if the horizontal distance of best on-points is
	larger than a given threshold.

2012-08-05  Werner Lemberg  <<EMAIL>>

	[autofit] Variable renamings.

	* src/autofit/aflatin.c (af_latin_metrics_init_blues): Replace
	`glyph' with `outline'.
	s/best_first/best_contour_first/.
	s/best_last/best_contour_last/.

2012-07-31  Werner Lemberg  <<EMAIL>>

	[type1] Fix Savannah bug #37000.

	* src/type1/t1load.c (parse_encoding): Fix order of checks.

2012-07-17  Werner Lemberg  <<EMAIL>>

	[psaux] Fix Savannah bug #36833.

	* src/psaux/t1decode.c (t1operator_seac): `seac' is not a valid
	operator if we want metrics only.

2012-07-16  Werner Lemberg  <<EMAIL>>

	[type1] Fix Savannah bug #36832.

	* src/type1/t1load.c (parse_charstrings): Reject negative number of
	glyphs.

2012-07-13  Werner Lemberg  <<EMAIL>>

	[type1] Fix Savannah bug #36829.

	* src/type1/t1load.c (parse_encoding): Check cursor position after
	call to T1_Skip_PS_Token.

2012-07-12  Alexei Podtelezhnikov  <<EMAIL>>

	Revert the last commit 45337b07.

	* src/base/ftstroke.c (FT_Stroker_New): Revert the previous change.

2012-07-11  Alexei Podtelezhnikov  <<EMAIL>>

	[ftstroke] Fix uninitialized return value.

	* src/base/ftstroke.c (FT_Stroker_New): Return FT_Err_Ok instead.

2012-07-11  Werner Lemberg  <<EMAIL>>

	[smooth] Avoid memory leak in case of failure.

	* src/smooth/ftsmooth.c (ft_smooth_render_generic): Use flags to
	indicate what to clean up after finishing the function, with and
	without errors.

2012-07-09  Werner Lemberg  <<EMAIL>>

	Fix compilation with MSVC 5.0.

	Problem reported by Peter Breitenlohner and Akira Kakuto.

	* include/freetype/config/ftstdlib.h (ft_setjmp): Updated.
	* src/sfnt/ttcmap.c (tt_face_build_cmaps): Remove cast.

2012-07-09  Werner Lemberg  <<EMAIL>>

	[autofit] Improve debugging messages; do some code cleanup.

	* src/autofit/aflatin.c (af_latin_align_linked_edge,
	af_latin_hint_edges): Synchronize with formatting used in the
	ttfautohint project.

2012-07-07  Gilles Espinasse  <<EMAIL>>

	Fix strict-aliasing warning.

	* src/base/ftglyph.c (FT_Glyph_To_Bitmap): Avoid double cast.

2012-07-07  Dave Thomas  <<EMAIL>>

	[ARM] Fix FT_MulFix_arm.

	* include/freetype/config/ftconfig.h (FT_MulFix_arm) [__arm__]:
	Avoid ADDS instruction to clobber condition codes.

2012-07-06  Werner Lemberg  <<EMAIL>>

	[autofit] Do some code cleanup.

	* src/autofit/afglobal.c (af_face_globals_new): Simplify.

	* src/autofit/afhints.c: Use `FT_TRACE7' instead of `printf'
	everywhere.
	(FT_COMPONENT): New macro.
	(af_glyph_hints_done): Simplify.

	* include/freetype/internal/fttrace.h: Updated.

2012-07-05  Werner Lemberg  <<EMAIL>>

	[autofit] Improve output of debugging information.

	* src/autofit/afhints.c (af_glyph_hints_dump_segments): Print more
	data; report no data.
	(af_glyph_hints_dump_edges): Report no data.

2012-07-04  Werner Lemberg  <<EMAIL>>

	[autofit] Fix Savannah bug #36091.

	* src/autofit/aflatin.c (af_latin_metrics_init_blues),
	src/autofit/aflatin2.c (af_latin2_metrics_init_blues): Change the
	constraint for testing round vs. flat segment: Accept either a
	small distance or a small angle.

2012-07-04  Werner Lemberg  <<EMAIL>>

	[autofit] Beautify blue zone tracing.

	* src/autofit/aflatin.c (af_latin_metrics_init_blues),
	src/autofit/aflatin2.c (af_latin2_metrics_init_blues): Implement it.

2012-07-03  Werner Lemberg  <<EMAIL>>

	[autofit] Quantize stem widths.

	* src/autofit/afangles.c (af_sort_widths): Rename to...
	(af_sort_and_quantize_widths): This.
	Add code to avoid stem widths which are almost identical.
	* src/autofit/aftypes.h, src/autofit/aflatin.c, src/autofit/afcjk.c:
	Updated.

2012-07-03  Werner Lemberg  <<EMAIL>>

	[autofit] Minor speed-up.

	* src/autofit/afangles (af_sort_pos, af_sort_widths): Don't swap
	elements if they are equal.

2012-06-30  Gilles Espinasse  <<EMAIL>>

	Fix `checking if gcc static flag -static works' test.

	On my linux build tree, I receive yes answer in every package I
	build except FreeType for this test checking if gcc static flag
	`-static' works

	In FreeType, no is received, unless bzip2 and zlib are disabled using

	  ./configure --without-bzip2 --without-zlib

	The reason is that bzip2 and zlib tests add `-lz' and `-lbz2' to
	LDFLAGS and this broke static flag test.

	* builds/unix/configure.raw: Update CFLAGS and LDFLAGS only after
	LT_INIT has run.

2012-06-28  Infinality  <<EMAIL>>

	[truetype] Fix various artifacts.

	Verdana was broken in the original Infinality commit.  Also
	includes other minor fixes.

	* src/truetype/ttsubpix.h: Updated.  Removed unused macros.
	(RASTERIZER_35_Rules): Add Verdana.
	(SKIP_NONPIXEL_Y_MOVES_Rules): Add Tahoma `s'.
	(MIRP_CVT_ZERO_Rules): Remove Verdana.
	(ALWAYS_SKIP_DELTAP_Rules): Add Russian char 0x438.
	(COMPATIBLE_WIDTHS_Rules): Rearrange some rules.
	(X_SCALING_Rules): Adjust Verdana `a' at 12 and 13 ppem.

	* src/truetype/ttsubpix.c: Updated.
	(sph_set_tweaks): Re-execute fpgm always.

2012-06-28  Gilles Espinasse  <<EMAIL>>

	Fix CFLAGS and LDFLAGS share configure test.

	* builds/unix/configure.raw: Fix typo.

2012-06-28  Werner Lemberg  <<EMAIL>>

	[truetype] Set the `subpixel_positioned' flag unconditionally.

	This is how the code currently behaves.

	* src/truetype/ttgload.c (tt_loader_init): Do it.

2012-06-27  Werner Lemberg  <<EMAIL>>

	Fix conditional compilation.

	* src/base/basepic.c: Use FT_CONFIG_OPTION_MAC_FONTS.

2012-06-27  Werner Lemberg  <<EMAIL>>

	Fix conditional compilation.

	* include/freetype/internal/ftcalc.h (FT_MulDiv_No_Round): Don't
	enclose with `TT_USE_BYTECODE_INTERPRETER'; we now need the function
	elsewhere also.

	* src/autofit/afcjk.h: Use AF_CONFIG_OPTION_CJK.

	* src/truetype/ttgload.c (tt_loader_init): Fix compiler warning.

	* src/truetype/ttinterp.c (Ins_MSIRP): Fix compiler warning.

	* src/truetype/ttinterp.h: Use
	TT_CONFIG_OPTION_BYTECODE_INTERPRETER.

2012-06-26  Infinality  <<EMAIL>>

	[truetype] Remove unused rounding functionality.

	The subpixel hinting patch contained the concept of an adjustable
	number of gridlines per pixel.  This is no longer used due to x
	being completely ignored instead.  This will return some of the
	code to its existing state prior to the original Infinality
	commit.

	* include/freetype/internal/ftobjs.h (FT_PIX_FLOOR_GRID,
	FT_PIX_ROUND_GRID, FT_PIX_CEIL_GRID): Removed.

	* src/truetype/ttinterp.c: Updated.
	(Round_None, Round_To_Grid, Round_To_Half_Grid, Round_Down_To_Grid,
	Round_Up_To_Grid, Round_To_Double_Grid, Round_Super, Round_Super_45,
	SetSuperRound): Remove parameter to handle the number of grid lines per
	pixel.
	(SET_SuperRound, ROUND_None, CUR_Func_round): Updated.
	(DO_SROUND, DOS45ROUND, DO_ODD, DO_EVEN): Updated.
	(DO_ROUND, DO_NROUND): Updated.
	(Move_Zp2_Point, Ins_SHPIX, Ins_MSIRP, Ins_MDAP, Ins_MIAP,
	Ins_MDRP, Ins_MIRP): Perform Round_None instead of calling a modified
	rounding function.  Remove gridlines_per_pixel.  Create a local
	variable to store control value cutin. Simplify the conditional for
	ignore_x_mode.  Adjust rounding calls to pass only two values.

2012-06-25  Werner Lemberg  <<EMAIL>>

	[cff] Fix Savannah bug #36705.

	Handle numbers like 2.001 correctly.

	* src/cff/cffparse.c (cff_parse_real): Avoid negative values for
	`shift'.

2012-06-18  Infinality  <<EMAIL>>

	[truetype] Support subpixel hinting.

	This is the large, famous `Infinality' patch to support ClearType
	bytecode which has been available from
	http://www.infinality.net/blog/ for some time, and which has been
	refined over the last years.  While still experimental, it is now
	mature enough to be included directly into FreeType.

	Most of the code is based on the ClearType whitepaper written by
	Greg Hitchcock

	  https://www.microsoft.com/typography/cleartype/truetypecleartype.aspx

	which gives a detailed overview of the necessary changes to the
	Microsoft rasterizer so that older fonts are supported.  However, a
	lot of details are still missing, and this patches provides a
	framework to easily handle rendering issues down to the glyph level
	of certain fonts.

	Note that ClearType support is not completely implemented!  In
	particular, full support for the options `compatible_widths',
	`symmetrical_smoothing, and `bgr' (via the GETINFO bytecode
	instruction) is missing.

	* src/truetype/ttsubpix.c: New file, providing code to handle
	`tweaks', this is, rules for certain glyphs in certain fonts
	(including wildcards) which need a special treatment.

	* src/truetype/ttsubpix.h: New file, holding the tweaking rules.

	* include/freetype/config/ftoption.h, src/devel/ftoption.h
	(TT_CONFIG_OPTION_SUBPIXEL_HINTING): New macro.

	* include/freetype/internal/ftobjs.h (FT_PIX_FLOOR_GRID,
	FT_PIX_ROUND_GRID, FT_PIX_CEIL_GRID): New macros.

	* src/truetype/truetype.c [TT_USE_BYTECODE_INTERPRETER]: Include
	`ttsubpix.c'.

	* src/truetype/ttgload.c: Include `ttsubpix.h'.
	[All changes below are guarded by TT_CONFIG_OPTION_SUBPIXEL_HINTING.]

	(tt_get_metrics): Set tweak flags.
	(TT_Hint_Glyph): Call `FT_Outline_EmboldenXY' if necessary.
	(TT_Process_Simple_Glyph): Compensate emboldening if necessary.
	(compute_glyph_metrics): Handle `compatible widths' option.
	(tt_loader_init): Handle ClearType GETINFO information bits.

	* src/truetype/rules.mk (TT_DRV_SRC): Updated.

	* src/truetype/ttinterp.c: Include `ttsubpix.h'.
	[Where necessary, changes below are guarded by
	TT_CONFIG_OPTION_SUBPIXEL_HINTING.]

	(Direct_Move, Direct_Move_X): Extended.
	(Round_None, Round_To_Grid, Round_To_Half_Grid, Round_Down_To_Grid,
	Round_Up_To_Grid, Round_To_Double_Grid, Round_Super, Round_Super_45,
	SetSuperRound): Add parameter to handle the number of grid lines per
	pixel.
	(SET_SuperRound, ROUND_None, CUR_Func_round): Updated.
	(DO_SROUND, DOS45ROUND, DO_ODD, DO_EVEN): Updated.
	(DO_ROUND, DO_NROUND): Updated.
	(DO_RS): Take care of `Typeman' bytecode patterns.
	(Ins_FDEF): Add some debugging code.  Commented out.
	(Ins_ENDF): Restore state.
	(Ins_CALL, Ins_LOOPCALL): Handle inline delta functions.
	(Ins_MD): Handle `Vacuform' rounds.
	(Move_Zp2_Point, Ins_SHPIX, Ins_MSIRP, Ins_MDAP, Ins_MIAP,
	Ins_MDRP, Ins_MIRP): Handle tweaks.
	(Ins_ALIGNRP): Add tweak guard.
	(Ins_IUP, Ins_DELTAP): Handle tweaks.
	(Ins_GETINFO): Handle new ClearType bits.
	(TT_RunIns): Handle tweaks.

	* src/truetype/ttinterp.h: Updated.
	(SPH_TweakRule, SPH_ScaleRule): New structures for tweaks.
	(TT_ExecContextRec): Add members for subpixel hinting support.

	* src/truetype/ttobjs.h (TT_DefRecord): Add `inline_delta' member.

2012-06-15  Werner Lemberg  <<EMAIL>>

	* Version 2.4.10 released.
	=========================


	Tag sources with `VER-2-4-10'.

	* docs/VERSION.DLL: Update documentation and bump version number to
	2.4.10.

	* README, Jamfile (RefDoc),
	builds/win32/vc2005/freetype.vcproj, builds/win32/vc2005/index.html,
	builds/win32/vc2008/freetype.vcproj, builds/win32/vc2008/index.html,
	builds/win32/vc2010/freetype.vcxproj, builds/win32/vc2010/index.html,
	builds/win32/visualc/freetype.dsp,
	builds/win32/visualc/freetype.vcproj,
	builds/win32/visualc/index.html, builds/win32/visualce/freetype.dsp,
	builds/win32/visualce/freetype.vcproj,
	builds/win32/visualce/index.html,
	builds/wince/vc2005-ce/freetype.vcproj,
	builds/wince/vc2005-ce/index.html,
	builds/wince/vc2008-ce/freetype.vcproj,
	builds/wince/vc2008-ce/index.html: s/2.4.9/2.4.10/, s/249/2410/.

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 10.

	* builds/unix/configure.raw (version_info): Set to 15:0:9.

2012-06-15  Alexei Podtelezhnikov  <<EMAIL>>

	* src/base/ftsynth.c (FT_GlyphSlot_Embolden): Improve spacing.

	* docs/CHANGES: Updated.

2012-06-14  suzuki toshiya  <<EMAIL>>

	* builds/exports.mk: Add CCexe_CFLAGS and CCexe_LDFLAGS.

	to pass special compiler/linker flags under cross development.
	Suggested by Savannah bug #36367.

	ChangeLog on 2010-07-15 saying as they were removed was wrong
	for the official trunk of FreeType2.  This commit is the first
	introduction of them.

2012-06-14  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.

2012-06-14  suzuki toshiya  <<EMAIL>>

	[truetype] Add new versions of NEC FA family to tricky font list.

	NEC FA family dated in 1996 have different checksum.
	Reported by Johnson Y. Yan <<EMAIL>>; see

	  https://lists.gnu.org/archive/html/freetype-devel/2012-06/msg00023.html

	* src/truetype/ttobjs.c (tt_check_trickyness_sfnt_ids): 4 sets
	of fpgm & prep table checksums for FA-Gothic, FA-Minchou,
	FA-RoundedGothicM, FA-RoundedGothicB are added.  The family
	names in sample PDF are truncated, thus the list of the
	family names in tt_check_trickyness_family() is not updated yet.

2012-06-06  Werner Lemberg  <<EMAIL>>

	[ftraster] Fix rounding issue causing visual artifacts.

	Problem reported by jola <<EMAIL>>; see

	  https://lists.gnu.org/archive/html/freetype-devel/2012-05/msg00036.html

	* src/raster/ftraster.c (SMulDiv_No_Round): New macro.
	(Line_Up): Use it.
	* src/raster/ftmisc.h (FT_MulDiv_No_Round): Copied from `ftcalc.c'.

2012-05-28  Alexei Podtelezhnikov  <<EMAIL>>

	* src/base/ftoutln.c (FT_Outline_Get_Orientation): Simplify.

	We now use the cross product of the direction vectors to compute the
	outline's orientation.

2012-05-28  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.

2012-05-28  Alexei Podtelezhnikov  <<EMAIL>>

	New function FT_Outline_EmboldenXY.

	* include/freetype/ftoutln.h (FT_Outline_EmboldenXY): Define it.

	* src/base/ftoutln.c (FT_Outline_EmboldenXY): Implement it, using a
	simplified emboldening algorithm.
	(FT_Outline_Embolden): Make it a special case of
	`FT_Outline_EmboldenXY'

2012-05-07  Werner Lemberg  <<EMAIL>>

	[type1] Fix Savannah bug #36386.

	* src/type1/t1load.c (t1_load_keyword): Ignore keyword if context is
	not valid.

2012-04-07  Werner Lemberg  <<EMAIL>>

	Remove compiler warning.

	* src/truetype/ttgload.c (TT_Load_Glyph)
	[!TT_CONFIG_OPTION_EMBEDDED_BITMAPS]: Access `glyph->face' directly.

2012-03-28  Werner Lemberg  <<EMAIL>>

	[autofit] Properly copy scaler flags to script metrics object.

	Without this patch, only the dummy and cjk autohinter modules get
	them (since they copy the whole scaler object).

	* src/autofit/aflatin.c (af_latin_metrics_scale),
	src/autofit/aflatin2.c (af_latin2_metrics_scale): Implement it.

2012-03-22  Alexei Podtelezhnikov  <<EMAIL>>

	[bdflib] Remove redundant macro.

	* src/bdf/bdflib.c (isdigok): Remove and replace with sbitset, which
	is exactly the same.

2012-03-20  suzuki toshiya  <<EMAIL>>

	[configure] Fix Savannah bug #35644.

	* builds/unix/configure.raw: Check `-ansi' flag works even if gcc
	is used.  Bionic libc headers for Android lose the consistency
	when they are parsed with __STDC_VERSION__ older than 199901L or
	__STRICT_ANSI__.

2012-03-20  Werner Lemberg  <<EMAIL>>

	[bdf] Improvement to Savannah bug #35656.

	* src/bdf/bdflib.c (isdigok): Add cast, as suggested in report.

2012-03-17  Chris Liddell  <<EMAIL>>

	[type1] Fix Savannah bug #35847.

	* src/type1/t1load.c (parse_subrs): Fix the loop exit condition;
	we want to exit when we have run out of data.

2012-03-16  Werner Lemberg  <<EMAIL>>

	[bdf] Really fix Savannah bug #35658.

	* src/bdf/bdflib.c (_bdf_list_split): Add one more `field' initializer.

2012-03-14  Yann Droneaud  <<EMAIL>>

	[sfnt] Make arrays static like all others.

	* src/sfnt/ttload.c (tt_face_load_maxp, tt_face_load_os2),
	src/sfnt/ttmtx.c (tt_face_load_hhea): Add `static' keyword to frame
	fields.

2012-03-14  Huw Davies  <<EMAIL>>

	[sfnt] A refinement of the previous commit.

	* src/sfnt/sfobjs.c (tt_name_entry_ascii_from_utf16,
	tt_name_entry_ascii_from_other): Stop at null byte.

2012-03-14  Huw Davies  <<EMAIL>>

	[sfnt] Add `name' table compatibility to MS Windows.

	* src/sfnt/sfobjs.c (tt_name_entry_ascii_from_utf16,
	tt_name_entry_ascii_from_other): Don't replace `\0' with question
	marks when converting strings.

2012-03-14  Werner Lemberg  <<EMAIL>>

	[type1] Fix Savannah bug #35833.

	Based on the patch given in the bug report.

	* src/type1/t1load.c (IS_INCREMENTAL): New macro.
	(read_binary_data): Add parameter `incremental'.
	Update all callers using `IS_INCREMENTAL'.

2012-03-11  Werner Lemberg  <<EMAIL>>

	[autofit] Return correct linear advance width values.

	This was quite a subtle bug which accidentally showed up with glyph
	`afii10023' of arial.ttf (version 2.76).  This glyph is a composite;
	the first component, `E', has an advance width of 1366 font units,
	while the advance width of the composite itself (which looks like
	uppercase `E' with dieresis) is 1367 font units.  I think this is
	actually a bug in the font itself, because there is no reason that
	this glyph has not the same width as uppercase `E' without the
	dieresis.  Anyway, it helped identify this problem.

	Using the TrueType hinter, the correct value (1367) of `afii10023'
	was returned, but the autohinter mysteriously returned 1366.

	Digging in the code showed that the autohinter recursively calls
	FT_Load_Glyph to load the glyph, adding the FT_LOAD_NO_SCALE load
	flag.  However, the `linearHoriAdvance' field is still returned as a
	scaled value.  To avoid scaling twice, the old code in autofit reset
	`linearHoriAdvance', using the `horiAdvance' field.  This seemed to
	work since FT_LOAD_NO_SCALE was in use, but it failed actually,
	because `horiAdvance' is defined as the distance of the first
	subglyph's phantom points, which in turn are initialized using the
	advance width of the first subglyph.  And as the given example
	shows, these widths can differ.

	* src/autofit/afloader.c (af_loader_load_g): Temporarily set
	FT_LOAD_LINEAR_DESIGN while calling FT_Load_Glyph to get unscaled
	values for the linear advance widths.

2012-03-10  Werner Lemberg  <<EMAIL>>

	[truetype] Fix SSW instruction.

	* src/truetype/ttinterp.c (DO_SSW): SSW *does* use font units.  For
	verification, it took some time to find a font which actually uses
	this instruction.

2012-03-09  Vinnie Falco  <<EMAIL>>

	Prepare source code for amalgamation.

	* include/freetype/freetype.h: Swap order of preprocessor blocks.

2012-03-08  Werner Lemberg  <<EMAIL>>

	* Version 2.4.9 released.
	=========================


	Tag sources with `VER-2-4-9'.

	* docs/CHANGES: Updated.

	* docs/VERSION.DLL: Update documentation and bump version number to
	2.4.9.

	* README, Jamfile (RefDoc),
	builds/win32/vc2005/freetype.vcproj, builds/win32/vc2005/index.html,
	builds/win32/vc2008/freetype.vcproj, builds/win32/vc2008/index.html,
	builds/win32/vc2010/freetype.vcxproj, builds/win32/vc2010/index.html,
	builds/win32/visualc/freetype.dsp,
	builds/win32/visualc/freetype.vcproj,
	builds/win32/visualc/index.html, builds/win32/visualce/freetype.dsp,
	builds/win32/visualce/freetype.vcproj,
	builds/win32/visualce/index.html,
	builds/wince/vc2005-ce/freetype.vcproj,
	builds/wince/vc2005-ce/index.html,
	builds/wince/vc2008-ce/freetype.vcproj,
	builds/wince/vc2008-ce/index.html: s/2.4.8/2.4.9/, s/248/249/.

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 9.

	* builds/unix/configure.raw (version_info): Set to 14:1:8.

2012-03-08  Werner Lemberg  <<EMAIL>>

	[bdf] Add missing overflow check.

	* src/bdf/bdflib.c (_bdf_parse_glyphs) <BITMAP>: Add threshold for
	`glyph->bpr'.

2012-03-07  Vinnie Falco  <<EMAIL>>

	Prepare source code for amalgamation.

	* src/autofit/aferrors.h, src/bdf/bdferror.h, src/bzip2/ftbzip2.c,
	src/cache/ftcerror.h, src/cff/cfferrs.h, src/cid/ciderrs.h,
	src/gxvalid/gxverror.h, src/gzip/ftgzip.c, src/lzw/ftlzw.c,
	src/otvalid/otverror.h, src/pcf/pcferror.h, src/pfr/pfrerror.h,
	src/psaux/psauxerr.h, src/pshinter/pshnterr.h,
	src/psnames/psnamerr.h, src/raster/rasterrs.h, src/sfnt/sferrors.h,
	src/smooth/ftsmerrs.h, src/truetype/tterrors.h,
	src/type1/t1errors.h, src/type42/t42error.h, src/winfonts/fnterrs.h:
	Add #undef FT_ERR_PREFIX before #define FT_ERR_PREFIX.

2012-03-03  Werner Lemberg  <<EMAIL>>

	Fix Savannah bug #35660.

	For some divisions, we use casts to 32bit entities.  Always guard
	against division by zero with these casts also.

	* src/base/ftcalc.c (ft_div64by32): Remove redundant cast.
	(FT_MulDiv, FT_MulDiv_No_Round): Add 32bit cast.
	(FT_DivFix): Add 32bit cast (this omission triggered the bug).

2012-03-03  Werner Lemberg  <<EMAIL>>

	[psaux] Fix handling of track kerning.

	* src/psaux/afmparse.c (afm_parse_track_kern): Don't inverse sign
	for `min_kern'.  It is indeed quite common that track kerning
	*increases* spacing for very small sizes.

2012-03-02  Werner Lemberg  <<EMAIL>>

	[truetype] Fix Savannah bug #35689.

	* src/truetype/ttgload.c (TT_Load_Simple_Glyph): Check first outline
	point.

2012-03-01  Werner Lemberg  <<EMAIL>>

	[bdf] Fix Savannah bug #35656.

	* src/bdf/bdflib.c (_bdf_parse_glyphs) <_BDF_BITMAP>: Check validity
	of nibble characters instead of accessing `a2i' array.

2012-03-01  Werner Lemberg  <<EMAIL>>

	[winfonts] Fix Savannah bug #35659.

	* src/winfonts/winfnt.c (FNT_Face_Init): Check number of glyphs.

2012-03-01  Werner Lemberg  <<EMAIL>>

	[bdf] Fix Savannah bug #35658.

	* src/bdf/bdflib.c (_bdf_list_split): Initialize `field' elements
	properly.

2012-03-01  Werner Lemberg  <<EMAIL>>

	[psaux] Fix Savannah bug #35657.

	If in function `skip_spaces' the routine `skip_comment' comes to the
	end of buffer, `cur' is still increased by one, so we need to check
	for `p >= limit' and not `p == limit'.

	* src/psaux/psconv.c (PS_Conv_Strtol, PS_Conv_ToFixed,
	PS_Conv_ASCIIHexDecode, PS_Conv_EexecDecode): Fix boundary checking.

2012-03-01  Werner Lemberg  <<EMAIL>>

	[truetype] Fix Savannah bug #35646.

	* src/truetype/ttinterp.c (Ins_MIRP): Typo, present since ages.  The
	code is now in sync with the other operators (e.g. MSIRP) which
	modify twilight points.

2012-03-01  Werner Lemberg  <<EMAIL>>

	[bdf] Fix Savannah bug #35643.

	* src/bdf/bdflib.c (_bdf_list_ensure): Bring code in sync with
	comment before `_bdf_list_split', this is, really allocate at least
	five `field' elements.

2012-03-01  Werner Lemberg  <<EMAIL>>

	[bdf] Fix Savannah bug #35641.

	* src/bdf/bdflib.c (_bdf_parse_glyphs) <DWIDTH, BBX>: Abort if
	_BDF_ENCODING isn't set.  We need this because access to the `glyph'
	variable might be undefined otherwise.

2012-03-01  Werner Lemberg  <<EMAIL>>

	[truetype] Fix Savannah bug #35640.

	* src/truetype/ttinterp.c (SkipCode, TT_RunIns): Fix boundary check
	for NPUSHB and NPUSHW instructions.

2012-02-29  Werner Lemberg  <<EMAIL>>

	[truetype] Fix Savannah bug #35601.

	* src/truetype/ttinterp.c (Ins_SHZ): Use number of points instead of
	last point for loop.
	Also remove redundant boundary check.

2012-02-29  Werner Lemberg  <<EMAIL>>

	[truetype] Remove redundant check.

	* src/truetype/ttgload.c (TT_Load_Simple_Glyph): Remove redundant
	second check for ordered contour start points.

2012-02-29  Werner Lemberg  <<EMAIL>>

	[truetype] Make SHC instruction behave similar to MS rasterizer.

	* src/truetype/ttinterp.c (Ins_SHC): Handle virtual contour in
	twilight zone.

2012-02-29  Alexei Podtelezhnikov  <<EMAIL>>

	Avoid modulo operators against a power-of-two denominator.

	* src/afcjk.c (af_hint_normal_stem), src/base/ftoutln.c
	(ft_contour_has), src/cff/cffgload.c (cff_decoder_parse_charstrings)
	<cff_op_vvcurveto, cff_op_hhcurveto, cff_op_hvcurveto>,
	src/gxvalid/gxvcommn.c (GXV_32BIT_ALIGNMENT_VALIDATE),
	src/gxvalid/gxvfeat.c (gxv_feat_setting_validate): Replace `%' with
	`&' operator.

2012-02-29  Werner Lemberg  <<EMAIL>>

	[autofit] Don't synchronize digit widths for light rendering mode.

	We don't hint horizontally in this mode.

	* src/autofit/afloader.c (af_loader_load_g) <Hint_Metrics>:
	Implement it.

2012-02-26  Alexei Podtelezhnikov  <<EMAIL>>

	[type42] Minor code optimization (again).

	* src/type42/t42parse.c (t42_parse_sfnts): Simplify previous change.

2012-02-26  Mateusz Jurczyk  <<EMAIL>>
	    Werner Lemberg  <<EMAIL>>

	[smooth] Fix Savannah bug #35604.

	* src/smooth/ftsmooth.c (ft_smooth_render_generic): Use `FT_Pos'
	instead of `FT_UInt' for some variables and update comparisons
	accordingly.  A detailed analysis can be found in the bug report.

2012-02-26  Alexei Podtelezhnikov  <<EMAIL>>

	[type42] Minor code optimization.

	* src/type42/t42parse.c (t42_parse_sfnts): Use bitmask instead of
	modulo operator.

2012-02-26  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.

2012-02-26  Werner Lemberg  <<EMAIL>>

	[type1] Fix Savannah bug #35608.

	* src/type1/t1parse.c (T1_Get_Private_Dict): Reject too short
	dictionaries.

2012-02-26  Werner Lemberg  <<EMAIL>>

	[bdf] Support `ENCODING -1 <n>' format.

	* src/bdf/bdflib.c (_bdf_parse_glyphs) <ENCODING>: Implement it.

2012-02-26  Werner Lemberg  <<EMAIL>>

	[bdf] Fix Savannah bug #35607.

	* src/bdf/bdflib.c (_bdf_parse_glyphs) <ENCODING>: Normalize
	negative encoding values.

2012-02-26  Werner Lemberg  <<EMAIL>>

	[type1] Fix Savannah bug #35606.

	* src/type1/t1load.c (parse_subrs): Add proper guards for `strncmp'.

	* src/psaux/psobjs.c (ps_parser_skip_PS_token): Emit error message
	only if cur < limit.

2012-02-25  Werner Lemberg  <<EMAIL>>

	[pcf] Fix Savannah bug #35603.

	* src/pcf/pcfread.c (pcf_get_properties): Assure final zero byte in
	`strings' array.

2012-02-25  Werner Lemberg  <<EMAIL>>

	[type42] Fix Savannah bug #35602.

	* src/type42/t42parse.c (t42_parse_sfnts): Check `string_size' more
	thoroughly.

2012-02-25  Werner Lemberg  <<EMAIL>>

	[bdf] Fix Savannah bugs #35599 and #35600.

	* src/bdf/bdflib.c (ACMSG16): New warning message.
	(_bdf_parse_glyphs) <_BDF_BITMAP>: Check line length.

2012-02-24  Werner Lemberg  <<EMAIL>>

	[bdf] Fix Savannah bugs #35597 and #35598.

	* src/bdf/bdflib.c (_bdf_is_atom): Fix handling of property value.

2012-02-24  Vinnie Falco  <<EMAIL>>

	Prepare source code for amalgamation (6/6).

	* src/cff/cffdrivr.c: s/Load_Glyph/cff_glyph_load/.

	* src/cid/cidload.c: s/parse_font_matrix/cid_parse_font_matrix/.
	s/t1_init_loader/cid_init_loader/.
	s/t1_done_loader/cid_done_loader/.

	* src/psaux/t1cmap.c: s/t1_get_glyph_name/psaux_get_glyph_name/.

	* src/truetype/ttdriver.c: s/Load_Glyph/tt_glyph_load/.

	* src/type1/t1load.c: s/parse_font_matrix/t1_parse_font_matrix/.

2012-02-24  Vinnie Falco  <<EMAIL>>

	Prepare source code for amalgamation (5/6).

	* include/freetype/fterrors.h: Undefine FT_KEEP_ERR_PREFIX after
	using it.

2012-02-22  Vinnie Falco  <<EMAIL>>

	Prepare source code for amalgamation (4/6).

	* src/smooth/ftgrays.c, src/raster/ftraster.c: Undefine RAS_ARG,
	RAS_ARGS, RAS_VAR, and RAS_VARS before defining it.

	* src/smooth/ftgrays.c: s/TRaster/black_TRaster/,
	s/PRaster/black_PRaster/.
	* src/raster/ftraster.c: s/TRaster/gray_TRaster/,
	s/PRaster/gray_PRaster/.

2012-02-20  Vinnie Falco  <<EMAIL>>

	Prepare source code for amalgamation (3/6).

	* src/smooth/ftgrays.c: s/TWorker/black_TWorker/,
	s/PWorker/black_PWorker/.
	* src/raster/ftraster.c: s/TWorker/gray_TWorker/,
	s/PWorker/gray_PWorker/.

2012-02-20  Vinnie Falco  <<EMAIL>>

	Prepare source code for amalgamation (2/6).

	* src/smooth/ftgrays.c, src/raster/ftraster.c: Undefine FLOOR,
	CEILING, TRUNC, and SCALED before defining it.

2012-02-20  Vinnie Falco  <<EMAIL>>

	Prepare source code for amalgamation (1/6).

	See discussion starting at

	  https://lists.gnu.org/archive/html/freetype-devel/2012-01/msg00037.html

	* src/smooth/ftgrays.c: s/TBand/gray_TBand/.
	* src/raster/ftraster.c: s/TBand/black_TBand/.

2012-02-17  Alexei Podtelezhnikov  <<EMAIL>>

	[autofit] Fix outline flags.

	* src/autofit/afloader.c (af_loader_load_g): Don't reassign
	`outline.flags' so that this information is preserved.  See
	discussion starting at

	  https://lists.gnu.org/archive/html/freetype-devel/2012-02/msg00046.html

2012-02-11  Werner Lemberg  <<EMAIL>>

	[truetype] Fix Savannah bug #35466.

	Jump instructions are now bound to the current function.  The MS
	Windows rasterizer behaves the same, as confirmed by Greg Hitchcock.

	* src/truetype/ttinterp.h (TT_CallRec): Add `Cur_End' element.
	* src/truetype/ttobjs.h (TT_DefRecord): Add `end' element.

	* src/truetype/ttinterp.c (DO_JROT, DO_JMPR, DO_JROF): Check upper
	bound of jump address.
	(Ins_FDEF, Ins_CALL, Ins_LOOPCALL, Ins_UNKNOWN, TT_RunIns): Updated.

2012-02-11  Werner Lemberg  <<EMAIL>>

	We don't use `extensions'.

	* include/freetype/internal/ftobjs.h (FT_DriverRec): Remove
	`extensions' field.

2012-02-11  Werner Lemberg  <<EMAIL>>

	Clean up `generic' fields.

	* include/freetype/internal/ftobjs.h (FT_ModuleRec, FT_LibraryRec):
	Remove `generic' field since users can't access it.

	* src/base/ftobjs.c (FT_Done_GlyphSlot): Call `generic.finalizer' as
	advertised in the documentation of FT_Generic.
	(Destroy_Module, FT_Done_Library): Updated to changes in `ftobjs.h'.

2012-02-07  Werner Lemberg  <<EMAIL>>

	[autofit] Harmonize function arguments.

	* src/autofit/afloader.c, src/autofit/afloader.h: Use `FT_Int32' for
	`load_flags'.

2012-02-07  Werner Lemberg  <<EMAIL>>

	* src/cff/cffobjs.c (cff_face_init): Remove unnecessary casts.

2012-01-17  suzuki toshiya  <<EMAIL>>

	[gxvalid] Fix Savannah bug #35286.

	Patch submitted by anonymous reporter.

	* src/gxvalid/gxvcommn.c (gxv_XStateTable_subtable_setup):
	gxv_set_length_by_ulong_offset() must be called with 3, not 4,
	the number of the subtables in the state tables; classTable,
	stateArray, entryTable.

2012-01-17  suzuki toshiya  <<EMAIL>>

	[raccess] Modify for PIC build.

	Based on the patch provided by Erik Dahlstrom <<EMAIL>>,
	https://lists.gnu.org/archive/html/freetype-devel/2012-01/msg00010.html

	Also `raccess_guess_table[]' and `raccess_rule_by_darwin_vfs()'
	are renamed with `ft_' suffixes.

	* src/base/ftbase.h: `raccess_rule_by_darwin_vfs()' is renamed
	to `ft_raccess_rule_by_darwin_vfs()'.
	* src/base/ftobjs.c: Ditto.

	* src/base/ftrfork.c: Declarations of FT_RFork_Rule,
	raccess_guess_rec, are moved to...
	* include/freetype/internal/ftrfork.h: Here.

	* include/freetype/internal/ftrfork.h:
	FT_RFORK_RULE_ARRAY_{BEGIN,ENTRY,END} macros are defined
	to replace raccess_guess_table[] in both of PIC and non-PIC
	modes.
	* src/base/ftrfork.c: raccess_guess_table[] array is rewritten
	by FT_RFORK_RULE_ARRAY_{BEGIN,ENTRY,END}.

	* src/base/basepic.h (BasePIC): Add `ft_raccess_guess_table'
	storage.  (FT_RACCESS_GUESS_TABLE_GET): New macro to retrieve
	the function pointer from `ft_raccess_guess_table' storage in
	`BasePIC' structure.
	* src/base/ftrfork.c (FT_Raccess_Guess): Rewritten with
	FT_RACCESS_GUESS_TABLE_GET.
	(raccess_get_rule_type_from_rule_index): Add `library' as the
	first argument to the function, to retrieve the storage of
	`ft_raccess_guess_table' from it.  Also `raccess_guess_table'
	is replaced by FT_RACCESS_GUESS_TABLE_GET.
	(ft_raccess_rule_by_darwin_vfs): Ditto.

2012-01-16  suzuki toshiya  <<EMAIL>>

	Remove trailing spaces.

2012-01-16  suzuki toshiya  <<EMAIL>>

	Formatting PIC related sources.

	* src/autofit/afpic.c: Harmonize to FT2 coding conventions.
	* src/base/basepic.c: Ditto.
	* src/base/ftpic.c: Ditto.
	* src/cff/cffpic.c: Ditto.
	* src/pshinter/pshpic.c: Ditto.
	* src/psnames/pspic.c: Ditto.
	* src/raster/rastpic.c: Ditto.
	* src/sfnt/sfntpic.c: Ditto.
	* src/smooth/ftspic.c: Ditto.
	* src/truetype/ttpic.c: Ditto.

2012-01-16  suzuki toshiya  <<EMAIL>>

	[autofit] Fix the inclusion of `aflatin2.h' in PIC file.

	* src/autofit/afpic.c: Include `aflatin2.h' when
	FT_OPTION_AUTOFIT2 is defined, as afglobal.c does so.
	Unconditionally inclusion causes declared but unimplemented
	warning by GCC 4.6.

2012-01-16  suzuki toshiya  <<EMAIL>>

	[cff] Remove redundant declarations of cff_cmap_XXX_class_rec.

	* src/cff/cffpic.c: The declarations of
	FT_Init_Class_cff_cmap_encoding_class_rec() and
	FT_Init_Class_cff_cmap_unicode_class_rec() are removed.
	They can be obtained by the inclusion of cffcmap.h.
	cffcmap.h invokes FT_DECLARE_CMAP_CLASS() and it declares
	FT_Init_Class_cff_cmap_encoding_class_rec() etc in PIC mode.

2012-01-15  suzuki toshiya  <<EMAIL>>

	Fix redundant declaration warning in PIC mode.

	Originally FT_DEFINE_{DRIVER,MODULE,RENDERER}() macros were
	designed to declare xxx_pic_{free,init} by themselves.
	Because these macros are used at the end of the module
	interface (e.g. ttdriver.c) and the wrapper source to build
	a module as a single object (e.g. truetype.c) includes
	the PIC file (e.g. ttpic.c) before the module interface,
	these macros are expanded AFTER xxx_pic_{free,init} body
	when the modules are built as single object.
	The declaration after the implementation causes the redundant
	declaration warnings, so the declarations are moved to module
	PIC headers (e.g. ttpic.h).  Separating to other header files
	are needed for multi build.

	* include/freetype/internal/ftdriver.h (FT_DEFINE_DRIVER):
	Remove class_##_pic_free and class_##_pic_init declarations.
	* include/freetype/internal/ftobjs.h (FT_DEFINE_RENDERER,
	FT_DEFINE_MODULE): Ditto.

	* src/base/basepic.h: Insert a comment and fix coding style.
	* src/autofit/afpic.h: Declare autofit_module_class_pic_{free,
	init}.
	* src/cff/cffpic.h: Declare cff_driver_class_pic_{free,init}.
	* src/pshinter/pshpic.h: Declare pshinter_module_class_pic_{free,
	init}.
	* src/psnames/pspic.h: Declare psnames_module_class_pic_{free,
	init}.
	* src/raster/rastpic.h: Declare
	ft_raster{1,5}_renderer_class_pic_{free,init}
	* src/sfnt/sfntpic.h: Declare sfnt_module_class_pic_{free,init}.
	* src/smooth/ftspic.h: Declare
	ft_smooth_{,lcd_,lcdv_}renderer_class_pic_{free,init}.
	* src/truetype/ttpic.h: Declare tt_driver_class_pic_{free,init}.

2012-01-15  suzuki toshiya  <<EMAIL>>

	Make pspic.c to include module error header to fix multi build.

	* src/psnames/pspic.c: Include `psnamerr.h'.

2012-01-14  suzuki toshiya  <<EMAIL>>

	[base] Fix a dereference of uninitialized variable in PIC mode.

	* src/base/ftglyph.c (FT_Glyph_To_Bitmap): `glyph' must be
	set before dereferring to obtain `library'.  The initialization
	of `clazz', `glyph', `library' and NULL pointer check are
	reordered to minimize PIC conditionals.

2012-01-14  suzuki toshiya  <<EMAIL>>

	[base] Insert explicit cast for GCC 4.6 in PIC mode.

	* src/base/ftinit.c (FT_Add_Default_Modules): Under PIC
	configuration, FT_DEFAULT_MODULES_GET returns
	FT_Module_Class** pointer, GCC 4.6 warns that
	const FT_Module_Class* const* variable is warned as
	inappropriate to store it.  To calm it, explicit cast is
	inserted.  Also `library' is checked to prevent the NULL
	pointer dereference in FT_DEFAULT_MODULES_GET.

2012-01-13  suzuki toshiya  <<EMAIL>>

	Fix PIC build broken by d9145241fe378104ba4c12a42534549faacc92e6.

	Under PIC configuration, FT_{CFF,PSCMAPS,SFNT,TT}_SERVICES_GET
	take no arguments but derefer the variable named `library'
	internally.

	* src/cff/cffdrivr.c (cff_get_interface): Declare `library' and
	set it if non-NULL driver is passed.
	* src/truetype/ttdriver.c (tt_get_interface): Ditto.

	* src/sfnt/sfdriver.c (sfnt_get_interface): Declare `library'
	under PIC configuration, and set it if non-NULL module is given.
	* src/psnames/psmodule.c (psnames_get_interface): Ditto.

2012-01-13  suzuki toshiya  <<EMAIL>>

	Make PIC files include module error headers, to use the error codes
	with per-module prefix.

	* src/autofit/afpic.c: Include `aferrors.h'.
	* src/cff/cffpic.c: Include `cfferrs.h'.
	* src/pshinter/pshpic.c: Include `pshnterr.h'.
	* src/raster/rastpic.c: Include `rasterrs.h'.
	* src/sfnt/sfntpic.c: Include `sferrors.h'.
	* src/smooth/ftspic.c: Include `ftsmerrs.h'.
	* src/truetype/ttpic.c: Include `tterrors.h'.

2012-01-04  Tobias Ringström  <<EMAIL>>

	[truetype] Fix IP instruction if x_ppem != y_ppem.

	* src/truetype/ttinterp.c (Ins_IP): Scale `orus' coordinates
	properly.

2012-01-02  Werner Lemberg  <<EMAIL>>

	Fix tracing message for `loca' table.

	* src/truetype/ttpload.c (tt_face_get_location): Don't emit a
	warning message if the last `loca' entry references an empty glyph.

2011-12-10  Werner Lemberg  <<EMAIL>>

	Add some variable initializations.
	Reported by Richard COOK <<EMAIL>>.

	* src/type1/t1driver.c (t1_ps_get_font_value): Initialize `val'.
	* src/smooth/ftgrays.c (gray_render_conic): Initialize `levels'
	earlier.

2011-12-08  Werner Lemberg  <<EMAIL>>

	Fix serious scaling bug in `FT_Get_Advances'.

	* src/base/ftadvanc.c (FT_Get_Advances): Advance values returned by
	`FT_Load_Glyph' must be simply multiplied by 1024.

2011-12-08  Werner Lemberg  <<EMAIL>>

	* src/bdf/bdflib.c (_bdf_parse_start): Drop redundant error tracing.

2011-12-02  suzuki toshiya  <<EMAIL>>

	[mac] Unify DARWIN_NO_CARBON with FT_MACINTOSH.

	Originally FT_MACINTOSH was a pure auto macro and DARWIN_NO_CARBON
	was a configurable macro to disable Carbon-dependent code.  Because
	now configure script sets DARWIN_NO_CARBON by default and disables
	Darwin & Carbon-dependent codes, these macros can be unified.
	FT_MACINTOSH (undefined by default) is kept and DARWIN_NO_CARBON
	(defined by default) is removed, because DARWIN_NO_CARBON violates
	FT_XXX naming convention of public macros, and a macro configured by
	default is not portable for the building without configure (e.g.
	make devel).

	* builds/unix/configure.raw: Define FT_MACINTOSH if Carbon-based
	old Mac font support is requested and Carbon is available.
	* builds/unix/ftconfig.in: Undefine FT_MACINTOSH when the support
	for Mac OS X without Carbon (e.g.  Mac OS X 10.4 for ppc64) is
	requested.
	* include/freetype/config/ftconfig.in: Ditto.
	* builds/vms/ftconfig.h: Ditto.

	* src/base/ftbase.h: Remove DARWIN_NO_CARBON.
	* src/base/ftbase.c: Ditto.
	* src/base/ftobjs.c: Ditto.
	* src/base/ftrfork.c: Ditto.

	* src/base/ftmac.c: Compile the body if FT_MACINTOSH is defined
	(same with TT_USE_BYTECODE_INTERPRETER in ttinterp.c).
	* builds/mac/ftmac.c: Ditto.

	* builds/mac/FreeType.m68k_cfm.make.txt: Define FT_MACINTOSH.
	* builds/mac/FreeType.m68k_far.make.txt: Ditto.
	* builds/mac/FreeType.ppc_classic.make.txt: Ditto.
	* builds/mac/FreeType.ppc_carbon.make.txt: Ditto.

2011-11-30  suzuki toshiya  <<EMAIL>>

	Fix Savannah bug #34728 (`make devel' on Mac OS X).

	* builds/toplevel.mk: Check `/dev/null' to identify the Unix-
	like systems without `init' nor `hurd' (e.g. Mac OS X >= 10.4).
	* builds/unix/detect.mk: Ditto.

2011-11-30  suzuki toshiya  <<EMAIL>>

	[apinames] Fix the overflow of signed integer hash.

	* src/tools/apinames.c (names_add): Change the type of `h' from
	int to unsigned int, to prevent undefined behaviour in the
	overflow of signed integers (overflow of unsigned int is defined
	to be wrap around).  Found by clang test suggested by Sean
	McBride.

2011-11-30  Werner Lemberg  <<EMAIL>>

	[winfonts] Remove casts.

	* src/winfonts/winfnt.c (winfnt_driver_class): Remove all casts and
	update affected functions.
	(FNT_Size_Select): Fix number of arguments.

2011-11-30  Werner Lemberg  <<EMAIL>>

	[type42] Remove casts.

	* src/type42/t42drivr.c (t42_driver_class): Remove all casts and
	update affected functions.

	* src/type42/t42objs.c, src/type42/t42objs.h: Updated for t42 driver
	changes.

2011-11-30  Werner Lemberg  <<EMAIL>>

	[type1] Remove casts.

	* src/type1/t1driver.c (t1_driver_class): Remove all casts and
	update affected functions.

	* src/type1/t1gload.c, src/type1/t1gload.h, src/type1/t1objs.c:
	Updated for t1driver changes.
	src/type1/t1objs.h (T1_Driver): Remove unused typedef.
	Updated for t1driver changes.

2011-11-27  Werner Lemberg  <<EMAIL>>

	[bdf] Fix Savannah bug #34896.

	ENCODING now covers the whole Unicode range.

	Note, however, that this change is quite expensive since it
	increases the size of three arrays by almost 400kByte in total.  The
	right fix is to replace the logic with something smarter.
	Additionally, there exist very old BDFs for three-byte CCCII
	encoding which exceeds the range of Unicode (another reason to have
	a smarter logic).

	* src/bdf/bdf.h (bdf_font_t): Increase size of `nmod' and `umod'
	arrays.
	* src/bdf/bdflib.c (bdf_parse_t): Increase size of `have' array.

2011-11-27  Werner Lemberg  <<EMAIL>>

	[bdf] Improve tracing.

	* src/bdf/bdflib.c (DBGMSG1, DBGMSG2): New macros.
	(_bdf_parse_glyphs): Use them.

2011-11-26  Werner Lemberg  <<EMAIL>>

	Improve tracing.

	* src/bdf/bdfdrivr.c (BDF_Face_Done), src/pcf/pcfdrivr.c
	(PCF_Face_Done): Remove tracing message.

	* src/bdf/bdfdrivr.c (BDF_Face_Init), src/cff/cffobjs.c
	(cff_face_init), src/cid/cidobjs.c (cid_face_init),
	src/pfr/pfrobjs.c (pfr_face_init), src/sfnt/sfobjs.c
	(sfnt_init_face), src/truetype/ttobjs.c (tt_face_init),
	src/type1/t1objs.c (T1_Face_Init), src/type42/t42objs.c
	(T42_Face_Init), src/winfonts/winfnt.c (FNT_Face_Init): Add
	`greeting' message.

	* src/sfnt/sfobjs.c (sfnt_open_font), src/type42/t42objs.c
	(T42_Open_Face): Improve tracing.

2011-11-26  Werner Lemberg  <<EMAIL>>

	[cid] Fix error code.

	* src/cid/cidparse.c (cid_parser_new): Do it.

2011-11-26  Werner Lemberg  <<EMAIL>>

	[cff] Fix error code.

	* src/cff/cffload.c (cff_font_load): Do it.

2011-11-26  Werner Lemberg  <<EMAIL>>

	Add new error code FT_Err_Missing_Module.

	Previously, FreeType misleadingly returned
	FT_Err_Unknown_File_Format if a module was missing (or a test was
	missing completely).

	* include/freetype/fterrdef.h (FT_Err_Missing_Module): Define.

	* src/cff/cffobjs.c (cff_face_init), src/cff/cffdrivr.c
	(cff_get_glyph_name), src/cid/cidobjs.c (cid_face_init),
	src/sfnt/sfobjs.c (sfnt_init_face), src/truetype/ttobjs.c
	(tt_face_init), src/type1/t1objs.c (T1_Face_Init),
	src/type42/t42objs.c (T42_Face_Init, T42_Driver_Init): Updated.

	* src/type1/t1afm.c (T1_Read_Metrics), src/type/t1objs.c
	(T1_Face_Init), src/type42/t42objs.c (T42_Face_Init): Remove now
	redundant test for `psaux'.

2011-11-25  Werner Lemberg  <<EMAIL>>

	[bdf] Add more error messages.

	* src/bdf/bdflib.c (_bdf_set_default_spacing, _bdf_add_property):
	Add line number argument.
	Update all callers.
	(ERRMSG5, ERRMSG6, ERRMSG7, ERRMSG8, ERRMSG9): New macros.
	(_bdf_readstream, _bdf_set_default_spacing, _bdf_add_property,
	_bdf_parse_glyphs, _bdf_parse_start): Add error messages.

2011-11-24  Werner Lemberg  <<EMAIL>>

	* include/freetype/fterrors.h: Remove dead code.

2011-11-15  Werner Lemberg  <<EMAIL>>

	* docs/releases: Updated.

2011-11-15  Werner Lemberg  <<EMAIL>>

	* Version 2.4.8 released.
	=========================


	Tag sources with `VER-2-4-8'.

	* docs/CHANGES: Updated.

	* docs/VERSION.DLL: Update documentation and bump version number to
	2.4.8.

	* README, Jamfile (RefDoc),
	builds/win32/vc2005/freetype.vcproj, builds/win32/vc2005/index.html,
	builds/win32/vc2008/freetype.vcproj, builds/win32/vc2008/index.html,
	builds/win32/vc2010/freetype.vcxproj, builds/win32/vc2010/index.html,
	builds/win32/visualc/freetype.dsp,
	builds/win32/visualc/freetype.vcproj,
	builds/win32/visualc/index.html, builds/win32/visualce/freetype.dsp,
	builds/win32/visualce/freetype.vcproj,
	builds/win32/visualce/index.html,
	builds/wince/vc2005-ce/freetype.vcproj,
	builds/wince/vc2005-ce/index.html,
	builds/wince/vc2008-ce/freetype.vcproj,
	builds/wince/vc2008-ce/index.html: s/2.4.7/2.4.8/, s/247/248/.

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 8.

	* builds/unix/configure.raw (version_info): Set to 14:0:8.

2011-11-13  Chris Liddell  <<EMAIL>>

	Add FT_Get_PS_Font_Value() API.

	This allows a Type 1 font face to be interrogated to retrieve most
	of the dictionary keys (keys not relevant to FreeType's Type 1
	interpreter are not available).

	* include/freetype/internal/services/svpsinfo.h
	(PS_GetFontValueFunc): New typedef.
	(PSInfo): Add `ps_get_font_value'.
	(FT_DEFINE_SERVICE_PSINFOREC): Updated.

	* include/freetype/internal/t1types.h (T1_EncodingType): Moved to...
	* include/freetype/t1tables.h: Here.
	(PS_Dict_Keys): New enumeration.
	(FT_Get_PS_Font_Value): New declaration.

	* src/base/fttype1.c (FT_Get_PS_Font_Value): New function.

	* src/type1/t1driver.c (t1_ps_get_font_value): This new function
	does the real job.
	(t1_service_ps_info): Add it.

	* src/cff/cffdrivr.c (cff_service_ps_info), src/cid/cidriver.c
	(cid_service_ps_info), src/type42/t42drivr.c (t42_service_ps_info):
	Updated.

2011-11-08  Braden Thomas  <<EMAIL>>

	[cid] Various loading fixes.

	* src/cid/cidload.c (cid_load_keyword) <default>,
	(parse_font_matrix, parse_expansion_factor): Correctly check number
	of dictionaries.
	(cid_read_subrs): Protect against invalid values of `num_subrs'.
	Assure that the elements of the `offsets' array are ascending.

2011-11-05  Werner Lemberg  <<EMAIL>>

	* README: We use copyright ranges also.

	According to

	  https://www.gnu.org/prep/maintain/html_node/Copyright-Notices.html

	this should be mentioned explicitly.

2011-10-30  suzuki toshiya  <<EMAIL>>

	[raccess] Supplement for previous fix.

	* src/base/ftbase.h (raccess_rule_by_darwin_vfs): Do not declare
	it on native Mac OS X.
	* src/base/ftrfork.c (raccess_get_rule_type_from_rule_index):
	Hide raccess_get_rule_type_from_rule_index() on native Mac OS X
	too.

2011-10-30  suzuki toshiya  <<EMAIL>>

	[raccess] Hide raccess_rule_by_darwin_vfs() on native Mac OS X.

	* src/base/ftrfork.c (raccess_rule_by_darwin_vfs): Do not
	compile on native Mac OS X because it is not used.

2011-10-25  Werner Lemberg  <<EMAIL>>

	[truetype] Fix MD instruction for twilight zone.

	* src/truetype/ttinterp.c (Ins_MD): Without this fix, the MD
	instruction applied to original coordinates of twilight points
	always returns zero.

2011-10-18  Werner Lemberg  <<EMAIL>>

	* Version 2.4.7 released.
	=========================


	Tag sources with `VER-2-4-7'.

	* docs/CHANGES: Updated.

	* docs/VERSION.DLL: Update documentation and bump version number to
	2.4.7.

	* README, Jamfile (RefDoc),
	builds/win32/vc2005/freetype.vcproj, builds/win32/vc2005/index.html,
	builds/win32/vc2008/freetype.vcproj, builds/win32/vc2008/index.html,
	builds/win32/vc2010/freetype.vcxproj, builds/win32/vc2010/index.html,
	builds/win32/visualc/freetype.dsp,
	builds/win32/visualc/freetype.vcproj,
	builds/win32/visualc/index.html, builds/win32/visualce/freetype.dsp,
	builds/win32/visualce/freetype.vcproj,
	builds/win32/visualce/index.html,
	builds/wince/vc2005-ce/freetype.vcproj,
	builds/wince/vc2005-ce/index.html,
	builds/wince/vc2008-ce/freetype.vcproj,
	builds/wince/vc2008-ce/index.html: s/2.4.6/2.4.7/, s/246/247/.

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 7.

	* builds/unix/configure.raw (version_info): Set to 13:2:7.

2011-10-15  Kal Conley  <<EMAIL>>

	Fix handling of transformations if no renderer is present.

	* src/base/ftobjs.c (FT_Load_Glyph): Thinko.

2011-10-15  Kal Conley  <<EMAIL>>

	Fix conditions for autohinting.

	* src/base/ftobjs.c (FT_Load_Glyph): Handle
	FT_LOAD_IGNORE_TRANSFORM.

2011-10-07  suzuki toshiya  <<EMAIL>>

	[gxvalid] Fix a bug to detect too large offset in morx table.

	* src/gxvalid/gxvmorx2.c
	(gxv_morx_subtable_type2_ligActionIndex_validate): Fix a bug
	that too large positive offset cannot be detected.

2011-10-01  Braden Thomas  <<EMAIL>>

	Handle some border cases.

	* include/freetype/config/ftstdlib.h (FT_USHORT_MAX): New macro.

	* src/base/ftbitmap.c (FT_Bitmap_Convert): Protect against invalid
	value of `target->rows'.

	* src/psaux/t1decode.c (t1_decoder_parse_charstrings): Add check for
	flex start.

	* src/raster/ftrend1.c (ft_raster1_render): Check `width' and
	`height'.

	* src/truetype/ttgxvar.c (TT_Vary_Get_Glyph_Deltas): Protect against
	invalid values in `localpoints' array.

2011-10-01  Werner Lemberg  <<EMAIL>>

	[psnames] Handle zapfdingbats.
	Problem reported by Nicolas Rougier <<EMAIL>>.

	* src/tools/glnames.py (adobe_glyph_list): Add data from AGL's
	`zapfdingbats.txt' file.

	* src/psnames/pstables.h: Regenerated.

2011-09-27  Simon Bünzli  <<EMAIL>>

	[type1] Fix Savannah bug #34189.

	* src/type1/t1load.c (T1_Open_Face): Initialize
	`face->len_buildchar'.

2011-09-26  Werner Lemberg  <<EMAIL>>

	[cff] Dump SIDs while tracing.

	* src/cff/cffobjs.c (cff_face_init): Do it.

	* src/cff/cffparse.c (cff_parser_run) [FT_DEBUG_LEVEL_TRACE]
	<cff_kind_string>: Identify as SID.

2011-09-17  Werner Lemberg  <<EMAIL>>

	Remove unused FT_ALIGNMENT macro.

	* builds/unix/ftconfig.in, builds/vms/ftconfig.h,
	include/freetype/config/ftconfig.h: Do it.

2011-09-17  Alexei Podtelezhnikov  <<EMAIL>>

	[smooth] Slightly optimize conic and cubic flatteners.

	* src/smooth/ftgrays.c (gray_render_conic, gray_render_cubic): Move
	out some code from the main loop to speed it up.

2011-09-11  Tomas Hoger  <<EMAIL>>

	Slightly improve LZW_CLEAR handling.

	* src/lzw/ftzopen.c (ft_lzwstate_io) <FT_LZW_PHASE_CODE>:
	Ensure that subsequent (modulo garbage byte(s)) LZW_CLEAR codes are
	handled as clear codes.  This also re-sets old_code and old_char to
	predictable values, which is a little better than using `random'
	ones if the code following LZW_CLEAR is invalid.

2011-09-11  Tomas Hoger  <<EMAIL>>

	Add explicit LZW decompression stack size limit.

	Stack larger than 1<<LZW_MAX_BITS is never needed if prefix table is
	constructed correctly.  It's even less than that, see e.g.
	libarchive code comment for a better size upper bound:

	  http://code.google.com/p/libarchive/source/browse/trunk/libarchive/archive_read_support_filter_compress.c?r=3635#121

	This patch adds explicit stack size limit, enforced when stack is
	realloced.

	An alternative is to ensure that code < state->prefix[code - 256]
	when traversing prefix table.  Such check is less efficient and
	should not be required if prefix table is constructed correctly in
	the first place.

	* src/lzw/ftzopen.c (ft_lzwstate_stack_grow): Implement it.

2011-09-11  Tomas Hoger  <<EMAIL>>

	Protect against loops in the prefix table.

	LZW decompressor did not sufficiently check codes read from the
	input LZW stream.  A specially-crafted or corrupted input could
	create a loop in the prefix table, which leads to memory usage
	spikes, as there's no decompression stack size limit.

	* src/lzw/ftzopen.c (ft_lzwstate_io) <FT_LZW_PHASE_START>: First
	code in valid LZW stream must be 0..255.
	<FT_LZW_PHASE_CODE>: In the special KwKwK case, code == free_ent,
	code > free_ent is invalid.

2011-09-09  Werner Lemberg  <<EMAIL>>

	Better tracing of metrics.

	* src/base/ftobjs.c (FT_Request_Size, FT_Select_Size): Decorate with
	FT_TRACE.

2011-09-07  Werner Lemberg  <<EMAIL>>

	[cff] Fix Savannah bug #33816.

	* src/cff/cfftypes.h (CFF_FontRecDictRec): New member
	`has_font_matrix'.
	* src/cff/cffparse.c (cff_parse_font_matrix): Set it.
	Update tracing output.
	* src/cff/cffobjs.c (cff_face_init): Use it so that the heuristics
	can be removed.

2011-08-30  Werner Lemberg  <<EMAIL>>

	Better tracing of metrics.

	* src/base/ftobjs.c (FT_Select_Metrics, FT_Request_Metrics):
	Decorate with FT_TRACE.

2011-08-25  Werner Lemberg  <<EMAIL>>

	[cff] Better tracing of the parsing process.

	* src/cff/cffload.c (cff_subfont_load, cff_font_load): Decorate with
	FT_TRACE.

	* src/cff/cffparse.c (cff_parse_font_matrix, cff_parse_font_bbox,
	cff_parse_private_dict, cff_parse_cid_ros): Updated.
	(CFF_FIELD_NUM, CFF_FIELD_FIXED, CFF_FIELD_FIXED_1000,
	CFF_FIELD_STRING, CFF_FIELD_BOOL, CFF_FIELD_CALLBACK, CFF_FIELD,
	CFF_FIELD_DELTA): Add argument for ID.
	(cff_parser_run): Decorate with FT_TRACE.

	* src/cff/cffparse.h (CFF_Field_Handler) [FT_DEBUG_LEVEL_TRACE]: Add
	`id' member.

	* src/cff/cfftoken.h: Add IDs to all fields.

2011-08-16  Werner Lemberg  <<EMAIL>>

	Fix Savannah bug #34022.

	* README, docs/INSTALL: Remove references to UPGRADE.UNIX.

2011-08-15  Werner Lemberg  <<EMAIL>>

	Fix Savannah bug #34018.

	* docs/UPGRADE.UNIX: Removed.  Obsolete.

2011-08-15  David Bevan  <<EMAIL>>

	Fix Savannah bug #33992.

	* src/base/ftstroke.c (FT_Stroker_ParseOutline): Fix border case.

2011-08-12  Werner Lemberg  <<EMAIL>

	[truetype] Fix degenerate case in S{P,F,DP}VTL opcodes.

	* src/truetype/ttinterp.c (Ins_SxVTL): Handle p1 == p2 specially.
	(Ins_SDPVTL): Handle v1 == v2 specially.

2011-08-09  Werner Lemberg  <<EMAIL>>

	[cff] Fix Savannah bug #33975.

	* src/cff/cffparse.c (cff_parse_font_matrix): Fix typo.

2011-07-29  Werner Lemberg  <<EMAIL>>

	* Version 2.4.6 released.
	=========================


	Tag sources with `VER-2-4-6'.

	* docs/CHANGES: Updated.

	* docs/VERSION.DLL: Update documentation and bump version number to
	2.4.6.

	* README, Jamfile (RefDoc),
	builds/win32/vc2005/freetype.vcproj, builds/win32/vc2005/index.html,
	builds/win32/vc2008/freetype.vcproj, builds/win32/vc2008/index.html,
	builds/win32/vc2010/freetype.vcxproj, builds/win32/vc2010/index.html,
	builds/win32/visualc/freetype.dsp,
	builds/win32/visualc/freetype.vcproj,
	builds/win32/visualc/index.html, builds/win32/visualce/freetype.dsp,
	builds/win32/visualce/freetype.vcproj,
	builds/win32/visualce/index.html,
	builds/wince/vc2005-ce/freetype.vcproj,
	builds/wince/vc2005-ce/index.html,
	builds/wince/vc2008-ce/freetype.vcproj,
	builds/wince/vc2008-ce/index.html: s/2.4.5/2.4.6/, s/245/246/.

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 6.

	* builds/unix/configure.raw (version_info): Set to 13:1:7.

2011-07-29  Werner Lemberg  <<EMAIL>>

	[cff] Add some more tracing infos.

	* src/cff/cffparse.c (cff_parse_font_matrix, cff_parse_font_bbox,
	cff_parse_cid_ros): Add tracing.

2011-07-22  Dirk Müller  <<EMAIL>>

	[psaux, type1] Fix null pointer dereferences.

	Found with font fuzzying.

	* src/psaux/t1decode.c (t1_decoder_parse_charstrings): Check
	`decoder->buildchar'.

	* src/type1/t1load.c (t1_load_keyword): Check `blend->num_designs'.

2011-07-20  Chris Morgan  <<EMAIL>>

	Add FT_CONFIG_OPTION_DISABLE_STREAM_SUPPORT.

	Useful for embedded systems which don't need file stream support.

	* src/base/ftsystem.c, src/base/ftobjs.c (FT_Stream_New): Implement
	it.

2011-07-20  Elton Chung  <<EMAIL>>

	* src/base/ftpatent.c (FT_Face_SetUnpatentedHinting): Fix typo.

2011-07-16  Steven Chu  <<EMAIL>>

	[truetype] Fix metrics on size request for scalable fonts.

	* src/truetype/ttdriver.c (tt_size_request): Fix copying metrics
	from TT_Size to FT_Size if scalable font.

	See

	  https://lists.gnu.org/archive/html/freetype-devel/2011-07/msg00049.html

	for some comparison images.

2011-07-14  Matthias Drochner  <<EMAIL>>.

	[psaux] Fix potential sign extension problems.

	When shifting right a signed value, it is not defined by the
	C standard whether one gets a sign extension or not.  Use a macro to
	do an explicit cast from a signed short (assuming that this is
	16bit) to an int.

	* src/psaux/t1decode.c (Fix2Int): New macro.
	Use it where appropriate.

2011-07-14  Werner Lemberg  <<EMAIL>>

	* src/psaux/t1decode.c (t1_decoder_parse_charstrings)
	<op_callothersubr>: Better handling of subroutine index 0.
	From Matthias Drochner <<EMAIL>>.

2011-07-10  Алексей Подтележников  <<EMAIL>>

	[psaux] Optimize previous commit.

	* src/psaux/t1decode.c (t1_decoder_parse_charstrings)
	<op_callothersubr>: Move error check down to avoid testing twice for
	good cases.

2011-07-08  Werner Lemberg  <<EMAIL>>

	[psaux] Add better argument check for `callothersubr'.

	* src/psaux/t1decode.c (t1_decoder_parse_charstrings)
	<op_callothersubr>: Reject negative arguments.

2011-07-07  Werner Lemberg  <<EMAIL>>

	[sfnt] Try harder to find non-zero values for ascender and descender.

	* src/sfnt/sfobjs.c (sfnt_load_face): Consult `OS/2' table in case
	the `hhea' table's values are zero.

2011-07-03  Werner Lemberg  <<EMAIL>>

	Fix previous commit.

	We want to unset FT_FACE_FLAG_SCALABLE only if there are bitmap
	strikes in the font.

	* src/truetype/ttobjs.c (tt_face_init): Implement it.

	* docs/CHANGES: Updated.

2011-07-02  Just Fill Bugs  <<EMAIL>>

	[truetype] Fix Savannah bug #33246.

	* src/truetype/ttobjs.c (tt_check_single_notdef): New function.
	(tt_face_init): Use it to test FT_FACE_FLAG_SCALABLE.

2011-07-02  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.

2011-07-02  David Bevan  <<EMAIL>>

	[ftstroke] Major revision.

	The main problems
	-----------------

	  o If FT_STROKER_LINEJOIN_BEVEL was specified, unlimited miter
	    joins (not bevel joins) were generated.  Indeed, the meanings of
	    `miter' and `bevel' were incorrectly reversed (consistently) in
	    both the code and comments.

	  o The way bevel joins were constructed (whether specified
	    explicitly, or created as a result of exceeding the miter limit)
	    did not match what is required for stroked text in PostScript or
	    PDF.

	The main fixes
	--------------

	  o The behaviour of FT_STROKER_LINEJOIN_BEVEL has been corrected.

	  o A new line join style, FT_STROKER_LINEJOIN_MITER_FIXED, has been
	    introduced to support PostScript and PDF miter joins.

	  o FT_STROKER_LINEJOIN_MITER_VARIABLE has been introduced as an
	    alias for FT_STROKER_LINEJOIN_MITER.

	Additionally, a variety of stroking errors have been fixed.  These
	would cause various artifacts (including points `at infinity'),
	especially when stroking poor quality fonts.

	See

	  https://lists.gnu.org/archive/html/freetype-devel/2011-07/msg00001.html

	for example documents.  The FreeType stroker now produces results
	very similar to that produced by GhostScript and Distiller for these
	fonts.

	Other problems
	--------------

	The following problems have been resolved:

	  o Inside corners could be generated incorrectly.  Intersecting the
	    inside corner could cause a missing triangular area and other
	    effects.

	    The intersection point can only be used if the join is between
	    two lines and both lines are long enough.  The `optimization'
	    condition in `ft_stroker_inside' has been corrected; this
	    requires the line length to be passed into various functions and
	    stored in `FT_StrokerRec'.

	  o Incorrect cubic curves could be generated.  The angle
	    calculations in `FT_Stroker_CubicTo' have been corrected to
	    handle the case of the curve crossing the +/-PI direction.

	  o If the border radius was greater than the radius of curvature of
	    a curve, then the negative sector would end up outside (not
	    inside) the border.  This situation is now recognized and the
	    negative sector is circumnavigated in the opposite direction.
	    (If round line joins are being used, this code is disabled
	    because the line join will always cover the negative sector.)

	  o When a curve is split, the arcs may not join smoothly (especially
	    if the curve turns sharply back on itself).  Changes in
	    direction between adjacent arcs were not handled.  A round
	    corner is now added if the deviation from one arc to the next is
	    greater than a suitable threshold.

	  o The current direction wasn't retained if a the outline contained
	    a zero length lineto or a curve that was determined to be
	    `basically a point'.  This could cause a spurious join to be
	    added.

	  o Cubics with close control points could be mishandled.  All eight
	    cases are now distinguished correctly.

	Other improvements
	------------------

	o Borders for cubic curves could be too `flat'.
	  FT_SMALL_CUBIC_THRESHOLD has been reduced a little to prevent
	  this.

	o The handling and use of movable points has been simplified a
	  little.

	o Various values are now computed only if the results are actually
	  needed.

	o The directions of the outer and inner borders have been swapped,
	  as recommended by Graham Asher.

	* src/base/ftstroke.c: Revised.
	* include/freetype/ftstroke.h: Updated.

2011-06-30  İsmail Dönmez  <<EMAIL>>

	* builds/toplevel.mk: We use git, not CVS, thus skip `.gitignore'.

2011-06-29  Werner Lemberg  <<EMAIL>>

	[bdf] Fix Savannah bug #33663.

	* src/bdf/bdflib.c (_bdf_parse_glyphs): Handle negative values for
	ENCODING correctly.

	* docs/CHANGES: Document it.

2011-06-24  Werner Lemberg  <<EMAIL>>

	* Version 2.4.5 released.
	=========================


	Tag sources with `VER-2-4-5'.

	* docs/CHANGES: Updated.

	* docs/VERSION.DLL: Update documentation and bump version number to
	2.4.5

	* README, Jamfile (RefDoc),
	builds/win32/vc2005/freetype.vcproj, builds/win32/vc2005/index.html,
	builds/win32/vc2008/freetype.vcproj, builds/win32/vc2008/index.html,
	builds/win32/vc2010/freetype.vcxproj, builds/win32/vc2010/index.html,
	builds/win32/visualc/freetype.dsp,
	builds/win32/visualc/freetype.vcproj,
	builds/win32/visualc/index.html, builds/win32/visualce/freetype.dsp,
	builds/win32/visualce/freetype.vcproj,
	builds/win32/visualce/index.html,
	builds/wince/vc2005-ce/freetype.vcproj,
	builds/wince/vc2005-ce/index.html,
	builds/wince/vc2008-ce/freetype.vcproj,
	builds/wince/vc2008-ce/index.html: s/2.4.4/2.4.5/, s/244/245/.

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 5.

	* builds/unix/configure.raw (version_info): Set to 13:0:7.

2011-06-20  Werner Lemberg  <<EMAIL>>

	* src/autofit/aflatin.c (af_latin_metrics_scale_dim): Fix change
	from 2011-05-04.

2011-06-19  suzuki toshiya  <<EMAIL>>

	[gxvalid] make the `prop' validation tracing verbose.

	* src/gxvalid/gxvprop.c: Add tracing messages for errors.

2011-06-19  suzuki toshiya  <<EMAIL>>

	[autogen.sh] Reflect environment variable LIBTOOLIZE.

2011-06-18  Werner Lemberg  <<EMAIL>>

	Update license documentation.

	* docs/GPL.TXT: Renamed to...
	* docs/GPLv2.TXT: This.

	* docs/LICENSE.TXT: Updated.

2011-06-14  suzuki toshiya  <<EMAIL>>

	Fix g++4.6 compiler warnings in module drivers.

	The background is same with previous commit.

	* src/truetype/ttgxvar.c (ft_var_readpackedpoints):
	Init `points'.  (TT_Vary_Get_Glyph_Deltas): Init
	`delta_xy'.  (TT_Get_MM_Var): Init `mmvar'.
	* src/type1/t1load.c (T1_Get_MM_Var): Ditto.
	* src/cff/cffdrivr.c (cff_ps_get_font_info): Init
	`font_info'.
	* src/cff/cffload.c (cff_index_get_pointers): Init `t'.
	(cff_font_load): Init `sub'.
	* src/cff/cffobjs.c (cff_size_init): Init `internal'.
	(cff_face_init): Init `cff'.
	* src/pfr/pfrload.c (pfr_extra_item_load_stem_snaps):
	Init `snaps'.
	* src/pcf/pcfread.c (pcf_get_properties): Init `properties'.
	(pcf_get_bitmaps): Init `offsets'.  (pcf_get_encodings):
	Init `tmpEncoding'.
	* src/sfnt/ttload.c (tt_face_load_gasp): Init `gaspranges'.
	* src/sfnt/ttsbit.c (Load_SBit_Image): Init `components'.
	* src/cache/ftcmru.c (FTC_MruList_New): Init `node'.
	* src/gzip/ftgzip.c (FT_Stream_OpenGzip): Init `zip' and
	`zip_buff'.
	* src/lzw/ftlzw.c (FT_Stream_OpenLZW): Init `zip'.
	* src/bzip2/ftbzip2.c (FT_Stream_OpenBzip2): Init `zip'.

2011-06-14  suzuki toshiya  <<EMAIL>>

	[base] Fix g++4.6 compiler warnings in src/base/*.c.

	Passing uninitialized pointer to FT_NEW() families is
	not problematic theoretically (as far as the returned
	pointer is checked before writing), but g++4.6 dislikes
	it and warns by -Wuninitialized.  Initialize them by NULL.

	* src/base/ftobjs.c (FT_Stream_New): Init `stream'.
	(new_memory_stream): Ditto.
	(FT_New_GlyphSlot): Init `slot'.
	(FT_CMap_New): Init `cmap'.
	(open_face_PS_from_sfnt_stream): Init `sfnt_ps'.
	(Mac_Read_POST_Resource): Init `pfb_data'.
	(Mac_Read_sfnt_Resource): Init `sfnt_data'.
	* src/base/ftrfork.c (FT_Raccess_Get_DataOffsets):
	Init `offsets_internal' and `ref'.
	(raccess_guess_darwin_hfsplus): Init `newpath'.
	(raccess_guess_darwin_newvfs): Ditto.
	* src/base/ftbitmap.c (ft_bitmap_assure_buffer):
	Init `buffer'.
	* src/base/ftstroke.c (FT_Stroker_New): Init `stroker'.

2011-06-14  suzuki toshiya  <<EMAIL>>

	[gxvalid] Cleanup.

	Some invalid, overrunning, unrecommended non-zero values
	are cared in paranoid validation mode only.  There are
	many lines looking like:

	  if ( valid->root->level >= FT_VALIDATE_PARANOID )
	    FT_INVALID_xxx;

	To simplify them, GXV_SET_ERR_IF_PARANOID( err ) is
	introduced for more paranoid validation in future.

	* src/gxvalid/gxvcommn.h (IS_PARANOID_VALIDATION):
	New macro to assure valid->root->level is more or
	equal to FT_VALIDATE_PARANOID.  (GXV_SET_ERR_IF_PARANOID):
	New macro to raise an error if in paranoid validation.
	* src/gxvalid/gxvcommn.c: Use GXV_SET_ERR_IF_PARANOID().
	* src/gxvalid/gxvfeat.c: Ditto.
	* src/gxvalid/gxvjust.c: Ditto.
	* src/gxvalid/gxvkern.c: Ditto.
	* src/gxvalid/gxvmort.c: Ditto.
	* src/gxvalid/gxvmort0.c: Ditto.
	* src/gxvalid/gxvmort1.c: Ditto.
	* src/gxvalid/gxvmort2.c: Ditto.
	* src/gxvalid/gxvmorx1.c: Ditto.
	* src/gxvalid/gxvmorx2.c: Ditto.

2011-06-14  suzuki toshiya  <<EMAIL>>

	[gxvalid] Fix gcc4.6 compiler warnings in gxvtrak.c.

	* src/gxvalid/gxvtrak.c (gxv_trak_trackTable_validate):
	Check different entries pointing same tracking value.
	(gxv_trak_validate): Remove unused variable `table_size'.

2011-06-14  suzuki toshiya  <<EMAIL>>

	[gxvalid] Fix gcc4.6 compiler warnings in gxvmorx*.c.

	* src/gxvalid/gxvmorx.c (gxv_morx_subtables_validate):
	Conditionalize unvalidated variable `subFeatureFlags'.
	(gxv_morx_chain_validate): Conditionalize unvalidated
	variable `defaultFlags'.

	* src/gxvalid/gxvmorx0.c
	(gxv_morx_subtable_type0_entry_validate):
	Conditionalize unvalidated variables; `markFirst',
	`dontAdvance', `markLast', `verb'.

	* src/gxvalid/gxvmorx1.c
	(gxv_morx_subtable_type1_entry_validate): Conditionalize
	unvalidated variables; `setMark', `dontAdvance'.

	* src/gxvalid/gxvmorx2.c
	(gxv_morx_subtable_type2_ligActionOffset_validate):
	Conditionalize unvalidated variables; `last', `store'.
	Checking for overrunning offset is added.
	(gxv_morx_subtable_type2_entry_validate):
	Conditionalize unvalidated variables; `setComponent',
	`dontAdvance', `performAction'.
	(gxv_morx_subtable_type2_ligatureTable_validate):
	Check if the GID for ligature does not exceed the
	max GID in `maxp' table.

	* src/gxvalid/gxvmort5.c
	(gxv_morx_subtable_type5_InsertList_validate):
	Conditionalize unvalidated loading of `insert_glyphID'
	array.  (gxv_morx_subtable_type5_entry_validate):
	Conditionalize unvalidated variables; `setMark',
	`dontAdvance', `currentIsKashidaLike',
	`markedIsKashidaLike', `currentInsertBefore',
	`markedInsertBefore'.

2011-06-14  suzuki toshiya  <<EMAIL>>

	[gxvalid] Fix gcc4.6 compiler warnings in gxvmort*.c.

	* src/gxvalid/gxvmort.c (gxv_mort_subtables_validate):
	Conditionalize unvalidated variable `subFeatureFlags'.
	(gxv_mort_chain_validate): Conditionalize unvalidated
	variable `defaultFlags'.

	* src/gxvalid/gxvmort0.c
	(gxv_mort_subtable_type0_entry_validate): Check the
	conflict of the marks for the glyphs.

	* src/gxvalid/gxvmort1.c
	(gxv_mort_subtable_type1_offset_to_subst_validate):
	Local variables `min_gid', `max_gid' are replaced by
	variables in the validator.
	(gxv_mort_subtable_type1_entry_validate): Conditionalize
	unvalidated variables; `setMark', `dontAdvance'.
	(gxv_mort_subtable_type1_substTable_validate):
	Validate the GID by the min/max GIDs in the validator.

	* src/gxvalid/gxvmort2.c
	(gxv_mort_subtable_type2_ligActionOffset_validate):
	Conditionalize unvalidated variables; `last', `store'.
	Checking for overrunning offset is added.
	(gxv_mort_subtable_type2_entry_validate):
	Conditionalize unvalidated variables; `setComponent',
	`dontAdvance'.
	(gxv_mort_subtable_type2_ligatureTable_validate):
	Check if the GID for ligature does not exceed the
	max GID in `maxp' table.

	* src/gxvalid/gxvmort5.c
	(gxv_mort_subtable_type5_InsertList_validate):
	Conditionalize unvalidated loading of `insert_glyphID'
	array.  (gxv_mort_subtable_type5_entry_validate):
	Conditionalize unvalidated variables; `setMark',
	`dontAdvance', `currentIsKashidaLike',
	`markedIsKashidaLike', `currentInsertBefore',
	`markedInsertBefore'.

2011-06-14  suzuki toshiya  <<EMAIL>>

	[gxvalid] Fix gcc4.6 compiler warnings in gxvkern.c.

	* src/gxvalid/gxvkern.c
	(gxv_kern_subtable_fmt0_pairs_validate): Conditionalize
	unvalidated variable `kernValue'.
	(gxv_kern_subtable_fmt1_entry_validate): Conditionalize
	unvalidated variables; `push', `dontAdvance', `kernAction',
	`kernValue'.
	(gxv_kern_coverage_new_apple_validate): Conditionalize
	trace-only variables; `kernVertical', `kernCrossStream',
	`kernVariation'.
	(gxv_kern_coverage_classic_apple_validate): Conditionalize
	trace-only variables; `horizontal', `cross_stream'.
	(gxv_kern_coverage_classic_microsoft_validate):
	Conditionalize trace-only variables; `horizontal',
	`minimum', `cross_stream', `override'.
	(gxv_kern_subtable_validate): Conditionalize trace-only
	variables; `version', `tupleIndex'.

2011-06-14  suzuki toshiya  <<EMAIL>>

	[gxvalid] Fix gcc4.6 compiler warnings in gxvjust.c.

	* src/gxvalid/gxvjust.c (gxv_just_check_max_gid):
	New function to unify the checks of too large GID.
	(gxv_just_wdp_entry_validate): Conditionalize unvalidated
	variables; `beforeGrowLimit', `beforeShrinkGrowLimit',
	`afterGrowLimit', `afterShrinkGrowLimit', `growFlags',
	`shrinkFlags'.  Additional check for non-zero values in
	unused storage `justClass' is added.
	(gxv_just_actSubrecord_type0_validate): Conditionalize
	unvalidated variable `order'.  GID is checked by
	gxv_just_check_max_gid().  Additional check for upside-down
	relationship between `lowerLimit' and `upperLimit' is added.
	(gxv_just_actSubrecord_type1_validate): GID is checked by
	gxv_just_check_max_gid().
	(gxv_just_actSubrecord_type2_validate): Conditionalize
	unvalidated variable `substThreshhold'.  GID is checked by
	gxv_just_check_max_gid().
	(gxv_just_actSubrecord_type5_validate): GID is checked by
	gxv_just_check_max_gid().
	(gxv_just_classTable_entry_validate): Conditionalize
	unvalidated variables; `setMark', `dontAdvance',
	`markClass', `currentClass'.

2011-06-14  suzuki toshiya  <<EMAIL>>

	[gxvalid] Preparation to fix gcc4.6 compiler warnings.

	* src/gxvalid/gxvcommn.h (GXV_LOAD_TRACE_VARS): New macro to
	conditionalize the variable which is only used for trace messages.
	Automatically set by FT_DEBUG_LEVEL_TRACE.
	(GXV_LOAD_UNUSED_VARS): New macro to conditionalize the loading of
	unvalidated variables.  Undefined by default to calm gcc4.6 warning.
	(GXV_ValidatorRec.{min_gid,max_gid}): New variables to hold defined
	GID ranges, for the comparison of GID ranges in different subtables.

2011-06-08  Werner Lemberg  <<EMAIL>>

	[autofit] Remove unused structure member.

	* src/autofit/afhints.h (AF_SegmentRec): Remove `contour'.
	* src/autofit/aflatin.c (af_latin_hints_compute_segments),
	src/autofit/aflatin2.c (af_latin2_hints_compute_segments): Updated.

2011-05-30  Werner Lemberg  <<EMAIL>>

	Fix g++ 4.6 compilation.

	* src/autofit/afhints.c (af_glyph_hints_dump_segments,
	af_glyph_hints_dump_edges): Use cast.

2011-05-30  Werner Lemberg  <<EMAIL>>

	Fix gcc 4.6 compiler warnings.

	* src/autofit/afcjk.c (af_cjk_metrics_init_blues): Use casts and
	remove unused variables.
	* src/autofit/aflatin.c (af_latin_hints_compute_edges): Comment out
	`up_dir'.
	* src/smooth/ftsmooth.c (ft_smooth_render_generic): Use `height_org'
	and `width_org' conditionalized.

2011-05-28  suzuki toshiya  <<EMAIL>>

	[mac] Conditionalize the inclusion of `AvailabilityMacros.h'.

	The native SDK on earliest Mac OS X (10.0-10.1) did not have
	`AvailabilityMacros.h'.  To prevent the inclusion of missing
	header file, ECANCELED (introduced in 10.2) in POSIX header
	file <errno.h> is checked to detect the system version.

	* include/freetype/config/ftconfig.h: Conditionalize the
	inclusion of `AvailabilityMacros.h'.
	* builds/unix/ftconfig.in: Ditto.
	* builds/vms/ftconfig.h: Ditto.

2011-05-27  Werner Lemberg  <<EMAIL>>

	[autofit] Improve tracing of hinting process.

	* src/autofit/aflatin.c (af_latin_hint_edges): Add tracing message
	`ADJUST'.

2011-05-26  Werner Lemberg  <<EMAIL>>

	[autofit] Fix trace message.

	* src/autofit/aflatin.c (af_latin_hint_edges): Show correct value in
	tracing message.

2011-05-24  Daniel Zimmermann  <<EMAIL>>

	Reduce warnings for MS Visual Studio 2010.

	* src/autofit/afhints.c (af_glyph_hints_get_num_segments,
	af_glyph_hints_get_segment_offset) [!FT_DEBUG_AUTOFIT]: Provide
	return value.
	* src/cff/cffgload.c (cff_slot_load): Add cast.
	* src/truetype/ttobjs.c (tt_check_trickyness_sfnt_ids): Use proper
	loop variable type.

2011-05-16  suzuki toshiya  <<EMAIL>>

	Automake component `builds/unix/install-sh' is removed.

	* builds/unix/install-sh: Removed.  It is not needed to
	include repository, because autogen.sh installs it.
	* builds/unix/.gitignore: Register install-sh.

2011-05-12  suzuki toshiya  <<EMAIL>>

	[autofit] Make trace message for CJK bluezone more verbose.

2011-05-08  Just Fill Bugs  <<EMAIL>>
            suzuki toshiya  <<EMAIL>>

	[autofit] Add bluezones for CJK Ideographs.

	To remove extrema of vertical strokes of CJK Ideographs at
	low resolution and make the top and bottom horizontal stems
	aligned, bluezones for CJK Ideographs are calculated from
	sample glyphs.  At present, vertical bluezones (bluezones
	to align vertical stems) are disabled by default.  For detail, see
	https://lists.gnu.org/archive/html/freetype-devel/2011-04/msg00070.html
	https://lists.gnu.org/archive/html/freetype-devel/2011-04/msg00092.html
	https://lists.gnu.org/archive/html/freetype-devel/2011-05/msg00001.html

	* include/freetype/internal/fttrace.h: New trace component `afcjk'.
	* src/autofit/afcjk.h (AF_CJK{Blue,Axis,Metric}Rec): Add CJK version
	for AF_Latin{Blue,Axis,Metric}Rec.
	(af_cjk_metrics_check_digits): Ditto, shared with Indic module.
	(af_cjk_metrics_init_widths): Ditto.
	(af_cjk_metrics_init): Take AF_CJKMetric instead of AF_LatinMetric.
	(af_cjk_metrics_scale): Ditto (declaration).
	(af_cjk_hints_init): Ditto (declaration).
	(af_cjk_hints_apply): Ditto (declaration).
	* src/autofit/afcjk.c (af_cjk_metrics_scale): Ditto (body).
	(af_cjk_hints_init): Ditto (body).
	(af_cjk_hints_apply): Ditto (body).
	(af_cjk_metrics_init_widths): Duplicate af_latin_metrics_init_widths.
	(af_cjk_metrics_check_digits): Duplicate af_latin_metrics_check_digits.
	(af_cjk_metrics_init): Call CJK bluezone initializer.
	(af_cjk_metrics_scale_dim): Add code to scale bluezones.
	(af_cjk_hints_compute_blue_edges): New function, CJK version of
	af_latin_hints_compute_blue_edges.
	(af_cjk_metrics_init_blues): New function, CJK version of
	af_latin_metrics_init_blues.
	(af_cjk_hint_edges): Add code to align the edge stems to blue zones.

	* src/autofit/afindic.c (af_indic_metrics_init): Take AF_CJKMetric
	instead of AF_LatinMetric, and initialize as af_cjk_metrics_init.
	However bluezones are not initialized.
	(af_indic_metrics_scale): Take AF_CJKMetric instead of AF_LatinMetric.
	(af_indic_hints_init): Ditto.
	(af_indic_hints_apply): Ditto.

	* docs/CHANGES: Note about CJK bluezone support.

2011-05-06  Werner Lemberg  <<EMAIL>>

	[autofit] Remove unused struct member.

	* src/autofit/aflatin.h (AF_LatinAxis): Remove `control_overshoot'.

2011-05-04  Werner Lemberg  <<EMAIL>>

	* src/autofit/aflatin.c (af_latin_metrics_scale_dim): Simplify.

2011-05-01  Just Fill Bugs  <<EMAIL>>
            Werner Lemberg  <<EMAIL>>

	[autofit] Add more debugging functions.

	* src/autofit/afhints.c (af_glyph_hints_get_num_segments,
	af_glyph_hints_get_segment_offset): New functions.

2011-05-01  suzuki toshiya  <<EMAIL>>

	Add new option `--disable-mmap' to configure script.

	* builds/unix/configure.raw: New option `--disable-mmap'
	is added.  It is for the developers to simulate the systems
	without mmap() (like 4.3BSD, minix etc) on POSIX systems.

2011-04-30  suzuki toshiya  <<EMAIL>>

	[truetype] Always recalculate the sfnt table checksum.

	* src/truetype/ttobjs.c (tt_get_sfnt_checksum): Recalculate
	the sfnt table checksum even if non-zero value is written in
	the TrueType font header.  Some bad PDF generators write
	wrong values.  For details see examples and benchmark tests
	of the latency by recalculation:
	https://lists.gnu.org/archive/html/freetype-devel/2011-04/msg00091.html
	https://lists.gnu.org/archive/html/freetype-devel/2011-04/msg00096.html

2011-04-30  suzuki toshiya  <<EMAIL>>

	[truetype] Register a set of tricky fonts, NEC FA family.

	* src/truetype/ttobjs.c (tt_check_trickyness_sfnt_ids):
	Add 8 checksum sets for NEC FA family.  For the tricky fonts
	without some tables (e.g. NEC FA fonts lack cvt table),
	extra check is added to assure that a zero-length table in the
	registry is not included in the font.

2011-04-29  suzuki toshiya  <<EMAIL>>

	[truetype] Fix a bug in the sfnt table checksum getter.

	* src/truetype/ttobjs.c (tt_get_sfnt_checksum): Check the
	return value of face->goto_table() correctly.

2011-04-28  Werner Lemberg  <<EMAIL>>

	[autofit] Improve tracing messages.

	* src/autofit/aflatin.c (af_latin_metrics_init_blues,
	af_latin_align_linked_edge, af_latin_hint_edges): Do it.

2011-04-25  Kan-Ru Chen  <<EMAIL>>

	[truetype] Always check the checksum to identify tricky fonts.

	Because some PDF generators mangle the family name badly,
	the trickyness check by the checksum should be invoked always.
	For sample PDF, see
	https://lists.gnu.org/archive/html/freetype-devel/2011-04/msg00073.html

	* src/truetype/ttobjs.c (tt_check_trickyness): Even when
	tt_check_trickyness_family() finds no trickyness,
	tt_check_trickyness_sfnt_ids() is invoked.

2011-04-22  suzuki toshiya  <<EMAIL>>

	[autofit] Add more Indic scripts with hanging baseline.

	* src/autofit/afindic.c (af_indic_uniranges): Tibetan, Limbu,
	Sundanese, Meetei Mayek, Syloti Nagri and Sharada scripts are
	added.

2011-04-21  Behdad Esfahbod  <<EMAIL>>

	Always ignore global advance.

	This makes FT_LOAD_IGNORE_GLOBAL_ADVANCE_WIDTH redundant,
	deprecated, and ignored.  The new behavior is what every major user
	of FreeType has been requesting.  Global advance is broken in many
	CJK fonts.  Just ignoring it by default makes most sense.

	* src/truetype/ttdriver.c (tt_get_advances),
	src/truetype/ttgload.c (TT_Get_HMetrics, TT_Get_VMetrics,
	tt_get_metrics, compute_glyph_metrics, TT_Load_Glyph),
	src/truetype/ttgload.h: Implement it.

	* docs/CHANGES: Updated.

2011-04-21  rainy6144  <<EMAIL>>

	[autofit] Blur CJK stems if too many to preserve their gaps.

	When there are too many stems to preserve their gaps in the
	rasterization of CJK Ideographs at a low resolution, blur the
	stems instead of showing clumped stems.  See
	https://lists.gnu.org/archive/html/freetype-devel/2011-02/msg00011.html
	https://lists.gnu.org/archive/html/freetype-devel/2011-04/msg00046.html
	for details.

	* src/autofit/afcjk.c (af_cjk_hint_edges): Store the position of
	the previous stem by `has_last_stem' and `last_stem_pos', and skip
	a stem if the current and previous stem are too near to preserve
	the gap.

2011-04-18  Werner Lemberg  <<EMAIL>>

	Integrate autofitter debugging stuff.

	* devel/ftoption.h, include/freetype/config/ftoption.h
	(FT_DEBUG_AUTOFIT): New macro.

	* include/freetype/internal/fttrace.h: Add trace components for
	autofitter.

	* src/autofit/aftypes.h (AF_LOG): Removed.
	(_af_debug): Removed.

	* src/autofit/*: s/AF_DEBUG/FT_DEBUG_AUTOFIT/.
	s/AF_LOG/FT_TRACE5/.
	Define FT_COMPONENT where necessary.

2011-04-18  Werner Lemberg  <<EMAIL>>

	Synchronize config files.

	* builds/unix/ftconfig.in: Copy missing assembler routines from
	include/freetype/config/ftconfig.h.

2011-04-13  Werner Lemberg  <<EMAIL>>

	[psaux] Fix Savannah bug #33047.

	Patch submitted by anonymous reporter.

	* src/psaux/psobjs.c (ps_table_add): Use FT_PtrDist for pointer
	difference.

2011-04-11  Kan-Ru Chen  <<EMAIL>>

	Fix reading of signed integers from files on 64bit platforms.

	Previously, signed integers were converted to unsigned integers, but
	this can fail because of sign extension.  For example, 0xa344a1eb
	becomes 0xffffffffa344a1eb.

	We now do the reverse which is always correct because the integer
	size is the same during the cast from unsigned to signed.

	* include/freetype/internal/ftstream.h, src/base/ftstream.c
	(FT_Stream_Get*): Replace with...
	(FT_Stream_GetU*): Functions which read unsigned integers.
	Update all macros accordingly.

	* src/gzip/ftgzip.c (ft_gzip_get_uncompressed_size): Updated.

2011-04-07  Werner Lemberg  <<EMAIL>>

	Update Unicode ranges for CJK autofitter; in particular, add Hangul.

	* src/autofit/afcjk.c (af_cjk_uniranges): Update to Unicode 6.0.

2011-04-04  Werner Lemberg  <<EMAIL>>

	Fix formatting of autofit debug dumps.

	* src/autofit/afhints.c (af_glyph_hints_dump_points,
	af_glyph_hints_dump_segments, af_glyph_hints_dump_edges): Adjust
	column widths.

2011-03-30  Werner Lemberg  <<EMAIL>>

	* src/autofit/aftypes.h (AF_OutlineRec): Removed, unused.

2011-03-24  Werner Lemberg  <<EMAIL>>

	* src/cff/cfftypes.h (CFF_MAX_CID_FONTS): Increase to 256.
	This limit is given on p. 37 of Adobe Technical Note #5014.

2011-03-23  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttpload.c (tt_face_load_loca): Fix mismatch warning.

2011-03-20  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfobjs.c (sfnt_open_font): Check number of TTC subfonts.

2011-03-19  Werner Lemberg  <<EMAIL>>

	More C++ compilation fixes.

	* src/autofit/afhints.c (af_glyph_hints_dump_points,
	af_glyph_hints_dump_segments, af_glyph_hints_dump_edges)
	[__cplusplus]: Protect with `extern "C"'.

2011-03-18  Werner Lemberg  <<EMAIL>>

	C++ compilation fixes.

	* src/autofit/aflatin.c (af_latin_hints_apply), src/autofit/afcjk.c
	(af_cjk_hints_apply): Use cast for `dim'.

2011-03-17  Alexei Podtelezhnikov  <<EMAIL>>

	A better fix for Savannah bug #32671.

	* src/smooth/ftgrays.c (gray_render_conic): Clean up code and
	replace WHILE loop with a more natural DO-WHILE construct.

2011-03-16  Werner Lemberg  <<EMAIL>>.

	* src/base/ftstroke.c (FT_StrokerRec): Remove unused `valid' field.
	Suggested by Graham Asher.

2011-03-09  Werner Lemberg  <<EMAIL>>

	Make FT_Sfnt_Table_Info return the number of SFNT tables.

	* src/sfnt/sfdriver.c (sfnt_table_info): Implement it.
	* include/freetype/tttables.h: Update documentation.
	* docs/CHANGES: Updated.

2011-03-07  Bram Tassyns  <<EMAIL>>

	[cff] Fix Savannah bug #27988.

	* src/cff/cffobjs.c (remove_style): New function.
	(cff_face_init): Use it to strip off the style part of the family
	name.

2011-03-07  Werner Lemberg  <<EMAIL>>

	* docs/CHANGES: Updated.

2011-03-07  Alexei Podtelezhnikov  <<EMAIL>>

	Quick fix for Savannah bug #32671.

	This isn't the optimal solution yet, but it restores the previous
	rendering quality (more or less).

	* src/smooth/ftgrays.c (gray_render_conic): Do more splitting.

2011-03-06  Werner Lemberg  <<EMAIL>>

	Fix autohinting fallback.

	* src/base/ftobjs.c (FT_Load_Glyph): Assure that we only check TTFs,
	ignoring CFF-based OTFs.

2011-02-27  Werner Lemberg  <<EMAIL>>

	Add AF_CONFIG_OPTION_USE_WARPER to control the autofit warper.

	* devel/ftoption.h, include/freetype/config/ftoption.h
	(AF_CONFIG_OPTION_USE_WARPER): New macro.
	* src/autofit/aftypes.h (AF_USE_WARPER): Remove.

	* src/autofit/*: s/AF_USE_WARPER/AF_CONFIG_OPTION_USE_WARPER/.

	* src/autofit/afwarp.c [!AF_CONFIG_OPTION_USE_WARPER]: Replace dummy
	variable assignment with a typedef.

2011-02-26  Werner Lemberg  <<EMAIL>>

	[autofit] Slight simplifications.

	* src/autofit/aflatin.c (af_latin_hints_link_segments): Remove
	test which always returns false.
	(af_latin_hints_compute_blue_edges): Remove redundant assignment.

2011-02-24  Werner Lemberg  <<EMAIL>>

	* docs/PROBLEMS: Mention rendering differences on different
	platforms.
	Suggested and worded by Jason Owen <<EMAIL>>.

2011-02-24  Werner Lemberg  <<EMAIL>>

	[autofit] Comment out unused code.

	* src/autofit/aflatin.c, src/autofit/aflatin2.c
	(af_latin_hints_compute_edges): Do it.

2011-02-24  Werner Lemberg  <<EMAIL>>

	* src/autofit/afhints.h (AF_GlyphHints): Remove unused field.

2011-02-20  suzuki toshiya  <<EMAIL>>

	[cache] Fix an off-by-one bug in `FTC_Manager_RemoveFaceID'.
	Found by <<EMAIL>>, see detail in

	  https://lists.gnu.org/archive/html/freetype/2011-01/msg00023.html

	* src/cache/ftccache.c (FTC_Cache_RemoveFaceID): Check the node
	buckets[cache->p + cache->mask] too.

2011-02-19  Kevin Kofler  <<EMAIL>>

	Fall back to autohinting if a TTF/OTF doesn't contain any bytecode.
	This is Savannah patch #7471.

	* src/base/ftobjs.c (FT_Load_Glyph): Implement it.

2011-02-19  John Tytgat  <<EMAIL>>

	[cff] Fix subset prefix removal.
	This is Savannah patch #7465.

	* src/cff/cffobjs.c (remove_subset_prefix): Update length after
	subset prefix removal.

2011-02-13  Bradley Grainger  <<EMAIL>>

	Add inline assembly version of FT_MulFix for MSVC.

	* include/freetype/config/ftconfig.h: Ported the FT_MulFix_i386
	function from GNU inline assembly syntax (see #ifdef __GNUC__ block
	above) to MASM syntax for Microsoft Visual C++.

2011-02-13  Bradley Grainger  <<EMAIL>>

	Add project and solution files in Visual Studio 2010 format.

	* builds/win32/.gitignore: Ignore user-specific cache files.
	* builds/win32/vc2010/: Add VS2010 project & solution files, created
	by upgrading builds/win32/vc2008/freetype.vcproj.
	* objs/.gitignore: Ignore Visual Studio output files.

2011-02-01  Werner Lemberg  <<EMAIL>>

	* src/autofit/afdummy.c: Include `aferrors.h'.
	Problem reported by Chris Liddell <<EMAIL>>.

2011-02-01  Werner Lemberg  <<EMAIL>>

	[cff] Ignore unknown operators in charstrings.
	Patch suggested by Miles.Lau <<EMAIL>>.

	* src/cff/cffgload.c (cff_decoder_parse_charstrings): Emit tracing
	message for unknown operators and continue instead of exiting with a
	syntax error.

2011-02-01  Werner Lemberg  <<EMAIL>>

	[truetype] FT_LOAD_PEDANTIC now affects `prep' and `fpgm' also.

	* src/truetype/ttgload.c (tt_loader_init): Handle
	`FT_LOAD_PEDANTIC'.
	* src/truetype/ttobjs.c (tt_size_run_fpgm, tt_size_run_prep,
	tt_size_init_bytecode, tt_size_ready_bytecode): New argument to
	handle pedantic mode.
	* src/truetype/ttobjs.h: Updated.

2011-01-31  Werner Lemberg  <<EMAIL>>

	[truetype] Protect jump instructions against endless loops.

	* src/truetype/interp.c (DO_JROT, DO_JMPR, DO_JROF): Exit with error
	if offset is zero.

2011-01-31  Werner Lemberg  <<EMAIL>>

	[truetype] Improve handling of invalid references.

	* src/truetype/interp.c: Set even more TT_Err_Invalid_Reference
	error codes only if pedantic hinting is active.  At the same time,
	try to provide sane values which hopefully allow useful
	continuation.  Exception to this is CALL and LOOPCALL – due to
	possible stack corruption it is necessary to bail out.

2011-01-31  Werner Lemberg  <<EMAIL>>

	[truetype] Improve handling of stack underflow.

	* src/truetype/ttinterp.c (TT_RunIns, Ins_FLIPPT, Ins_DELTAP,
	Ins_DELTAC): Exit with error only if `pedantic_hinting' is set.
	Otherwise, try to do something sane.

2011-01-30  Werner Lemberg  <<EMAIL>>

	* src/sfnt/ttmtx.c (tt_face_load_hmtx): Fix tracing message.

2011-01-30  LIU Sun-Liang  <<EMAIL>>

	[truetype]: Fix behaviour of MIAP for invalid arguments.

	* src/truetype/ttinterp.c (Ins_MIAP): Set reference points even in
	case of error.

2011-01-18  Werner Lemberg  <<EMAIL>>

	[truetype] Fix handling of MIRP instruction.

	Thanks to Greg Hitchcock who explained the issue.

	* src/truetype/ttinterp.c (Ins_MIRP): Replace a `>=' operator with
	`>' since the description in the specification is incorrect.
	This fixes, for example, glyph `two' in font `Helvetica Neue LT Com
	65 medium' at 15ppem.

2011-01-15  suzuki toshiya  <<EMAIL>>

	Fix ARM assembly code in include/freetype/config/ftconfig.h.

	* include/freetype/config/ftconfig.h (FT_MulFix_arm):
	Copy the maintained code from builds/unix/ftconfig.in.
	Old GNU binutils could not accept the reduced syntax
	`orr %0, %2, lsl #16'.  Un-omitted syntax like RVCT,
	`orr %0, %0, %2, lsl #16' is better.  Reported by
	Johnson Y. Yan.  The bug report by Qt developers is
	considered too.

	https://bugreports.qt.io/browse/QTBUG-6521

2011-01-15  Werner Lemberg  <<EMAIL>>

	[raster] Make bbox handling the same as with Microsoft's rasterizer.

	Right before B/W rasterizing, the bbox gets simply rounded to
	integers.  This fixes, for example, glyph `three' in font `Helvetica
	Neue LT Com 65 Medium' at 11ppem.

	Thanks to Greg Hitchcock who explained this behaviour.

	* src/raster/ftrend1.c (ft_raster1_render): Implement it.

2011-01-15  suzuki toshiya  <<EMAIL>>

	Copy -mcpu=* & -march=* options from CFLAGS to LDFLAGS.

	* builds/unix/configure.raw: Consider recent gcc-standard
	flags to specify architecture in CFLAGS & LDFLAGS
	harmonization.  Requested by Savannah bug #32114, to
	support multilib feature of BuildRoot SDK correctly.

2011-01-15  suzuki toshiya  <<EMAIL>>

	Fix off-by-one bug in CFLAGS & LDFLAGS harmonizer.

	* builds/unix/configure.raw: Some important options that
	included in CFLAGS but not in LDFLAGS are copied to
	LDFLAGS, but the last option in CFLAGS was not checked.

2011-01-13  Werner Lemberg  <<EMAIL>>

	[raster] Add undocumented drop-out rule to the other bbox side also.

	* src/raster/ftraster.c (Vertical_Sweep_Drop,
	Horizontal_Sweep_Drop): Implement it.

2011-01-13  Werner Lemberg  <<EMAIL>>

	[raster] Reduce jitter value.

	This catches a rendering problem with glyph `x' from Tahoma at
	10ppem.  It seems that the increase of the precision in the change
	from 2009-06-11 makes a larger jitter value unnecessary.

	* src/raster/ftraster.c (Set_High_Precision): Implement it.

2011-01-13  Werner Lemberg  <<EMAIL>>

	[raster] Handle drop-outs at glyph borders according to Microsoft.

	If a drop-out rule would switch on a pixel outside of the glyph's
	bounding box, use the right (or top) pixel instead.  This is an
	undocumented feature, but some fonts like `Helvetica Neue LT Com 65
	Medium' heavily rely on it.

	Thanks to Greg Hitchcock who explained this behaviour.

	* src/raster/ftraster.c (Vertical_Sweep_Drop,
	Horizontal_Sweep_Drop): Implement it.

2011-01-09  suzuki toshiya  <<EMAIL>>

	[cache] Fix Savannah bug #31923, patch drafted by Harsha.

	When a node comparator changes the cached nodes during the
	search of a node matching with queried properties, the
	pointers obtained before the function should be updated to
	prevent the dereference to freed or reallocated nodes.
	To minimize the rescan of the linked list, the update is
	executed when the comparator notifies the change of cached
	nodes. This change depends previous change:
	38b272ffbbdaae276d636aec4ef84af407d16181

	* src/cache/ftccache.h (FTC_CACHE_LOOKUP_CMP): Rescan the
	top node if the cached nodes are changed.
	* src/cache/ftccache.c (FTC_Cache_Lookup): Ditto.

2011-01-09  suzuki toshiya  <<EMAIL>>

	[cache] Notice if a cache query induced the node list change.

	Some node comparators (comparing the cache node contents and the
	properties specified by the query) can flush the cache node to
	prevent the cache inflation.  The change may invalidate the pointers
	to the node obtained before the node comparison, so it should be
	noticed to the caller.  The problem caused by the cache node
	changing is reported by Harsha, see Savannah bug #31923.

	* src/cache/ftccache.h (FTC_Node_CompareFunc): Add new argument
	`FT_Bool* list_changed' to indicate the change of the cached nodes
	to the caller.
	(FTC_CACHE_LOOKUP_CMP): Watch the change of the cached nodes by
	`_list_changed'.
	(FTC_CACHE_TRYLOOP_END): Take new macro argument `_list_changed'
	and update it when `FTC_Manager_FlushN' flushes any nodes.

	* src/cache/ftccback.h (ftc_snode_compare): Updated to fit with new
	FTC_Node_CompareFunc type.
	(ftc_gnode_compare): Ditto.

	* src/cache/ftcbasic.c: Include FT_INTERNAL_OBJECTS_H to use
	TRUE/FALSE macros.
	(ftc_basic_gnode_compare_faceid): New argument `FT_Bool*
	list_changed' to indicate the change of the cache nodes (anyway, it
	is always FALSE).

	* src/cache/ftccmap.c: Include FT_INTERNAL_OBJECTS_H to use
	TRUE/FALSE macros.
	(ftc_cmap_node_compare): New argument `FT_Bool* list_changed' to
	indicate the change of the cache nodes (anyway, it is always FALSE).
	(ftc_cmap_node_remove_faceid): Ditto.

	* src/cache/ftccache.c (FTC_Cache_NewNode): Pass a NULL pointer to
	`FTC_CACHE_TRYLOOP_END', because the result is not needed.
	(FTC_Cache_Lookup): Watch the change of the cache nodes by
	`list_changed'.
	(FTC_Cache_RemoveFaceID): Ditto.

	* src/cache/ftcglyph.c: Include FT_INTERNAL_OBJECTS_H to use
	TRUE/FALSE macros.
	(ftc_gnode_compare): New argument `FT_Bool* list_changed' to
	indicate the change of the cache nodes (anyway, it is always FALSE).
	(FTC_GNode_Compare): New argument `FT_Bool* list_changed' to be
	passed to `ftc_gnode_compare'.
	* src/cache/ftcglyph.h (FTC_GNode_Compare): Ditto.

	* src/cache/ftcsbits.c (ftc_snode_compare): New argument `FT_Bool*
	list_changed' to indicate the change of the cache nodes, anyway.  It
	is updated by `FTC_CACHE_TRYLOOP'.
	(FTC_SNode_Compare): New argument `FT_Bool* list_changed' to be
	passed to `ftc_snode_compare'.
	* src/cache/ftcsbits.h (FTC_SNode_Compare): Ditto.

2011-01-09  suzuki toshiya  <<EMAIL>>

	[cache] Fit `FTC_GNode_Compare' to `FTC_Node_CompareFunc'.

	* src/cache/ftcglyph.h (FTC_GNode_Compare): Add the 3rd
	argument `FTC_Cache  cache' to fit FTC_Node_CompareFunc
	prototype.
	* src/cache/ftcglyph.c (FTC_GNode_Compare): Ditto. Anyway,
	`cache' is not used by its child `ftc_gnode_compare'.

2011-01-09  suzuki toshiya  <<EMAIL>>

	[cache] Deduplicate the code to get the top node by a hash.

	There are several duplicated code fragments getting the top node
	from a cache by a given hash, like:

	    idx = hash & cache->mask;
	    if ( idx < cache->p )
	      idx = hash & ( cache->mask * 2 + 1 );
	    pnode = cache->buckets + idx;

	To remove duplication, a cpp-macro to do same work
	`FTC_NODE__TOP_FOR_HASH' is introduced.  For non-inlined
	configuration, non-`ftc_get_top_node_for_hash' is also introduced.

	* src/cache/ftccache.h (FTC_NODE__TOP_FOR_HASH): Declare
	and implement inlined version.
	(FTC_CACHE_LOOKUP_CMP): Use `FTC_NODE__TOP_FOR_HASH'.
	* src/cache/ftccache.c (ftc_get_top_node_for_hash): Non-inlined
	version.
	(ftc_node_hash_unlink): Use `FTC_NODE__TOP_FOR_HASH'.
	(ftc_node_hash_link): Ditto.
	(FTC_Cache_Lookup): Ditto.

2011-01-09  suzuki toshiya  <<EMAIL>>

	[cache] inline-specific functions are conditionalized.

	* src/cache/ftcglyph.c (FTC_GNode_Compare): Conditionalized for
	inlined configuration.  This function is a thin wrapper of
	`ftc_gnode_compare' for inlined `FTC_CACHE_LOOKUP_CMP' (see
	`nodecmp' argument).  Under non-inlined configuration,
	`ftc_gnode_compare' is invoked by `FTC_Cache_Lookup', via
	`FTC_Cache->clazz.node_compare'.

	* src/cache/ftcglyph.h (FTC_GNode_Compare): Ditto.
	* src/cache/ftcsbits.c (FTC_SNode_Compare): Ditto, for
	`ftc_snode_compare'.
	* src/cache/ftcsbits.h (FTC_SNode_Compare): Ditto.

2011-01-09  suzuki toshiya  <<EMAIL>>

	[cache] Correct a type mismatch under non-inlined config.

	* src/cache/ftcglyph.h (FTC_GCACHE_LOOKUP_CMP): `FTC_GCache_Lookup'
	takes the node via a pointer `FTC_Node*', differently from cpp-macro
	`FTC_CACHE_LOOKUP_CMP'.

2011-01-06  suzuki toshiya  <<EMAIL>>

	Update Jamfile to include Bzip2 support.

	* Jamfile: Include src/bzip2 to project.
	Comments for lzw, gzip, bzip2 are changed to clarify that
	they are for compressed PCF fonts, not others.
	(e.g. compressed BDF fonts are not supported yet)

2011-01-05  suzuki toshiya  <<EMAIL>>

	Update Symbian project files to include Bzip2 support.

	Currently, it provides `FT_Stream_OpenBzip2' that returns
	unimplemented error always, to prevent unresolved symbol
	error for the applications designed for Unix systems.

	* builds/symbian/bld.inf: Include ftbzip2.h.
	* builds/symbian/freetype.mmp: Include ftbzip2.c.

2011-01-05  suzuki toshiya  <<EMAIL>>

	Update classic MacOS makefiles to include Bzip2 support.

	Currently, it provides `FT_Stream_OpenBzip2' that returns
	unimplemented error always, to prevent unresolved symbol
	error for the applications designed for Unix systems.

	* builds/mac/FreeType.m68k_cfm.make.txt: Include ftbzip2.c.o.
	* builds/mac/FreeType.m68k_far.make.txt: Ditto.
	* builds/mac/FreeType.ppc_carbon.make.txt: Include ftbzip2.c.x.
	* builds/mac/FreeType.ppc_classic.make.txt: Ditto.

2011-01-05  suzuki toshiya  <<EMAIL>>

	Update Amiga makefiles to include Bzip2 support.

	Currently, it provides `FT_Stream_OpenBzip2' that returns
	unimplemented error always, to prevent unresolved symbol
	error for the applications designed for Unix systems.

	* builds/amiga/makefile: Include bzip2.ppc.o built from ftbzip2.c.
	* builds/amiga/makefile.os4: Include bzip2.o built from ftbzip2.c.
	* builds/amiga/smakefile: Ditto.

2011-01-05  suzuki toshiya  <<EMAIL>>

	Update pkg-config tools to reflect Bzip2 support.

	* builds/unix/freetype-config.in: Include `-lbz2' to
	--libs output, if built with Bzip2 support.
	* builds/unix/freetype2.in: Ditto.

2011-01-05  suzuki toshiya  <<EMAIL>>

	* builds/unix/configure.raw: Remove `SYSTEM_BZ2LIB' macro.

	SYSTEM_ZLIB is used to switch the builtin zlib source
	or system zlib source out of FreeType2.  But ftbzip2
	module has no builtin bzip2 library and always requires
	system bzip2 library.  Thus SYSTEM_BZ2LIB is always yes,
	it is not used.

2011-01-03  Werner Lemberg  <<EMAIL>>

	*/rules.mk: Handle `*pic.c' files.

2010-12-31  Werner Lemberg  <<EMAIL>>

	* src/cff/cfftypes.h (CFF_MAX_CID_FONTS): Increase to 64.
	Problem reported by Tom Bishop <<EMAIL>>.

2010-12-31  Werner Lemberg  <<EMAIL>>

	Improve bzip2 support.

	* include/freetype/ftmoderr.h: Add bzip2.

	* docs/INSTALL.ANY, docs/CHANGES: Updated.

	* src/pcf/README: Updated.
	* include/freetype/internal/pcftypes.h: Obsolete, removed.

2010-12-31  Joel Klinghed  <<EMAIL>>

	Add bzip2 compression support to handle *.pcf.bz2 files.

	* builds/unix/configure.raw: Test for libbz2 library.

	* devel/ftoption.h, include/freetype/config/ftoption.h
	(FT_CONFIG_OPTION_USE_BZIP2): Define.
	* include/freetype/config/ftheader.h (FT_BZIP2_H): Define.

	* include/freetype/ftbzip2.h: New file.

	* src/bzip2/*: New files.

	* src/pcf/pcf.h: s/gzip_/comp_/.
	* src/pcf/pcfdrivr.c: Include FT_BZIP2_H.
	s/gzip_/comp_/.
	(PCF_Face_Init): Handle bzip2 compressed files.

	* docs/formats.txt, modules.cfg: Updated.

2010-12-25  Harsha  <<EMAIL>>

	Apply Savannah patch #7422.

	If we encounter a space in a string then the sbit buffer is NULL,
	height and width are 0s.  So the check in ftc_snode_compare will
	always pass for spaces (comparison with 255).  Here the comments
	above the condition are proper but the implementation is not.  When
	we create an snode I think it is the proper way to initialize the
	width to 255 and then put a check for being equal to 255 in snode
	compare function.

	* src/cache/ftcsbits.c (FTC_SNode_New): Initialize sbit widths with
	value 255.
	(ftc_snode_compare): Fix condition.

2010-12-13  Werner Lemberg  <<EMAIL>>

	Fix parameter handling of `FT_Set_Renderer'.
	Reported by Kirill Tishin <<EMAIL>>.

	* src/base/ftobjs.c (FT_Set_Renderer): Increment `parameters'.

2010-12-09  Werner Lemberg  <<EMAIL>>

	[cff] Allow `hlineto' and `vlineto' without arguments.

	We simply ignore such instructions.  This is invalid, but it doesn't
	harm; and indeed, there exist such subsetted fonts in PDFs.

	Reported by Albert Astals Cid <<EMAIL>>.

	* src/cff/cffgload.c (cff_decoder_parse_charstrings)
	[cff_op_hlineto]: Ignore instruction if there aren't any arguments
	on the stack.

2010-11-28  Werner Lemberg  <<EMAIL>>

	* Version 2.4.4 released.
	=========================


	Tag sources with `VER-2-4-4'.

	* docs/CHANGES: Updated.

	* docs/VERSION.DLL: Update documentation and bump version number to
	2.4.4

	* README, Jamfile (RefDoc),
	builds/win32/vc2005/freetype.vcproj, builds/win32/vc2005/index.html,
	builds/win32/vc2008/freetype.vcproj, builds/win32/vc2008/index.html,
	builds/win32/visualc/freetype.dsp,
	builds/win32/visualc/freetype.vcproj,
	builds/win32/visualc/index.html, builds/win32/visualce/freetype.dsp,
	builds/win32/visualce/freetype.vcproj,
	builds/win32/visualce/index.html,
	builds/wince/vc2005-ce/freetype.vcproj,
	builds/wince/vc2005-ce/index.html,
	builds/wince/vc2008-ce/freetype.vcproj,
	builds/wince/vc2008-ce/index.html: s/2.4.3/2.4.4/, s/243/244/.

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 4.

	* builds/unix/configure.raw (version_info): Set to 12:2:6.

2010-11-28  Alexei Podtelezhnikov  <<EMAIL>>

	[ftsmooth]: Minor code simplification.

	* src/smooth/ftgrays (gray_render_cubic): Do only one comparison
	instead of two.

2010-11-26  Johnson Y. Yan  <<EMAIL>>

	[truetype] Better multi-threading support.

	* src/truetype/ttinterp.c (TT_Load_Context): Reset glyph zone
	references.

2010-11-23  John Tytgat  <<EMAIL>>

	* src/psaux/t1decode.c (t1_decoder_parse_charstring): Expand
	start_point, check_points, add_point, add_point1, close_contour
	macros.
	Remove add_contour macro.
	Return error code from t1_builder_start_point and
	t1_builder_check_points when there was one (instead of returning 0).

2010-11-22  suzuki toshiya  <<EMAIL>>

	[truetype] Identify the tricky fonts by cvt/fpgm/prep checksums.
	Some Latin TrueType fonts are still expected to be unhinted.
	Fix Savannah bug #31645.

	* src/truetype/ttobjs.c (tt_check_trickyness): Divided to...
	(tt_check_trickyness_family): this checking family name, and
	(tt_check_trickyness_sfnt_ids): this checking cvt/fpgm/prep.
	(tt_get_sfnt_checksum): Function to retrieve the sfnt checksum
	for specified subtable even if cleared by lazy PDF generators.
	(tt_synth_sfnt_checksum): Function to calculate the checksum.

2010-11-18  Werner Lemberg  <<EMAIL>>

	[truetype] Fix `loca' handling for inconsistent number of glyphs.
	Reported by Johnson Y. Yan <<EMAIL>>.

	* src/truetype/ttpload.c (tt_face_load_loca): While sanitizing,
	handle case where `loca' is the last table in the font.

2010-11-18  Werner Lemberg  <<EMAIL>>

	[sfnt] Ignore all errors while loading `OS/2' table.
	Suggested by Johnson Y. Yan <<EMAIL>>.

	* src/sfnt/sfobjs.c (sfnt_load_face): Do it.

2010-11-18  Johnson Y. Yan  <<EMAIL>>

	[type1] Fix matrix normalization.

	* src/type1/t1load.c (parse_font_matrix): Handle sign of scaling
	factor.

2010-11-18  Werner Lemberg  <<EMAIL>>

	[type1] Improve guard against malformed data.
	Based on a patch submitted by Johnson Y. Yan
	<<EMAIL>>

	* src/type1/t1load.c (read_binary_data): Check `size'.

2010-11-17  Werner Lemberg  <<EMAIL>>

	[sfnt] While tracing, output table checksums also.

	* src/sfnt/ttload.c (tt_face_load_font_dir): Do it.

2010-11-04  suzuki toshiya  <<EMAIL>>

	[UVS] Fix `find_variant_selector_charmap', Savannah bug #31545.

	Since 2010-07-04, `find_variant_selector_charmap' returns
	the first cmap subtable always under rogue-compatible
	configuration, it causes NULL pointer dereference and
	make UVS-related functions crashed.

	* src/base/ftobjs.c (Fix find_variant_selector_charmap):
	Returns UVS cmap correctly.

2010-11-01  Alexei Podtelezhnikov  <<EMAIL>>

	[ftsmooth] Improve rendering.

	* src/smooth/ftsmooth.c (gray_render_conic): Since version 2.4.3,
	cubic deviations have been estimated _after_ UPSCALE, whereas
	conic ones have been evaluated _before_ UPSCALE, which produces
	inferior rendering results.  Fix this.
	Partially undo change from 2010-10-15 by using ONE_PIXEL/4; this has
	been tested with demo images sent to the mailing list.  See

	  https://lists.gnu.org/archive/html/freetype-devel/2010-10/msg00055.html

	and later mails in this thread.

2010-10-28  Werner Lemberg  <<EMAIL>>

	[ftraster] Minor fixes.

	Reported by Tom Bishop <<EMAIL>>.

	* src/raster/ftraster.c (ULong): Remove unused typedef.
	(TWorker): Remove unused variable `precision_mask'.

2010-10-28  Werner Lemberg  <<EMAIL>>

	[ftraster] Fix rendering.

	Problem reported by Tom Bishop <<EMAIL>>; see
	thread starting with

	  https://lists.gnu.org/archive/html/freetype/2010-10/msg00049.html

	* src/raster/ftraster.c (Line_Up): Replace FMulDiv with SMulDiv
	since the involved multiplication exceeds 32 bits.

2010-10-25  suzuki toshiya  <<EMAIL>>

	Revert a change of `_idx' type in `FTC_CACHE_LOOKUP_CMP'.

	* src/cache/ftccache.h (FTC_CACHE_LOOKUP_CMP): Revert
	the type of `_idx' from FT_PtrDist (by previous change)
	to original FT_UFast, to match with FT_CacheRec.

2010-10-24  suzuki toshiya  <<EMAIL>>

	[cache] Change the hash types to FT_PtrDist.

	On LLP64 platforms (e.g. Win64), FT_ULong (32-bit)
	variables are inappropriate to calculate hash values
	from the memory address (64-bit).  The hash variables
	are extended from FT_ULong to FT_PtrDist and new
	hashing macro functions are introduced.  The hash
	values on 16-bit memory platforms are changed, but
	ILP32 and LP64 are not changed.  The hash value in
	the cache subsystem is not reverted to the memory
	address, so using signed type FT_PtrDist is safe.

	* src/cache/ftccache.h (_FTC_FACE_ID_HASH): New hash
	function to replace `FTC_FACE_ID_HASH' for portability.
	* src/cache/ftcmanag.h (FTC_SCALER_HASH): Replace
	`FTC_FACE_ID_HASH' by `_FTC_FACE_ID_HASH'.
	* src/cache/ftccmap.c (FTC_CMAP_HASH): Ditto.

	* src/cache/ftccache.h (FTC_NodeRec): The type of the
	member `hash' is changed from FT_UInt32 to FT_PtrDist.

	* src/cache/ftccache.h (FTC_Cache_Lookup): The type of the
	argument `hash' is changed from FT_UInt32 to FT_PtrDist.
	(FTC_Cache_NewNode): Ditto.
	* src/cache/ftccache.c (ftc_cache_add): Ditto.
	(FTC_Cache_Lookup): Ditto.  (FTC_Cache_NewNode): Ditto.
	* src/cache/ftcglyph.h (FTC_GCache_Lookup): Ditto.
	* src/cache/ftcglyph.c (FTC_GCache_Lookup): Ditto.

	* src/cache/ftcbasic.c (FTC_ImageCache_Lookup): The type
	of the internal variable `hash' is changed to FT_PtrDist
	from FT_UInt32.  (FTC_ImageCache_LookupScaler): Ditto.
	(FTC_SBitCache_Lookup): Ditto.
	(FTC_SBitCache_LookupScaler): Ditto.
	* src/cache/ftccmap.c (FTC_CMapCache_Lookup): Ditto.
	* src/cache/ftccache.h (FTC_CACHE_LOOKUP_CMP): Ditto.
	Also the type of the internal variable `_idx' is changed to
	FT_PtrDist from FT_UFast for better pointer calculation.

2010-10-24  suzuki toshiya  <<EMAIL>>

	[cache] Hide internal macros incompatible with LLP64.

	`FT_POINTER_TO_ULONG', `FTC_FACE_ID_HASH', and
	`FTC_IMAGE_TYPE_HASH' are enclosed by
	FT_CONFIG_OPTION_OLD_INTERNALS and hidden from
	normal clients.

	For the history of these macros, see the investigation:
	https://lists.gnu.org/archive/html/freetype/2010-10/msg00022.html

2010-10-24  suzuki toshiya  <<EMAIL>>

	Change the type of `FT_MEM_VAL' from FT_ULong to FT_PtrDist.

	On LLP64 platforms (e.g. Win64), unsigned long (32-bit)
	cannot cover the memory address (64-bit).  `FT_MEM_VAL' is
	used for hashing only and not dereferred, so using signed
	type FT_PtrDist is safe.

	* src/base/ftdbgmem.c (FT_MEM_VAL): Change the type of the
	return value from FT_ULong to FT_PtrDist.
	(ft_mem_table_resize): The type of hash is changed to
	FT_PtrDist.  (ft_mem_table_get_nodep): Ditto.

2010-10-24  suzuki toshiya  <<EMAIL>>

	Replace "%lx" for memory address by "%p", LLP64 platforms.

	On LLP64 platforms (e.g. Win64), long (32-bit) cannot cover
	the memory address (64-bit).  Also the casts from the pointer
	type to long int should be removed to preserve the address
	correctly.

	* src/raster/ftraster.c (New_Profile): Replace "%lx" by "%p".
	(End_Profile) Ditto.
	* src/truetype/ttinterp.c (Init_Context): Ditto.

2010-10-15  Alexei Podtelezhnikov  <<EMAIL>>

	Fix thinko in spline flattening.

	FT_MAX_CURVE_DEVIATION is dependent on the value of ONE_PIXEL.

	* src/smooth/ftgrays.c (FT_MAX_CURVE_DEVIATION): Remove it and
	replace it everywhere with ONE_PIXEL/8.

2010-10-13  suzuki toshiya  <<EMAIL>>

	[raccess] Skip unrequired resource access rules by Darwin VFS.

	When a resource fork access rule by Darwin VFS could open the
	resource fork but no font is found in it, the rest of rules
	by Darwin VFS are skipped.  It reduces the warnings of the
	deprecated resource fork access method by recent Darwin kernel.
	Fix MacPorts ticket #18859:
		https://trac.macports.org/ticket/18859

	* src/base/ftobjs.c (load_face_in_embedded_rfork):
	When `FT_Stream_New' returns FT_Err_Cannot_Open_Stream, it
	means that the file is possible to be `fopen'-ed but zero-sized.
	Also there is a case that the resource fork is not zero-sized,
	but no supported font exists in it.  If a rule by Darwin VFS
	falls into such cases, there is no need to try other Darwin VFS
	rules anymore.  Such cases are marked by vfs_rfork_has_no_font.
	If it is TRUE, the Darwin VFS rules are skipped.

2010-10-13  suzuki toshiya  <<EMAIL>>

	[raccess] Grouping resource access rules based on Darwin VFS.

	MacOS X/Darwin kernel supports a few tricky methods to access
	a resource fork via ANSI C or POSIX interface.  Current resource
	fork accessor tries all possible methods to support all kernels.
	But if a method could open a resource fork but no font is found,
	there is no need to try other methods older than tested method.
	To determine whether the rule index is for Darwin VFS, a local
	function `ftrfork.c::raccess_rule_by_darwin_vfs' is introduced.
	To use this function in ftobjs.c etc but it should be inlined,
	it is exposed by ftbase.h.

	* src/base/ftrfork.c (FT_RFork_Rule): New enum type to identify
	the rules to access the resource fork.
	(raccess_guess_rec): New structure to bind the rule function and
	rule enum type.
	(FT_Raccess_Guess): The list of the rule functions is replaced by
	(raccess_guess_table): This.  This is exposed to be used by other
	intra module functions.
	(raccess_rule_by_darwin_vfs): A function to return a boolean
	if the rule specified by the rule index is based on Darwin VFS.

2010-10-13  suzuki toshiya  <<EMAIL>>

	Prevent to open a FT_Stream for zero-sized file on non-Unix.

	builds/unix/ftsystem.c prevents to open an useless stream from
	zero-sized file and returns FT_Err_Cannot_Open_Stream, but the
	stream drivers for ANSI C, Amiga and VMS return useless streams.
	For cross-platform consistency, all stream drivers should act
	same.

	* src/base/ftsystem.c (FT_Stream_Open): If the size of the opened
	file is zero, FT_Err_Cannot_Open_Stream is returned.
	* builds/amiga/src/base/ftsystem.c (FT_Stream_Open): Ditto.
	* src/vms/ftsystem.c (FT_Stream_Open): Ditto.

2010-10-12  Werner Lemberg  <<EMAIL>>

	[truetype] Fix Savannah bug #31310.

	* src/truetype/ttgxvar.c (ft_var_readpackedpoints): Protect against
	invalid `runcnt' values.

2010-10-08  Chris Liddell  <<EMAIL>>

	[sfnt] Fix Savannah bug #31275.

	* src/sfnt/ttpost.c: Include FT_INTERNAL_DEBUG_H.

2010-10-06  Werner Lemberg  <<EMAIL>>

	[truetype] Improve error handling of `SHZ' bytecode instruction.
	Problem reported by Chris Evans <<EMAIL>>.

	* src/truetype/ttinterp.c (Ins_SHZ): Check `last_point'.

2010-10-05  Werner Lemberg  <<EMAIL>>

	Fix Savannah bug #31253.
	Patch submitted by an anonymous reporter.

	* configure: Use `awk' instead of `sed' to manipulate output of `ls
	-id'.

2010-10-03  Werner Lemberg  <<EMAIL>>

	* Version 2.4.3 released.
	=========================


	Tag sources with `VER-2-4-3'.

	* docs/CHANGES: Updated.

	* docs/VERSION.DLL: Update documentation and bump version number to
	2.4.3

	* README, Jamfile (RefDoc),
	builds/win32/vc2005/freetype.vcproj, builds/win32/vc2005/index.html,
	builds/win32/vc2008/freetype.vcproj, builds/win32/vc2008/index.html,
	builds/win32/visualc/freetype.dsp,
	builds/win32/visualc/freetype.vcproj,
	builds/win32/visualc/index.html, builds/win32/visualce/freetype.dsp,
	builds/win32/visualce/freetype.vcproj,
	builds/win32/visualce/index.html,
	builds/wince/vc2005-ce/freetype.vcproj,
	builds/wince/vc2005-ce/index.html,
	builds/wince/vc2008-ce/freetype.vcproj,
	builds/wince/vc2008-ce/index.html: s/2.4.2/2.4.3/, s/242/243/.

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 3.

	* builds/unix/configure.raw (version_info): Set to 12:1:6.

2010-10-03  Werner Lemberg  <<EMAIL>>

	Avoid `configure' issues with symbolic links.
	Based on a patch from Alexander Stohr <<EMAIL>>.

	* configure: Compare directories using `ls -id'.
	Check existence of `reference' subdirectory before creating it.

2010-10-02  Werner Lemberg  <<EMAIL>>

	[sfnt] Fix Savannah bug #31088 (sort of).

	* src/sfnt/ttload.c (tt_face_load_maxp): Always allocate at least 64
	function entries.

2010-10-02  Werner Lemberg  <<EMAIL>>

	[smooth] Fix splitting of cubics for negative values.

	Reported by Róbert Márki <<EMAIL>>; see
	https://lists.gnu.org/archive/html/freetype/2010-09/msg00019.html.

	* src/smooth/ftgrays.c (gray_render_cubic): Fix thinko.

2010-10-01  suzuki toshiya  <<EMAIL>>

	[truetype] Fix Savannah bug #31040.

	* src/truetype/ttinterp.c (free_buffer_in_size): Remove.
	(TT_RunIns): Updated.

2010-09-20  suzuki toshiya  <<EMAIL>>

	[sfnt] Make error message filling NULL names less verbose.

	* src/sfnt/ttpost.c (load_format_20): Showing 1 summary message
	when we fill `post' names by NULL, instead of per-entry message.

2010-09-20  Graham Asher  <<EMAIL>>
	    David Bevan  <<EMAIL>>

	[smooth] Fix and improve spline flattening.

	This fixes the flattening of cubic, S-shaped curves and speeds up
	the handling of both the conic and cubic arcs.

	See the discussions on the freetype-devel mailing list in late
	August and September 2010 for details.

	* src/smooth/ftgrays.c (FT_MAX_CURVE_DEVIATION): New macro.
	(TWorker): Remove `conic_level' and `cubic_level' elements.
	(gray_render_conic): Simplify algorithm.
	(gray_render_cubic): New algorithm; details are given in the code
	comments.
	(gray_convert_glyph): Remove heuristics.

2010-09-19  Werner Lemberg  <<EMAIL>>

	Minor fixes.

	* src/cff/cffload.c (cff_charset_compute_cids): `charset->sids[i]'
	is `FT_UShort'.
	(cff_index_access_element): Don't use additions in comparison.
	* src/sfnt/ttpost.c (load_format_20): Make `post_limit' of type
	`FT_Long'.
	Don't use additions in comparison.
	Improve tracing messages.
	(load_format_25, load_post_names): Make `post_limit' of type
	`FT_Long'.

2010-09-19  suzuki toshiya  <<EMAIL>>

	[cff] Truncate the element length at the end of the stream.
	See Savannah bug #30975.

	* src/cff/cffload.c (cff_index_access_element): `off2', the offset
	to the next element is truncated at the end of the stream to prevent
	invalid I/O.  As `off1', the offset to the requested element has
	been checked by `FT_STREAM_SEEK', `off2' should be checked
	similarly.

2010-09-19  suzuki toshiya  <<EMAIL>>

	[cff] Ignore CID > 0xFFFFU.
	See Savannah bug #30975.

	* src/cff/cffload.c (cff_charset_compute_cids): Ignore CID if
	greater than 0xFFFFU.  CFF font spec does not mention maximum CID in
	the font, but PostScript and PDF spec define that maximum CID is
	0xFFFFU.

2010-09-19  suzuki toshiya  <<EMAIL>>

	[cff] Make trace message in` cff_charset_load' verbose.
	See Savannah bug #30975.

	* src/cff/cffload.c (cff_charset_load): Report the original `nleft'
	and truncated `nleft'.

2010-09-19  suzuki toshiya  <<EMAIL>>

	[cff] Correct `max_cid' from CID array length to max CID.
	See Savannah bug #30975.

	* src/cff/cffload.c (cff_charset_compute_cids): Don't increment
	max_cid after detecting max CID.  The array CFF_Charset->cids is
	allocated by max_cid + 1.
	(cff_charset_cid_to_gindex): Permit CID is less than or equal to
	CFF_Charset->max_cid.
	* src/cff/cffobjs.c (cff_face_init): FT_Face->num_glyphs is
	calculated as CFF_Charset->max_cid + 1.

2010-09-19  suzuki toshiya  <<EMAIL>>

	[truetype] Sanitize the broken offsets in `loca'.
	See Savannah bug #31040.

	* src/truetype/ttpload.c (tt_face_get_location): If `pos1', the
	offset to the requested entry in `glyf' exceeds the end of the
	table, return offset=0, length=0.  If `pos2', the offset to the next
	entry in `glyf' exceeds the end of the table, truncate the entry
	length at the end of `glyf' table.

2010-09-19  suzuki toshiya  <<EMAIL>>

	[sfnt] Prevent overrunning in `post' table parser.
	See Savannah bug #31040.

	* src/sfnt/ttpost.c (load_post_names): Get the length of `post'
	table and pass the limit of `post' table to `load_format_20' and
	`load_format_25'.
	(load_format_20): Stop the parsing when we reached at the limit of
	`post' table.  If more glyph names are required, they are filled by
	NULL names.

2010-09-17  suzuki toshiya  <<EMAIL>>

	[truetype] Don't duplicate size->twilight structure to be freed.
	See Savannah bug #31040 for detail.

	* src/truetype/ttinterp.c (free_buffer_in_size): Don't duplicate
	FT_GlyphZoneRec size->twilight to be freed.  If duplicated,
	`FT_FREE' erases the duplicated pointers only and leave original
	pointers.  They can cause the double-free crash when the burst
	errors occur in TrueType interpreter and `free_buffer_in_size' is
	invoked repeatedly.

2010-09-15  Werner Lemberg  <<EMAIL>>

	Make bytecode debugging with FontForge work again.

	* src/truetype/ttinterp.c (TT_RunIns): Don't call
	`free_buffer_in_size' in case of error if a debugger is active.

2010-09-14  Werner Lemberg  <<EMAIL>>

	Improve tracing messages.

	* src/truetype/ttinterp.c (TT_RunIns): Improve wording of tracing
	message.
	* src/truetype/ttobjs.c (tt_size_run_fpgm, tt_size_run_prep): Add
	tracing message.
	* src/truetype/ttgload.c (tt_loader_init): Add tracing message.
	* src/cache/ftcsbits.c (ftc_snode_load): Emit tracing message if
	glyph doesn't fit into a small bitmap container.

2010-09-13  Werner Lemberg  <<EMAIL>>

	Fix minor issues reported by <<EMAIL>>.

	* src/autofit/aflatin.c (af_latin_compute_stem_width): Remove
	redundant conditional check.
	* src/base/ftsynth.c (FT_GlyphSlot_Embolden): Ditto.
	* src/cff/cffload.c (cff_encoding_load): Remove conditional check
	which always evaluates to `true'.
	* src/pshinter/pshalgo.c (ps_glyph_interpolate_strong_points):
	Ditto.
	* src/truetype/ttinterp.c (Ins_IUP): Ditto.
	* src/cid/cidgload.c (cid_slot_load_glyph): Don't check for NULL if
	value is already dereferenced.
	* src/winfonts/winfnt.c (FNT_Load_Glyph): Fix check of `face'.

2010-08-31  suzuki toshiya  <<EMAIL>>

	Ignore the environmental setting of LIBTOOL.
	Patch is suggested by Adrian Bunk, to prevent unexpected
	reflection of environmental LIBTOOL.  See:
	https://savannah.nongnu.org/patch/?7290

	* builds/unix/unix-cc.in: LIBTOOL is unconditionally set to
	$(FT_LIBTOOL_DIR)/libtool.  FT_LIBTOOL_DIR is set to $(BUILD_DIR)
	by default.
	* configure: When configured for the building out of source tee,
	FT_LIBTOOL_DIR is set to $(OBJ_DIR).

2010-08-31  suzuki toshiya  <<EMAIL>>

	[truetype] Decrease the trace level catching the interpreter error.

	* src/truetype/ttinterp.c (TT_RunIns): Decrease the trace level
	showing the error when the interpreter returns with an error,
	from` FT_TRACE7' to `FT_TRACE1'.

2010-08-30  suzuki toshiya  <<EMAIL>>

	[truetype] Prevent bytecode reuse after the interpretation error.

	* src/truetype/ttinterp.c (free_buffer_in_size): New function to
	free the buffer allocated during the interpretation of this glyph.
	(TT_RunIns): Unset FT_Face->size->{cvt_ready,bytecode_ready} if
	an error occurs in the bytecode interpretation.  The interpretation
	of invalid bytecode may break the function definitions and referring
	them in later interpretation is danger.  By unsetting these flags,
	`fpgm' and `prep' tables are executed again in next interpretation.

	This fixes Savannah bug #30798, reported by Robert Święcki.

2010-08-29  Werner Lemberg  <<EMAIL>>

	[ftraster] Pacify compiler.

	* src/raster/ftraster.c (ft_black_new) [_STANDALONE_]: `memory' is
	not used.

2010-08-29  Werner Lemberg  <<EMAIL>>

	[cff] Allow SIDs >= 65000.

	* src/cff/cffload.c (cff_charset_load): Fix change from 2009-03-20:
	The threshold for SIDs is not applicable here.  I misinterpreted the
	`SID values 65000 and above are available for implementation use'
	sentence in the CFF specification.

	Problem reported by Ivan Ninčić <<EMAIL>>.

2010-08-28  suzuki toshiya  <<EMAIL>>

	Force hinting when the font lacks its familyname.

	In Type42 or Type11 font embedded in PostScript & PDF, TrueType sfnt
	stream may lack `name' table because they are not required.  Hinting
	for nameless fonts is safer for PDFs including embedded Chinese
	fonts.  Written by David Bevan, see:

	https://lists.gnu.org/archive/html/freetype-devel/2010-08/msg00021.html
	https://lists.freedesktop.org/archives/poppler/2010-August/006310.html

	* src/truetype/ttobjs.c (tt_check_trickyness): If a NULL pointer by
	nameless font is given, TRUE is returned to enable hinting.

2010-08-28  suzuki toshiya  <<EMAIL>>

	Register yet another tricky TrueType font.

	* src/truetype/ttobjs.c (tt_check_trickyness): Add `HuaTianKaiTi?',
	a Kaishu typeface paired with `HuaTianSongTi?' by Huatian
	Information Industry.

2010-08-17  Teijo Kinnunen  <<EMAIL>>

	[cache] Fix Savannah bug #30788.

	* src/cache/ftccache.c (FTC_Cache_Clear): Check `cache->buckets' for
	NULL too.

2010-08-10  Werner Lemberg  <<EMAIL>>

	Try to fix Savannah bug #30717 (and probably #30719 too).

	* src/smooth/ftsmooth.c (ft_smooth_render_generic): Add another
	overflow test for `width' and `height'.

2010-08-06  Werner Lemberg  <<EMAIL>>

	* Version 2.4.2 released.
	=========================


	Tag sources with `VER-2-4-2'.

	* docs/CHANGES: Updated.

	* docs/VERSION.DLL: Update documentation and bump version number to
	2.4.2

	* README, Jamfile (RefDoc),
	builds/win32/vc2005/freetype.vcproj, builds/win32/vc2005/index.html,
	builds/win32/vc2008/freetype.vcproj, builds/win32/vc2008/index.html,
	builds/win32/visualc/freetype.dsp,
	builds/win32/visualc/freetype.vcproj,
	builds/win32/visualc/index.html, builds/win32/visualce/freetype.dsp,
	builds/win32/visualce/freetype.vcproj,
	builds/win32/visualce/index.html,
	builds/wince/vc2005-ce/freetype.vcproj,
	builds/wince/vc2005-ce/index.html,
	builds/wince/vc2008-ce/freetype.vcproj,
	builds/wince/vc2008-ce/index.html: s/2.4.1/2.4.2/, s/241/242/.

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 2.

	* builds/unix/configure.raw (version_info): Set to 12:0:6.

2010-08-06  suzuki toshiya  <<EMAIL>>

	Fix Savannah bug #30648.

	* src/base/ftobjs.c (FT_Done_Library): Specify the order of font
	drivers during the face closing process.  Type42 faces should be
	closed before TrueType faces, because a Type42 face refers to
	another internal TrueType face which is created from sfnt[] array on
	the memory.

2010-08-06  Yuriy Kaminskiy  <<EMAIL>>

	[raster] Fix valgrind warning.

	* src/raster/ftraster.c (Decompose_Curve) <default>: Access point[0]
	only if we don't hit `limit'.

2010-08-06  suzuki toshiya  <<EMAIL>>

	Fix Savannah bug #30658.

	* src/base/ftobjs.c (Mac_Read_POST_Resource): Check that the total
	length of collected POST segments does not overrun the allocated
	buffer.

2010-08-06  Yuriy Kaminskiy  <<EMAIL>>

	Fix conditional usage of FT_MulFix_i386.
	With -ansi flag, gcc does not define `i386', only `__i386__'.

	* include/freetype/config/ftconfig.h, builds/unix/ftconfig.in:
	s/i386/__i386__/.

2010-08-05  Werner Lemberg  <<EMAIL>>

	[truetype] Fix Savannah bug #30657.

	* src/truetype/ttinterp.c (BOUNDSL): New macro.
	Change `BOUNDS' to `BOUNDSL' where appropriate.

	* src/truetype/ttinterp.h (TT_ExecContextRec): Fix type of
	`cvtSize'.

2010-08-05  Werner Lemberg  <<EMAIL>>

	[type42] Fix Savannah bug #30656.

	* src/type42/t42parse.c (t42_parse_sfnts): Protect against negative
	string_size.
	Fix comparison.

2010-08-05  suzuki toshiya  <<EMAIL>>

	[cff] Don't use any values in decoder after parsing error.

	* src/cff/cffgload.c (cff_slot_load): Skip the evaluations
	of the values in decoder, if `cff_decoder_parse_charstrings'
	returns any error.

2010-08-04  Werner Lemberg  <<EMAIL>>

	Fix Savannah bug #30644.

	* src/base/ftstream.c (FT_Stream_EnterFrame): Fix comparison.

2010-08-04  Werner Lemberg  <<EMAIL>>

	`make devel' fails if FT_CONFIG_OPTION_OLD_INTERNALS is set.

	* devel/ftoption.h: Synchronize with
	include/freetype/config/ftoption.h.

2010-08-04  suzuki toshiya  <<EMAIL>>

	[cff] Improve stack overflow test.

	* src/cff/cffgload.c (cff_decoder_parse_charstrings): Check stack
	after execution of operations too.

2010-07-18  Werner Lemberg  <<EMAIL>>

	Add reference counters and to FT_Library and FT_Face objects.

	* include/freetype/freetype.h (FT_Reference_Face): New function.
	* include/freetype/ftmodapi.h (FT_Reference_Library): New function.

	* include/freetype/internal/ftobjs.h (FT_Face_InternalRec,
	FT_LibraryRec): New field `refcount'.

	* src/base/ftobjs.c (FT_Open_Face, FT_New_Library): Handle
	`refcount'.
	(FT_Reference_Face, FT_Reference_Library): Implement new functions.
	(FT_Done_Face, FT_Done_Library): Handle `refcount'.

	* docs/CHANGES: Updated.

2010-07-18  Werner Lemberg  <<EMAIL>>

	* Version 2.4.1 released.
	=========================


	Tag sources with `VER-2-4-1'.

	* docs/CHANGES: Updated.

	* docs/VERSION.DLL: Update documentation and bump version number to
	2.4.1.

	* README, Jamfile (RefDoc),
	builds/win32/vc2005/freetype.vcproj, builds/win32/vc2005/index.html,
	builds/win32/vc2008/freetype.vcproj, builds/win32/vc2008/index.html,
	builds/win32/visualc/freetype.dsp,
	builds/win32/visualc/freetype.vcproj,
	builds/win32/visualc/index.html, builds/win32/visualce/freetype.dsp,
	builds/win32/visualce/freetype.vcproj,
	builds/win32/visualce/index.html,
	builds/wince/vc2005-ce/freetype.vcproj,
	builds/wince/vc2005-ce/index.html,
	builds/wince/vc2008-ce/freetype.vcproj,
	builds/wince/vc2008-ce/index.html: s/2.4.0/2.4.1/, s/240/241/.

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 1.

	* builds/unix/configure.raw (version_info): Set to 11:1:5.

2010-07-17  Werner Lemberg  <<EMAIL>>

	[cff] Final try to fix `hintmask' and `cntrmask' limit check.

	Problem reported by Tobias Wolf <<EMAIL>>.

	* src/cff/cffgload.c (cff_decoder_parse_charstrings)
	<cff_op_hintmask>: Sigh.  I'm apparently too silly to fix this
	correctly in less than three tries.

2010-07-12  Werner Lemberg  <<EMAIL>>

	* Version 2.4.0 released.
	=========================


	Tag sources with `VER-2-4-0'.

	* docs/CHANGES: Updated.

	* docs/VERSION.DLL: Update documentation and bump version number to
	2.4.0.

	* README, Jamfile (RefDoc),
	builds/win32/vc2005/freetype.vcproj, builds/win32/vc2005/index.html,
	builds/win32/vc2008/freetype.vcproj, builds/win32/vc2008/index.html,
	builds/win32/visualc/freetype.dsp,
	builds/win32/visualc/freetype.vcproj,
	builds/win32/visualc/index.html, builds/win32/visualce/freetype.dsp,
	builds/win32/visualce/freetype.vcproj,
	builds/win32/visualce/index.html,
	builds/wince/vc2005-ce/freetype.vcproj,
	builds/wince/vc2005-ce/index.html,
	builds/wince/vc2008-ce/freetype.vcproj,
	builds/wince/vc2008-ce/index.html: s/2.3.12/2.4.0/, s/2312/240/.

	* include/freetype/freetype.h (FREETYPE_MINOR): Set to 4.
	(FREETYPE_PATCH): Set to 0.

	* builds/unix/configure.raw (version_info): Set to 11:0:5.

2010-07-12  Werner Lemberg  <<EMAIL>>

	Remove C++ warnings.

	*/*: Initialize pointers where necessary to make g++ happy.

2010-07-12  malc  <<EMAIL>>
	    Richard Henderson  <<EMAIL>>

	Fix type-punning issues with C++.

	* include/freetype/internal/ftmemory.h (FT_ASSIGNP) [__cplusplus]:
	Emulate a `typeof' operator with an inline template which uses
	`static_cast'.

2010-07-11  Werner Lemberg  <<EMAIL>>

	Fix C++ compilation issue.

	* src/tools/apinames.c (names_dump) <OUTPUT_WATCOM_LBC>: Fix
	type of `dot' variable.

2010-07-10  suzuki toshiya  <<EMAIL>>

	Fix another case reported in Savannah bug #30373.
	Permit a face for Type1, Type42 and CFF without charmap,
	patch by Tor Andersson.

	* src/type1/t1objs.c (T1_Face_Init): Reset the error if it
	is FT_Err_No_Unicode_Glyph_Name.
	* src/type42/t42objs.c (T42_Face_Init): Ditto.
	* src/cff/cffobjs.c (cff_face_init): Ditto.

2010-07-09  suzuki toshiya  <<EMAIL>>

	Use defined macros to set {platform,encoding}_id.

	* src/bdf/bdfdrivr.c: Include ttnameid.h and use macros to
	set charmap.{platform,encoding}_id.
	* src/pcf/pcfdrivr.c: Ditto.
	* src/winfonts/winfnt.c: Ditto.
	* src/type1/t1objs.c: Ditto.
	* src/type42/t42objs.c: Ditto.
	* src/cff/cffobjs.c: Ditto.
	* src/pfr/pfrobjs.c: Ditto.

2010-07-09  suzuki toshiya  <<EMAIL>>

	Fix Savannah bug #30373.
	Too serious check of errors by `FT_CMap_New' since 2010-07-04
	is fixed. Reported by Tor Andersson.

	* include/freetype/fterrdef.h
	(PSnames_Err_No_Unicode_Glyph_Name): New error code to
	indicate the Unicode charmap synthesis failed because
	no Unicode glyph name is found.

	* src/psnames/psmodule.c (ps_unicodes_init): Return
	PSnames_Err_No_Unicode_Glyph_Name when no Unicode glyph name
	is found in the font.
	* src/cff/cffcmap.c (cff_cmap_unicode_init): Return
	CFF_Err_No_Unicode_Glyph_Name when no SID is available.

	* src/type1/t1objs.c (T1_Face_Init): Proceed if `FT_CMap_New'
	is failed by the lack of Unicode glyph name.
	* src/type42/t42objs.c (T42_Face_Init): Ditto.
	* src/cff/cffobjs.c (cff_face_init): Ditto.

2010-07-09  Ken Sharp  <<EMAIL>>

	Make ftraster.c compile in stand-alone mode with MSVC compiler.

	* src/raster/ftmisc.h (FT_Int64) [_WIN32, _WIN64]: Fix typedef
	since there is no `inttypes.h' for MSVC.

2010-07-08  Werner Lemberg  <<EMAIL>>

	[truetype] Fix Savannah bug #30361.

	* src/truetype/ttinterp.c (Ins_IUP): Fix bounds check.

2010-07-06  Werner Lemberg  <<EMAIL>>

	Pacify compiler.

	* src/cff/cffload.c (cff_index_get_pointers): Initialize
	`new_bytes'.

2010-07-05  Eugene A. Shatokhin  <<EMAIL>>

	Fix Savannah bug #27648.

	* src/base/ftobjs.c (ft_remove_renderer, FT_Add_Module): Call
	`raster_done' only if we have an outline glyph format.

2010-07-05  Werner Lemberg  <<EMAIL>>

	Fix Savannah bug #30030.

	* builds/win32/*/freetype.vcproj: Add ftxf86.c.

2010-07-05  Werner Lemberg  <<EMAIL>>

	[cff] Next try to fix `hintmask' and `cntrmask' limit check.

	Problem reported by malc <<EMAIL>>.

	* src/cff/cffgload.c (cff_decoder_parse_charstrings)
	<cff_op_hintmask>: It is possible that there is just a single byte
	after the `hintmask' or `cntrmask', e.g., a `return' instruction.

2010-07-04  suzuki toshiya  <<EMAIL>>

	Restrict the number of the charmaps in a rogue-compatible mode.
	Fix for Savannah bug #30059.

	* src/cache/ftccmap.c (FTC_CMapCache_Lookup): Replace `16' the
	minimum character code passed by a legacy rogue client by...
	* include/freetype/config/ftoption.h (FT_MAX_CHARMAP_CACHEABLE):
	This.  It is undefined when FT_CONFIG_OPTION_OLD_INTERNALS is
	undefined (thus the rogue client compatibility is not required).

	* src/cff/cffobjs.c (cff_face_init): Abort the automatic
	selection or synthesis of Unicode cmap subtable when the charmap
	index exceeds FT_MAX_CHARMAP_CACHEABLE.
	* src/sfnt/ttcmap.c (tt_face_build_cmaps): Issue error message
	when the charmap index exceeds FT_MAX_CHARMAP_CACHEABLE.

	* src/base/ftobjs.c (find_unicode_charmap): When Unicode charmap
	is found after FT_MAX_CHARMAP_CACHEABLE, ignore it and search
	earlier one.
	(find_variant_selector_charmap): When UVS charmap is found after
	FT_MAX_CHARMAP_CACHEABLE, ignore it and search earlier one.
	(FT_Select_Charmap): When a charmap matching with requested
	encoding but after FT_MAX_CHARMAP_CACHEABLE, ignore and search
	earlier one.
	(FT_Set_Charmap): When a charmap matching with requested
	charmap but after FT_MAX_CHARMAP_CACHEABLE, ignore and search
	earlier one.
	(FT_Get_Charmap_Index): When a requested charmap is found
	after FT_MAX_CHARMAP_CACHEABLE, return the inverted charmap
	index.

2010-07-04  Werner Lemberg  <<EMAIL>>

	TrueType hinting is no longer patented.

	* include/freetype/config/ftoption.h, devel/ftoption.h
	(TT_CONFIG_OPTION_BYTECODE_INTERPRETER): Define.
	(TT_CONFIG_OPTION_UNPATENTED_HINTING): Undefine.

	* docs/CHANGES, docs/INSTALL, include/freetype/freetype.h: Updated.
	* docs/TRUETYPE, docs/PATENTS: Removed.

2010-07-04  suzuki toshiya  <<EMAIL>>

	Check error value by `FT_CMap_New'.

	* src/cff/cffobjs.c (cff_face_init): Check error value by
	`FT_CMap_New'.
	* src/pfr/pfrobjs.c (pfr_face_init): Ditto.
	* src/type1/t1objs.c (T1_Face_Init): Ditto.
	* src/type42/t42objs.c (T42_Face_Init): Ditto.

2010-07-03  Werner Lemberg  <<EMAIL>>

	Make ftgrays.c compile stand-alone again.

	* src/smooth/ftgrays.c [_STANDALONE_]: Include `stddef.h'.
	(FT_INT_MAX, FT_PtrDist)[_STANDALONE_]: Define.

2010-07-02  suzuki toshiya  <<EMAIL>>

	Additional fix for Savannah bug #30306.

	* src/base/ftobjs.c (Mac_Read_POST_Resource): If the type of the
	POST fragment is 0, the segment is completely ignored.  The declared
	length of the segment is not cared at all.  According to Adobe
	Technical Note 5040, type 0 segment is a comment only and should not
	be loaded for the interpreter.  Reported by Robert Święcki.

2010-07-01  Werner Lemberg  <<EMAIL>>

	[truetype] Protect against code range underflow.

	* src/truetype/ttinterp.c (DO_JROT, DO_JMPR, DO_JROF): Don't allow
	negative IP values.

2010-07-01  Werner Lemberg  <<EMAIL>>

	[truetype] Add rudimentary tracing for bytecode instructions.

	* src/truetype/ttinterp.c (opcode_name) [FT_DEBUG_LEVEL_TRACE]: New
	array.
	(TT_RunIns): Trace opcodes.

2010-06-30  Werner Lemberg  <<EMAIL>>

	[smooth] Fix Savannah bug #30263.

	* src/smooth/ftgrays.c (gray_render_span): Use cast to `unsigned
	int' to avoid integer overflow.

	* src/smooth/ftsmooth.c (ft_smooth_render_generic): Use smaller
	threshold values for `width' and `height'.  This is not directly
	related to the bug fix but makes sense anyway.

2010-07-01  suzuki toshiya  <<EMAIL>>

	Initial fix for Savannah bug #30306.

	* src/base/ftobjs.c (Mac_Read_POST_Resource): Check `rlen', the
	length of fragment declared in the POST fragment header, and prevent
	an underflow in length calculation.  Some fonts set the length to
	zero in spite of the existence of a following 16bit `type'.
	Reported by Robert Święcki.

2010-07-01  suzuki toshiya  <<EMAIL>>

	Additional fix for Savannah bug #30248 and #30249.

	* src/base/ftobjs.c (Mac_Read_POST_Resource): Check the buffer size
	during gathering PFB fragments embedded in LaserWriter PS font for
	Macintosh.  Reported by Robert Święcki.

2010-06-30  Alexei Podtelezhnikov  <<EMAIL>>

	Minor optimizations by avoiding divisions.

	* src/sfnt/ttkern.c (tt_face_load_kern, tt_face_get_kerning):
	Replace divisions with multiplication in comparisons.

2010-06-29  Werner Lemberg  <<EMAIL>>

	Fix minor tracing issues.

	* src/cff/cffgload.c, src/truetype/ttgload.c: Adjust tracing levels.

2010-06-27  Werner Lemberg  <<EMAIL>>

	[cff] Really fix `hintmask' and `cntrmask' limit check.

	* src/cff/cffgload.c (cff_decoder_parse_charstrings)
	<cff_op_hintmask>: Fix thinko and handle tracing also.

2010-06-27  Werner Lemberg  <<EMAIL>>

	Fix valgrind warning.

	* src/base/ftoutln.c (FT_Outline_Get_Orientation): Initialize
	`result' array.

2010-06-27  Werner Lemberg  <<EMAIL>>

	[cff] Fix memory leak.

	* src/cff/cffgload.c (cff_operator_seac): Free charstrings even in
	case of errors.

2010-06-27  Werner Lemberg  <<EMAIL>>

	[cff] Protect against invalid `hintmask' and `cntrmask' operators.

	* src/cff/cffgload.c (cff_decoder_parse_charstrings)
	<cff_op_hintmask>: Ensure that we don't exceed `limit' while parsing
	the bit masks of the `hintmask' and `cntrmask' operators.

2010-06-26  Werner Lemberg  <<EMAIL>>

	Fix PFR change 2010-06-24.

	* src/pfr/pfrgload.c (pfr_glyph_load_simple): Really protect against
	invalid indices.

2010-06-26  Werner Lemberg  <<EMAIL>>

	Improve PFR tracing messages.

	* src/pfr/pfrgload.c (pfr_glyph_load_rec): Emit tracing messages for
	simple and compound glyph offsets.

2010-06-26  Werner Lemberg  <<EMAIL>>

	Fix last PFR change.

	* src/pfr/pfrobjs.c (pfr_face_init): Fix rejection logic.

2010-06-26  Werner Lemberg  <<EMAIL>>

	[sfnt] Fix Savannah bug #30262.

	* src/sfnt/ttload.c (tt_face_load_maxp): Limit `maxComponentDepth'
	arbitrarily to 100 to avoid stack exhaustion.

2010-06-26  Werner Lemberg  <<EMAIL>>

	Add some memory checks (mainly for debugging).

	* src/base/ftstream.c (FT_Stream_EnterFrame): Exit with error
	if the frame size is larger than the stream size.

	* src/base/ftsystem.c (ft_ansi_stream_io): Exit with error if
	seeking a position larger than the stream size.

2010-06-25  Werner Lemberg  <<EMAIL>>

	[pfr] Fix Savannah bug #30261.

	* src/pfr/pfrobjs.c (pfr_face_init): Reject fonts which contain
	neither outline nor bitmap glyphs.

2010-06-25  Werner Lemberg  <<EMAIL>>

	[cff] Fix Savannah bug #30254.

	* src/cff/cffload.c (cff_index_get_pointers): Do sanity check for
	first offset also.

2010-06-25  suzuki toshiya  <<EMAIL>>

	Initial fix for Savannah bug #30248 and #30249.

	* src/base/ftobjs.c (Mac_Read_POST_Resource): Check the error during
	reading a PFB fragment embedded in LaserWriter PS font for Macintosh.
	Reported by Robert Święcki.

2010-06-24  Werner Lemberg  <<EMAIL>>

	[pcf] Fix Savannah bug #30247.

	* src/pcf/pcfread.c (pcf_get_metrics): Disallow (invalid) fonts with
	zero metrics.

2010-06-24  Graham Asher  <<EMAIL>>

	* src/smooth/ftgrays.c (gray_render_cubic): Fix algorithm.
	The previous version was too aggressive, as demonstrated in
	https://lists.gnu.org/archive/html/freetype-devel/2010-06/msg00020.html.

2010-06-24  Werner Lemberg  <<EMAIL>>

	*/*: Use module specific error names where appropriate.

2010-06-24  Werner Lemberg  <<EMAIL>>

	[sfnt] Fix Savannah bug #30236.

	* src/sfnt/ttcmap.c (tt_face_build_cmaps): Improve check for pointer
	to `cmap_table'.

2010-06-24  Werner Lemberg  <<EMAIL>>

	[pfr] Fix Savannah bug #30235.

	* src/pfr/pfrgload.c (pfr_glyph_load_simple): Protect against
	invalid indices if there aren't any coordinates for indexing.

2010-06-24  Werner Lemberg  <<EMAIL>>

	[bdf]: Font properties are optional.

	* src/bdf/bdflib.c (_bdf_readstream): Use special error code to
	indicate a redo operation.
	(_bdf_parse_start): Handle `CHARS' keyword here too and pass current
	input line to `_bdf_parse_glyph'.

2010-06-23  Werner Lemberg  <<EMAIL>>

	[bdf] Fix Savannah bug #30220.

	* include/freetype/fterrdef.h
	(BDF_Err_Missing_Fontboundingbox_Field): New error code.

	* src/bdf/bdflib.c (_bdf_parse_start): Check for missing
	`FONTBOUNDINGBOX' field.
	Avoid memory leak if there are multiple `FONT' lines (which is
	invalid but doesn't hurt).

2010-06-21  Werner Lemberg  <<EMAIL>>

	[pfr] Fix Savannah bug #30168.

	* src/pfr/pfrgload.c (pfr_glyph_load_compound): Limit the number of
	subglyphs to avoid endless recursion.

2010-06-20  Werner Lemberg  <<EMAIL>>

	[psaux] Fix Savannah bug #30145.

	* src/psaux/psobjs.c (t1_builder_add_contour): Protect against
	`outline == NULL' which might happen in invalid fonts.

2010-06-19  Werner Lemberg  <<EMAIL>>

	[bdf] Fix Savannah bug #30135.

	* src/bdf/bdflib.c (_bdf_list_join): Don't modify value in static
	string `empty'.
	(_bdf_parse_glyph): Avoid memory leak in case of error.

2010-06-15  Werner Lemberg  <<EMAIL>>

	[autofit] Fix Savannah bug #30108.

	* src/autofit/afglobal.c (af_face_globals_compute_script_coverage):
	Properly mask AF_DIGIT bit in comparison.

2010-06-11  Werner Lemberg  <<EMAIL>>

	[pshinter] Fix Savannah bug #30106.

	Point numbers for FreeType's implementation of hinting masks are
	collected before the final number of points of a glyph has been
	determined; in particular, the code for handling the `endchar'
	opcode can reduce the number of points.

	* src/pshinter/pshalgo.c (psh_glyph_find_strong_points): Assure that
	`end_point' is not larger than `glyph->num_points'.

2010-06-11  Werner Lemberg  <<EMAIL>>

	[cff]: Improve debugging output.

	* src/cff/cffgload.c (cff_decoder_parse_charstrings)
	<cff_op_hintmask>: Implement it.

2010-06-10  Graham Asher  <<EMAIL>>

	ftgrays: Speed up rendering of small cubic splines.

	* src/smooth/ftgrays.c (gray_render_cubic): Implement new,
	simplified algorithm to find out whether the spline can be replaced
	with two straight lines.  See this thread for more:

	  https://lists.gnu.org/archive/html/freetype-devel/2010-06/msg00000.html

2010-06-09  Werner Lemberg  <<EMAIL>>

	[cff] Fix Savannah bug #30082.

	* src/cff/cffgload.c (cff_decoder_parse_charstrings)
	<cff_op_callothersubr>: Protect against stack underflow.

2010-06-08  Werner Lemberg  <<EMAIL>>

	[cff] Fix Savannah bug #30053.

	* src/cff/cffparse.c (cff_parse_real): Handle border case where
	`fraction_length' has value 10.

2010-06-07  Werner Lemberg  <<EMAIL>>

	Fix Savannah bug #30052.
	This bug has been introduced with commit 2415cbf3.

	* src/base/ftobjs.c (FT_Get_First_Char, FT_Get_Next_Char): Protect
	against endless loop in case of corrupted font header data.

2010-05-26  Werner Lemberg  <<EMAIL>>

	Remove unused variable.
	Found by Graham.

	* src/autofit/afhints.c (af_glyph_hints_reload): Remove unused
	variable `first' in first block.

2010-05-22  Werner Lemberg  <<EMAIL>>

	Fix various memory problems found by linuxtesting.org.

	* src/base/ftgxval.c (FT_TrueTypeGX_Free, FT_ClassicKern_Free),
	src/base/ftotval.c (FT_OpenType_Free), src/base/ftpfr.c
	(ft_pfr_check): Check `face'.

	* src/base/ftobjs.c (FT_Get_Charmap_Index): Check `charmap' and
	`charmap->face'.
	(FT_Render_Glyph): Check `slot->face'.
	(FT_Get_SubGlyph_Info): Check `glyph->subglyphs'.

2010-05-22  Werner Lemberg  <<EMAIL>>

	autofit: Remove dead code.
	Suggested by Graham.

	* src/autofit/afhints.c (af_glyph_hints_compute_inflections):
	Removed.
	(af_glyph_hints_reload): Remove third argument.
	Update all callers.

2010-05-21  Bram Tassyns  <<EMAIL>>

	[cff] Fix Savannah bug #27987.

	* src/cff/cffobjs.c (remove_subset_prefix): New function.
	(cff_face_init): Use it to adjust `cffface->family_name'.

2010-05-20  Werner Lemberg  <<EMAIL>>

	TrueType: Make FreeType ignore maxSizeOfInstructions in `maxp'.

	Acroread does the same.

	* src/truetype/ttgload.c (TT_Process_Composite_Glyph): Call
	`Update_Max' to adjust size of instructions array if necessary and
	add a rough safety check.

	(load_truetype_glyph): Save `loader->byte_len' before recursive
	call.

	* src/truetype/ttinterp.h, src/truetype/ttinterp.c (Update_Max):
	Declare it as FT_LOCAL.

2010-05-18  Hongbo Ni  <<EMAIL>>

	Apply Savannah patch #7196.

	* src/cff/cffgload.c (cff_slot_load): Prevent crash if CFF subfont
	index is out of range.

2010-05-11  Werner Lemberg  <<EMAIL>>

	* docs/formats.txt: Give pointer to PCF documentation.
	Information provided by Alan Coopersmith
	<<EMAIL>>.

2010-05-10  Ken Sharp  <<EMAIL>>

	[psaux] Fix Savannah bug #29846.

	Previously we discovered fonts which used `setcurrentpoint' to set
	the initial point of a contour to 0,0.  This caused FreeType to
	raise an error, because the `setcurrentpoint' operator is only
	supposed to be used with the results from an OtherSubr subroutine.

	This was fixed by simply ignoring the error and carrying on.

	Now we have found a font which uses setcurrentpoint to actually
	establish a non-zero point for a contour during the course of a
	glyph program.  FWIW, these files may be produced by an application
	called `Intaglio' on the Mac, when converting TrueType fonts to
	Type 1.

	The fix allows the new invalid behaviour, the old invalid behaviour
	and real proper usage of the operator to work the same way as Adobe
	interpreters apparently do.

	* src/psaux/t1decode.c (t1_decoder_parse_charstrings): Make
	`setcurrentpoint' use the top two elements of the stack to establish
	unconditionally the current x and y coordinates.

	Make the `flex' subroutine handling (OtherSubr 0) put the current
	x,y coordinates onto the stack, instead of two dummy uninitialised
	values.

2010-04-14  Ken Sharp  <<EMAIL>>

	[psaux] Fix Savannah bug #29444.

	* src/psaux/psobjs.c (t1_builder_start_point): Accept (invalid)
	`lineto' immediately after `hsbw', in accordance with Acrobat, GS,
	and others.

2010-04-14  Michał Cichoń  <<EMAIL>>

	[psaux] Fix Savannah bug #27999.

	* src/cache/ftcmanag.c (FTC_Manager_RemoveFaceID): Only remove
	selected entry, not all.

2010-04-06  Jonathan Kew  <<EMAIL>>

	[truetype] Add overflow check to `fvar' table.

	* src/truetype/ttgxvar.c (TT_Get_MM_Var): Check axis and instance
	count.

2010-04-05  Ken Sharp  <<EMAIL>>

	[raster] Fix Savannah bug #29335.

	* src/raster/ftraster.c (Line_Up): Use slow multiplication to
	prevent overflow.  This shouldn't have any serious impact on speed,
	however.

2010-04-05  Werner Lemberg  <<EMAIL>>

	Add new function `FT_Library_SetLcdFilterWeights'.

	This is based on code written by Lifter
	<https://unixforum.org/index.php?showuser=11691>.  It fixes
	FreeDesktop bug #27386.

	* src/base/ftlcdfil.c (FT_Library_SetLcdFilterWeights): New
	function.

	* include/freetype/ftlcdfil.h: Updated.

	* docs/CHANGES: Updated.

2010-04-01  John Tytgat  <<EMAIL>>

	[truetype] Fix Savannah bug #29404.

	* src/truetype/ttgload.c: Revert change 2752bd1a (check on bit 1
	of `head' table of TrueType fonts).

2010-03-14  suzuki toshiya  <<EMAIL>>

	Fix `multi build' for Tytgat's CFF driver improvement.

	* src/base/cffload.h (cff_index_get_name): Added.

2010-03-12  suzuki toshiya  <<EMAIL>>

	Remove duplicated inclusion of `FT_OUTLINE_H' in ftobjs.c.

	* src/base/ftobjs.c: Remove 2nd inclusion of `FT_OUTLINE_H'.

2010-03-11  Chris Liddell  <<EMAIL>>

	[raster] Fix Savannah bug #27442.

	* src/raster/ftraster.c (ft_black_reset): Fix `buffer_size'.

2010-03-09  Werner Lemberg  <<EMAIL>>

	[cff] Remove unused variable.
	Reported by Graham.

	* src/cff/cffparse.c (cff_parse_real): Remove `rest'.

2010-03-02  John Tytgat  <<EMAIL>>

	[cff] Improve CFF string (especially glyphname) lookup performance.

	We do this by avoiding memory allocation and file I/O.  This is
	Savannah patch #7104.

	* src/cff/cfftypes.h: Include PS cmaps service and
	FT_INTERNAL_POSTSCRIPT_HINTS_H.
	(CFF_SubFontRec): Remove `num_local_subrs'.
	(CFF_FontRec): Add `num_strings', `strings', and `string_pool'
	fields.
	Remove `string_index' and `num_global_subrs' fields.
	Use real types instead of `void' for `pshinter' and `psnames' fields.

	* src/cff/cffload.c: Don't include PS cmaps service.
	(cff_index_get_pointers): Add `pool' parameter which allows inserting
	an extra NUL character for each String INDEX entry.
	(cff_index_get_name): Make it a local function.
	(cff_index_get_string): New function.
	(cff_subfont_load): Updated.
	(cff_font_load): Initialize `num_strings', `strings', and
	`string_pool' fields in the `CFF_FontRec' structure.
	(cff_index_get_sid_string): Use `cff_index_get_string' instead of
	`cff_index_get_name'.
	(cff_font_done): Updated.

	* src/cff/cffload.h: Don't include PS cmaps service.
	(cff_index_get_string): Added.
	(cff_index_get_sid_string): Updated.

	* src/cff/cffobjs.c: Don't include PS cmaps service and
	FT_INTERNAL_POSTSCRIPT_HINTS_H.
	(cff_size_get_globals_funcs, cff_slot_init): Updated.
	(cff_face_init): Follow `cff_index_get_name',
	`cff_index_get_string', and `cff_index_get_sid_string' changes.

	* src/cff/cffcmap.c (cff_sid_free_glyph_name): Removed.
	(cff_sid_to_glyph_name): Use `cff_index_get_cid_string'.
	(cff_cmap_unicode_init): Updated.

	* src/cff/cffdrivr.c: Don't include PS cmap service.
	(cff_get_glyph_name): Avoid unnecessary lookup for POSTSCRIPT_CMAPS
	service.
	(cff_get_glyph_name, cff_ps_get_font_info, cff_get_ros): Follow API
	`cff_index_get_sid_string' change.
	(cff_get_name_index): Use `cff_index_get_string' instead of
	`cff_index_get_name'.

	* src/cff/cffgload.c: Don't include FT_INTERNAL_POSTSCRIPT_HINTS_H.
	(cff_decoder_init, cff_decoder_prepare): Updated.

2010-02-27  Werner Lemberg  <<EMAIL>>

	Simplify code.
	Suggested by Behdad.

	* src/base/ftobjs.c (FT_Get_First_Char): Don't use a loop since we
	call FT_Get_Next_Char anyway if necessary.

2010-02-26  Behdad Esfahbod  <<EMAIL>>

	Improve handling of invalid glyph indices in char->index functions.

	* src/base/ftobjs.c (FT_Get_First_Char, FT_Get_Next_Char): Use a
	loop.

2010-02-18  Chris Liddell  <<EMAIL>>

	[truetype] Fix Savannah bug #28905.

	Initialize phantom points before calling the incremental interface
	to update glyph metrics.

	* src/truetype/ttgload.c (tt_get_metrics_incr_overrides)
	[FT_CONFIG_OPTION_INCREMENTAL]: New function, split off from...
	(tt_get_metrics): This.
	Updated.
	(load_truetype_glyph): Use tt_get_metrics_incr_overrides.

----------------------------------------------------------------------------

Copyright (C) 2010-2022 by
David Turner, Robert Wilhelm, and Werner Lemberg.

This file is part of the FreeType project, and may only be used, modified,
and distributed under the terms of the FreeType project license,
LICENSE.TXT.  By continuing to use, modify, or distribute this file you
indicate that you have read the license and understand and accept it
fully.


Local Variables:
version-control: never
coding: utf-8
End:
