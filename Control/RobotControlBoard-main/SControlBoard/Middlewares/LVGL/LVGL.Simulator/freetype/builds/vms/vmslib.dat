!
! This is a simple driver file with information used by make.com to
! check if external libraries (like t1lib and freetype) are available on
! the system.
!
! Layout of the file:
!
!    - Lines starting with ! are treated as comments
!    - Elements in a data line are separated by # signs
!    - The elements need to be listed in the following order
!      1.) Name of the Library 
!      2.) Location where the object library can be found
!      3.) Location where the include files for the library can be found
!      4.) Include file used to verify library location
!      5.) CPP define to pass to the build to indicate availability of
!          the library
!
! Example: The following  lines show how definitions
!          might look like. They are site specific and the locations of the
!          library and include files need almost certainly to be changed.
!
! Location: All of the libaries can be found at the following addresses
!
!   ZLIB:     http://www.decus.de:8080/www/vms/sw/zlib.htmlx
!
BZ2LIB # sys$library:libbz2.olb # decc$user_include: # bzlib.h # FT_CONFIG_OPTION_SYSTEM_ZLIB
PNGLIB # sys$library:libpng.olb # sys$library: # png.h # FT_CONFIG_OPTION_SYSTEM_ZLIB
ZLIB # sys$library:libz.olb # sys$library: # zlib.h # FT_CONFIG_OPTION_SYSTEM_ZLIB
