#   File:       FreeType.m68k_cfm.make
#   Target:     FreeType.m68k_cfm
#   Created:    Thursday, October 27, 2005 09:23:25 PM


MAKEFILE        = FreeType.m68k_cfm.make
\xA5MondoBuild\xA5    = {MAKEFILE}  # Make blank to avoid rebuilds when makefile is modified

ObjDir          = :objs:
Includes        =  \xB6
				  -ansi strict \xB6
				  -includes unix \xB6
				  -i :include: \xB6
				  -i :src: \xB6
				  -i :include:freetype:config:

Sym-68K         = -sym off

COptions        = \xB6
			-d FT_MACINTOSH=1 \xB6
			-d HAVE_FSSPEC=1 \xB6
			-d HAVE_FSREF=0 \xB6
			-d HAVE_QUICKDRAW_TOOLBOX=1 \xB6
			-d HAVE_QUICKDRAW_CARBON=0 \xB6
			-d HAVE_ATS=0 \xB6
			-d FT2_BUILD_LIBRARY \xB6
			-d FT_CONFIG_CONFIG_H="<ftconfig.h>" \xB6
			-d FT_CONFIG_MODULES_H="<ftmodule.h>" \xB6
			{Includes} {Sym-68K} -model cfmseg


### Source Files ###

SrcFiles        =  \xB6
				  :src:autofit:autofit.c \xB6
				  :builds:mac:ftbase.c \xB6
				  :src:base:ftbbox.c \xB6
				  :src:base:ftbdf.c \xB6
				  :src:base:ftbitmap.c \xB6
				  :src:base:ftdebug.c \xB6
				  :src:base:ftfstype.c \xB6
				  :src:base:ftglyph.c \xB6
				  :src:base:ftgxval.c \xB6
				  :src:base:ftinit.c \xB6
				  :src:base:ftmm.c \xB6
				  :src:base:ftotval.c \xB6
				  :src:base:ftpfr.c \xB6
				  :src:base:ftstroke.c \xB6
				  :src:base:ftsynth.c \xB6
				  :src:base:ftsystem.c \xB6
				  :src:base:fttype1.c \xB6
				  :src:base:ftwinfnt.c \xB6
				  :src:cache:ftcache.c \xB6
				  :src:bdf:bdf.c \xB6
				  :src:cff:cff.c \xB6
				  :src:cid:type1cid.c \xB6
#				  :src:gxvalid:gxvalid.c \xB6
				  :src:gzip:ftgzip.c \xB6
				  :src:bzip2:ftbzip2.c \xB6
				  :src:lzw:ftlzw.c \xB6
				  :src:otvalid:otvalid.c \xB6
				  :src:pcf:pcf.c \xB6
				  :src:pfr:pfr.c \xB6
				  :src:psaux:psaux.c \xB6
				  :src:pshinter:pshinter.c \xB6
				  :src:psnames:psmodule.c \xB6
				  :src:raster:raster.c \xB6
				  :src:sfnt:sfnt.c \xB6
				  :src:smooth:smooth.c \xB6
				  :src:truetype:truetype.c \xB6
				  :src:type1:type1.c \xB6
				  :src:type42:type42.c \xB6
				  :src:winfonts:winfnt.c


### Object Files ###

ObjFiles-68K    =  \xB6
				  "{ObjDir}autofit.c.o" \xB6
				  "{ObjDir}ftbase.c.o" \xB6
				  "{ObjDir}ftbbox.c.o" \xB6
				  "{ObjDir}ftbdf.c.o" \xB6
				  "{ObjDir}ftbitmap.c.o" \xB6
				  "{ObjDir}ftdebug.c.o" \xB6
				  "{ObjDir}ftfstype.c.o" \xB6
				  "{ObjDir}ftglyph.c.o" \xB6
				  "{ObjDir}ftgxval.c.o" \xB6
				  "{ObjDir}ftinit.c.o" \xB6
				  "{ObjDir}ftmm.c.o" \xB6
				  "{ObjDir}ftotval.c.o" \xB6
				  "{ObjDir}ftpfr.c.o" \xB6
				  "{ObjDir}ftstroke.c.o" \xB6
				  "{ObjDir}ftsynth.c.o" \xB6
				  "{ObjDir}ftsystem.c.o" \xB6
				  "{ObjDir}fttype1.c.o" \xB6
				  "{ObjDir}ftwinfnt.c.o" \xB6
				  "{ObjDir}ftcache.c.o" \xB6
				  "{ObjDir}bdf.c.o" \xB6
				  "{ObjDir}cff.c.o" \xB6
				  "{ObjDir}type1cid.c.o" \xB6
#				  "{ObjDir}gxvalid.c.o" \xB6
				  "{ObjDir}ftgzip.c.o" \xB6
				  "{ObjDir}ftbzip2.c.o" \xB6
				  "{ObjDir}ftlzw.c.o" \xB6
				  "{ObjDir}otvalid.c.o" \xB6
				  "{ObjDir}pcf.c.o" \xB6
				  "{ObjDir}pfr.c.o" \xB6
				  "{ObjDir}psaux.c.o" \xB6
				  "{ObjDir}pshinter.c.o" \xB6
				  "{ObjDir}psmodule.c.o" \xB6
				  "{ObjDir}raster.c.o" \xB6
				  "{ObjDir}sfnt.c.o" \xB6
				  "{ObjDir}smooth.c.o" \xB6
				  "{ObjDir}truetype.c.o" \xB6
				  "{ObjDir}type1.c.o" \xB6
				  "{ObjDir}type42.c.o" \xB6
				  "{ObjDir}winfnt.c.o"


### Libraries ###

LibFiles-68K    =


### Default Rules ###

.c.o  \xC4  .c  {\xA5MondoBuild\xA5}
	{C} {depDir}{default}.c -o {targDir}{default}.c.o {COptions}


### Build Rules ###

:builds:mac:ftbase.c \xC4\xC4 :src:base:ftbase.c
	Duplicate :src:base:ftbase.c :builds:mac:ftbase.c

"{ObjDir}ftbase.c.o" \xC4\xC4 :builds:mac:ftbase.c
	{C} :builds:mac:ftbase.c -o "{ObjDir}ftbase.c.o" \xB6
		-i :builds:mac: \xB6
		-i :src:base: \xB6
		{COptions}

FreeType.m68k_cfm    \xC4\xC4  FreeType.m68k_cfm.o

FreeType.m68k_cfm.o  \xC4\xC4  {ObjFiles-68K} {LibFiles-68K} {\xA5MondoBuild\xA5}
	Lib \xB6
		-o {Targ} \xB6
		{ObjFiles-68K} \xB6
		{LibFiles-68K} \xB6
		{Sym-68K} \xB6
		-mf -d



### Required Dependencies ###

"{ObjDir}autofit.c.o" \xC4 :src:autofit:autofit.c
# "{ObjDir}ftbase.c.o" \xC4 :src:base:ftbase.c
"{ObjDir}ftbbox.c.o" \xC4 :src:base:ftbbox.c
"{ObjDir}ftbdf.c.o" \xC4 :src:base:ftbdf.c
"{ObjDir}ftbitmap.c.o" \xC4 :src:base:ftbitmap.c
"{ObjDir}ftdebug.c.o" \xC4 :src:base:ftdebug.c
"{ObjDir}ftfstype.c.o" \xC4 :src:base:ftfstype.c
"{ObjDir}ftglyph.c.o" \xC4 :src:base:ftglyph.c
"{ObjDir}ftgxval.c.o" \xC4 :src:base:ftgxval.c
"{ObjDir}ftinit.c.o" \xC4 :src:base:ftinit.c
"{ObjDir}ftmm.c.o" \xC4 :src:base:ftmm.c
"{ObjDir}ftotval.c.o" \xC4 :src:base:ftotval.c
"{ObjDir}ftpfr.c.o" \xC4 :src:base:ftpfr.c
"{ObjDir}ftstroke.c.o" \xC4 :src:base:ftstroke.c
"{ObjDir}ftsynth.c.o" \xC4 :src:base:ftsynth.c
"{ObjDir}ftsystem.c.o" \xC4 :src:base:ftsystem.c
"{ObjDir}fttype1.c.o" \xC4 :src:base:fttype1.c
"{ObjDir}ftwinfnt.c.o" \xC4 :src:base:ftwinfnt.c
"{ObjDir}ftcache.c.o" \xC4 :src:cache:ftcache.c
"{ObjDir}bdf.c.o" \xC4 :src:bdf:bdf.c
"{ObjDir}cff.c.o" \xC4 :src:cff:cff.c
"{ObjDir}type1cid.c.o" \xC4 :src:cid:type1cid.c
# "{ObjDir}gxvalid.c.o" \xC4 :src:gxvalid:gxvalid.c
"{ObjDir}ftgzip.c.o" \xC4 :src:gzip:ftgzip.c
"{ObjDir}ftbzip2.c.o" \xC4 :src:bzip2:ftbzip2.c
"{ObjDir}ftlzw.c.o" \xC4 :src:lzw:ftlzw.c
"{ObjDir}otvalid.c.o" \xC4 :src:otvalid:otvalid.c
"{ObjDir}pcf.c.o" \xC4 :src:pcf:pcf.c
"{ObjDir}pfr.c.o" \xC4 :src:pfr:pfr.c
"{ObjDir}psaux.c.o" \xC4 :src:psaux:psaux.c
"{ObjDir}pshinter.c.o" \xC4 :src:pshinter:pshinter.c
"{ObjDir}psmodule.c.o" \xC4 :src:psnames:psmodule.c
"{ObjDir}raster.c.o" \xC4 :src:raster:raster.c
"{ObjDir}sfnt.c.o" \xC4 :src:sfnt:sfnt.c
"{ObjDir}smooth.c.o" \xC4 :src:smooth:smooth.c
"{ObjDir}truetype.c.o" \xC4 :src:truetype:truetype.c
"{ObjDir}type1.c.o" \xC4 :src:type1:type1.c
"{ObjDir}type42.c.o" \xC4 :src:type42:type42.c
"{ObjDir}winfnt.c.o" \xC4 :src:winfonts:winfnt.c


### Optional Dependencies ###
### Build this target to generate "include file" dependencies. ###

Dependencies  \xC4  $OutOfDate
	MakeDepend \xB6
		-append {MAKEFILE} \xB6
		-ignore "{CIncludes}" \xB6
		-objdir "{ObjDir}" \xB6
		-objext .o \xB6
		{Includes} \xB6
		{SrcFiles}


