/****************************************************************************
 *
 * gxvmod.h
 *
 *   FreeType's TrueTypeGX/AAT validation module implementation
 *   (specification).
 *
 * Copyright (C) 2004-2022 by
 * su<PERSON> to<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> Hat K.K.,
 * <PERSON>, <PERSON>, and <PERSON>.
 *
 * This file is part of the FreeType project, and may only be used,
 * modified, and distributed under the terms of the FreeType project
 * license, LICENSE.TXT.  By continuing to use, modify, or distribute
 * this file you indicate that you have read the license and
 * understand and accept it fully.
 *
 */

/****************************************************************************
 *
 * gxvalid is derived from both gxlayout module and otvalid module.
 * Development of gxlayout is supported by the Information-technology
 * Promotion Agency(IPA), Japan.
 *
 */


#ifndef GXVMOD_H_
#define GXVMOD_H_

#include <freetype/ftmodapi.h>


FT_BEGIN_HEADER


  FT_EXPORT_VAR( const FT_Module_Class )  gxv_module_class;


FT_END_HEADER

#endif /* GXVMOD_H_ */


/* END */
