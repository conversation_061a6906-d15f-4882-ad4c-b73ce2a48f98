/****************************************************************************
 *
 * afws-decl.h
 *
 *   Auto-fitter writing system declarations (specification only).
 *
 * Copyright (C) 2013-2022 by
 * <PERSON>, <PERSON>, and <PERSON>.
 *
 * This file is part of the FreeType project, and may only be used,
 * modified, and distributed under the terms of the FreeType project
 * license, LICENSE.TXT.  By continuing to use, modify, or distribute
 * this file you indicate that you have read the license and
 * understand and accept it fully.
 *
 */


#ifndef AFWS_DECL_H_
#define AFWS_DECL_H_

  /* Since preprocessor directives can't create other preprocessor */
  /* directives, we have to include the header files manually.     */

#include "afdummy.h"
#include "aflatin.h"
#include "afcjk.h"
#include "afindic.h"

#endif /* AFWS_DECL_H_ */


/* END */
