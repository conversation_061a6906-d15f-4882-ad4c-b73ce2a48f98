                  FreeType font driver for BDF fonts

                       Francesco <PERSON>
                  <<EMAIL>>


Introduction
************

BDF (Bitmap Distribution Format) is a bitmap font format defined by Adobe,
which is intended to be easily understood by both humans and computers.
This code implements a BDF driver for the FreeType library, following the
Adobe Specification V 2.2.  The specification of the BDF font format is
available from Adobe's web site:

  https://www.adobe.com/content/dam/acom/en/devnet/font/pdfs/5005.BDF_Spec.pdf

Many good bitmap fonts in bdf format come with XFree86 (www.XFree86.org).
They do not define vertical metrics, because the X Consortium BDF
specification has removed them.


Encodings
*********

The variety of encodings that accompanies bdf fonts appears to encompass the
small set defined in freetype.h.  On the other hand, two properties that
specify encoding and registry are usually defined in bdf fonts.

I decided to make these two properties directly accessible, leaving to the
client application the work of interpreting them.  For instance:


  #include FT_INTERNAL_BDF_TYPES_H

  FT_Face          face;
  BDF_Public_Face  bdfface;


  FT_New_Face( library, ..., &face );

  bdfface = (BDF_Public_Face)face;

  if ( ( bdfface->charset_registry == "ISO10646" ) &&
       ( bdfface->charset_encoding == "1" )        )
    [..]


Thus the driver always exports `ft_encoding_none' as face->charmap.encoding.
FT_Get_Char_Index's behavior is unmodified, that is, it converts the ULong
value given as argument into the corresponding glyph number.

If the two properties are not available, Adobe Standard Encoding should be
assumed.


Anti-Aliased Bitmaps
********************

The driver supports an extension to the BDF format as used in Mark Leisher's
xmbdfed bitmap font editor.  Microsoft's SBIT tool expects bitmap fonts in
that format for adding anti-aliased them to TrueType fonts.  It introduces a
fourth field to the `SIZE' keyword which gives the bpp value (bits per
pixel) of the glyph data in the font.  Possible values are 1 (the default),
2 (four gray levels), 4 (16 gray levels), and 8 (256 gray levels).  The
driver returns either a bitmap with 1 bit per pixel or a pixmap with 8bits
per pixel (using 4, 16, and 256 gray levels, respectively).


Known problems
**************

- A font is entirely loaded into memory.  Obviously, this is not the Right
  Thing(TM).  If you have big fonts I suggest you convert them into PCF
  format (using the bdftopcf utility): the PCF font drive of FreeType can
  perform incremental glyph loading.

When I have some time, I will implement on-demand glyph parsing.

- Except for encodings properties, client applications have no visibility of
  the PCF_Face object.  This means that applications cannot directly access
  font tables and must trust FreeType.

- Currently, glyph names are ignored.

  I plan to give full visibility of the BDF_Face object in an upcoming
  revision of the driver, thus implementing also glyph names.

- As I have never seen a BDF font that defines vertical metrics, vertical
  metrics are (parsed and) discarded.  If you own a BDF font that defines
  vertical metrics, please let me know (I will implement them in 5-10
  minutes).


License
*******

Copyright (C) 2001-2002 by Francesco Zappa Nardelli

Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

*** Portions of the driver (that is, bdflib.c and bdf.h):

Copyright 2000 Computing Research Labs, New Mexico State University
Copyright 2001-2002, 2011 Francesco Zappa Nardelli

Permission is hereby granted, free of charge, to any person obtaining a
copy of this software and associated documentation files (the "Software"),
to deal in the Software without restriction, including without limitation
the rights to use, copy, modify, merge, publish, distribute, sublicense,
and/or sell copies of the Software, and to permit persons to whom the
Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
THE COMPUTING RESEARCH LAB OR NEW MEXICO STATE UNIVERSITY BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT
OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR
THE USE OR OTHER DEALINGS IN THE SOFTWARE.


Credits
*******

This driver is based on excellent Mark Leisher's bdf library.  If you
find something good in this driver you should probably thank him, not
me.
