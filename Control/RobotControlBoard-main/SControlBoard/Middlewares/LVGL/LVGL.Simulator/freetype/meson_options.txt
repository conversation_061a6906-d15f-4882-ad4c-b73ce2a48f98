#
# meson_options.txt
#

# Copyright (C) 2020-2022 by
# <PERSON>, <PERSON>, and <PERSON>.
#
# This file is part of the FreeType project, and may only be used, modified,
# and distributed under the terms of the FreeType project license,
# LICENSE.TXT.  By continuing to use, modify, or distribute this file you
# indicate that you have read the license and understand and accept it
# fully.


option('brotli',
  type: 'feature',
  value: 'auto',
  description: 'Use Brotli library to support decompressing WOFF2 fonts')

option('bzip2',
  type: 'feature',
  value: 'auto',
  description: 'Support reading bzip2-compressed font files')

option('harfbuzz',
  type: 'feature',
  value: 'auto',
  description: 'Use Harfbuzz library to improve auto-hinting;'
               + ' if available, many glyphs not directly addressable'
               + ' by a font\'s character map will be hinted also')

option('mmap',
  type: 'feature',
  value: 'auto',
  description: 'Use mmap() to open font files for faster parsing')

option('png',
  type: 'feature',
  value: 'auto',
  description: 'Support color bitmap glyph formats in the PNG format;'
               + ' requires libpng')

option('tests',
  type: 'feature',
  value: 'disabled',
  description: 'Enable FreeType unit and regression tests')

option('zlib',
  type: 'combo',
  choices: [ 'auto', 'none',
             'internal', 'external', 'system',
             'disabled', 'enabled' ],
  description: 'Support reading gzip-compressed font files')

# EOF
