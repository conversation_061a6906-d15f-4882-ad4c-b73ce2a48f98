FreeType 2.11.1
===============

Homepage: https://www.freetype.org

FreeType is a freely available software library to render fonts.

It  is  written  in  C,   designed  to  be  small,  efficient,  highly
customizable,  and portable  while capable  of producing  high-quality
output (glyph images) of most vector and bitmap font formats.

Please   read  the   `docs/CHANGES`   file,   it  contains   IMPORTANT
INFORMATION.

Read the files `docs/INSTALL*`  for installation instructions; see the
file `docs/LICENSE.TXT` for the available licenses.

For using FreeType's git repository  instead of a distribution bundle,
please read file `README.git`.

The FreeType 2 API reference is located in directory `docs/reference`;
use the file  `index.html` as the top entry point.   [Please note that
currently  the search  function  for  locally installed  documentation
doesn't work due to cross-site scripting issues.]

Additional documentation is  available as a separate  package from our
sites.  Go to

  https://download.savannah.gnu.org/releases/freetype/

and download one of the following files.

  freetype-doc-2.11.1.tar.xz
  freetype-doc-2.11.1.tar.gz
  ftdoc2111.zip

To view the documentation online, go to

  https://www.freetype.org/freetype2/docs/


Mailing Lists
-------------

The preferred  way of  communication with the  FreeType team  is using
e-mail lists.

  general use and discussion:      <EMAIL>
  engine internals, porting, etc.: <EMAIL>
  announcements:                   <EMAIL>
  git repository tracker:          <EMAIL>

The lists are moderated; see

  https://www.freetype.org/contact.html

how to subscribe.


Bugs
----

Please submit bug reports at

  https://gitlab.freedesktop.org/freetype/freetype/-/issues

Alternatively,    you    might    report    bugs    by    e-mail    to
`<EMAIL>`.    Don't  forget   to  send   a  detailed
explanation of the problem -- there  is nothing worse than receiving a
terse message that only says 'it doesn't work'.


Patches
-------

For larger changes please provide merge requests at

  https://gitlab.freedesktop.org/freetype/freetype/-/merge_requests

Alternatively, you can send patches to the `<EMAIL>`
mailing list  -- and thank you  in advance for your  work on improving
FreeType!

Details on the process can be found here:

  https://www.freetype.org/developer.html#patches


Enjoy!

  The FreeType Team

----------------------------------------------------------------------

Copyright (C) 2006-2022 by
David Turner, Robert Wilhelm, and Werner Lemberg.

This  file is  part of  the FreeType  project, and  may only  be used,
modified,  and distributed  under the  terms of  the  FreeType project
license,  LICENSE.TXT.  By  continuing to  use, modify,  or distribute
this file you  indicate that you have read  the license and understand
and accept it fully.


--- end of README ---
