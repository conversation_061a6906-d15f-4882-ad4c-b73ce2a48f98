﻿<?xml version="1.0" encoding="utf-8"?>
<!--
   PROJECT:   Mouri Internal Library Essentials
   FILE:      Mile.Project.targets
   PURPOSE:   Definition for Visual Studio C++ Project

   LICENSE:   The MIT License

   DEVELOPER: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON>_Naru<PERSON> AT Outlook.com)
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <!--
      Only do this for MSBuild versions below 16.0 as it is since done 
      automatically, see https://github.com/microsoft/msbuild/pull/3605.
    -->
    <MSBuildAllProjects Condition="'$(MSBuildToolsVersion)' &lt;= '15'">$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
  </PropertyGroup>
  <Target Name="MileProjectBuildManifestResources" BeforeTargets="BeforeResourceCompile">
    <ItemGroup Condition="'$(MileProjectType)'!='StaticLibrary'">
      <ResourceCompile Condition="'$(MileProjectManifestFile)'!=''" Include="$(MSBuildThisFileDirectory)Mile.Project.Manifest.rc" />
      <ResourceCompile Condition="Exists('$(ProjectDir)Mile.Project.Properties.h')" Include="$(MSBuildThisFileDirectory)Mile.Project.Version.rc" />
    </ItemGroup>
  </Target>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
</Project>