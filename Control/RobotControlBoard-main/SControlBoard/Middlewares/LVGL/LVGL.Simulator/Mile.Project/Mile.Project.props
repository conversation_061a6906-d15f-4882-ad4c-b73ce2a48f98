﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  PROJECT:   Mouri Internal Library Essentials
  FILE:      Mile.Project.props
  PURPOSE:   Definition for Visual Studio C++ Project

  LICENSE:   The MIT License

  DEVELOPER: <PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON><PERSON> AT Outlook.com)
-->
<!--
  The documents of specific options in Mile.Project.

  How to define the project type in the "Globals" label property group.
  If the project is a console application project.
    <MileProjectType>ConsoleApplication</MileProjectType>
  If the project is a windows application project.
    <MileProjectType>WindowsApplication</MileProjectType>
  If the project is a dynamic library project.
    <MileProjectType>DynamicLibrary</MileProjectType>
  If the project is a static library project.
    <MileProjectType>StaticLibrary</MileProjectType>

  How to define the manifest file in the "Globals" label property group.
    <MileProjectManifestFile>C:\Folder\Manifest.manifest</MileProjectManifestFile>

  How to enable the version information support.
    Copy Mile.Project.Properties.Template.h to the project folder and rename it
    to Mile.Project.Properties.h.
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <!--
      Only do this for MSBuild versions below 16.0 as it is since done 
      automatically, see https://github.com/microsoft/msbuild/pull/3605.
    -->
    <MSBuildAllProjects Condition="'$(MSBuildToolsVersion)' &lt;= '15'">$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
  </PropertyGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <ConfigurationType Condition="'$(MileProjectType)'=='DynamicLibrary'">DynamicLibrary</ConfigurationType>
    <ConfigurationType Condition="'$(MileProjectType)'=='StaticLibrary'">StaticLibrary</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '16.0'">v142</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '15.0'">v141</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '14.0'">v140</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <UseDebugLibraries Condition="'$(Configuration)'=='Debug'">true</UseDebugLibraries>
    <UseDebugLibraries Condition="'$(Configuration)'=='Release'">false</UseDebugLibraries>
    <WholeProgramOptimization Condition="'$(Configuration)'=='Release'">true</WholeProgramOptimization>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <PropertyGroup>
    <LinkIncremental Condition="'$(Configuration)'=='Debug'">true</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)'=='Release'">false</LinkIncremental>
    <GenerateManifest>false</GenerateManifest>
    <IncludePath>$(MSBuildThisFileDirectory);$(IncludePath)</IncludePath>
  </PropertyGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <SDLCheck>true</SDLCheck>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <EnableEnhancedInstructionSet Condition="'$(Platform)'=='Win32'">NoExtensions</EnableEnhancedInstructionSet>
      <PreprocessorDefinitions Condition="'$(Platform)'=='Win32'">WIN32;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)'=='Debug'">_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)'=='Debug'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)'=='Release'">NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)'=='Release'">MaxSpeed</Optimization>
      <FunctionLevelLinking Condition="'$(Configuration)'=='Release'">true</FunctionLevelLinking>
      <IntrinsicFunctions Condition="'$(Configuration)'=='Release'">true</IntrinsicFunctions>
      <PreprocessorDefinitions Condition="'$(MileProjectType)'=='ConsoleApplication'">_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(MileProjectType)'=='WindowsApplication'">_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(MileProjectType)'=='DynamicLibrary'">_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(MileProjectType)'=='StaticLibrary'">_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding Condition="'$(Configuration)'=='Release'">true</EnableCOMDATFolding>
      <OptimizeReferences Condition="'$(Configuration)'=='Release'">true</OptimizeReferences>
      <SubSystem Condition="'$(MileProjectType)'=='ConsoleApplication'">Console</SubSystem>
      <SubSystem Condition="'$(MileProjectType)'=='WindowsApplication'">Windows</SubSystem>
      <SubSystem Condition="'$(MileProjectType)'=='DynamicLibrary'">Windows</SubSystem>
      <SubSystem Condition="'$(MileProjectType)'=='StaticLibrary'"></SubSystem>
    </Link>
    <ResourceCompile Condition="'$(MileProjectType)'!='StaticLibrary'">
      <PreprocessorDefinitions Condition="'$(MileProjectType)'!='DynamicLibrary'">_MILE_PROJECT_MANIFEST_ID=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(MileProjectType)'=='DynamicLibrary'">_MILE_PROJECT_MANIFEST_ID=2;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(MileProjectManifestFile)'!=''">_MILE_PROJECT_MANIFEST_FILE=$([System.String]::Copy('$(MileProjectManifestFile)').Replace('\','\\'));%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(ProjectDir);$(MSBuildThisFileDirectory);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
</Project>
