﻿<?xml version="1.0" encoding="utf-8"?>
<!--
   PROJECT:   Mouri Internal Library Essentials
   FILE:      Mile.Project.Runtime.VC-LTL.props
   PURPOSE:   Definition for Visual Studio C++ Project

   LICENSE:   The MIT License

   DEVELOPER: <PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON>uri_Naruto AT Outlook.com)
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemDefinitionGroup>
    <ClCompile>
      <RuntimeLibrary Condition="'$(Configuration)'=='Debug'">MultiThreadedDebug</RuntimeLibrary>
      <RuntimeLibrary Condition="'$(Configuration)'=='Release'">MultiThreaded</RuntimeLibrary>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <PackageReference Include="VC-LTL">
      <Version>5.0.4-Beta3</Version>
    </PackageReference>
  </ItemGroup>
</Project>