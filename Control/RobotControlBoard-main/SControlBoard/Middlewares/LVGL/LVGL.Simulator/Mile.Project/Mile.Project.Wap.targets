﻿<?xml version="1.0" encoding="utf-8"?>
<!--
   PROJECT:   Mouri Internal Library Essentials
   FILE:      Mile.Project.Wap.targets
   PURPOSE:   Definition for Visual Studio Windows Packaging Project

   LICENSE:   The MIT License

   DEVELOPER: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON><PERSON> AT Outlook.com)
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(WapProjPath)\Microsoft.DesktopBridge.targets" />
  <!-- ## BEGIN HACK - Put all output files to the AppX root folder ## -->
  <!--
    Reference: https://github.com/microsoft/terminal
               /blob/a89c3e2f8527a51b39768980627ed8f7c1ea5f0b
               /src/cascadia/CascadiaPackage/CascadiaPackage.wapproj#L73
  -->
  <!-- 
    For our builds, we want to put all output files to the AppX root folder for
    simplifying the implementation.
  -->
  <Target Name="MileProjectStompSourceProjectForWapProject" BeforeTargets="_ConvertItems">
    <ItemGroup>
      <!--
        Stomp all "SourceProject" values for all incoming dependencies to 
        flatten the package. 
      -->
      <_TemporaryFilteredWapProjOutput Include="@(_FilteredNonWapProjProjectOutput)" />
      <_FilteredNonWapProjProjectOutput Remove="@(_TemporaryFilteredWapProjOutput)" />
      <_FilteredNonWapProjProjectOutput Include="@(_TemporaryFilteredWapProjOutput)">
        <!--
        Blank the SourceProject here to vend all files into the root of the
        package.
      -->
        <SourceProject>
        </SourceProject>
      </_FilteredNonWapProjProjectOutput>
    </ItemGroup>
  </Target>
  <!-- ## END HACK - Put all output files to the AppX root folder ## -->
  <!-- ## BEGIN HACK - Removing Microsoft.VCLibs Packages ## -->
  <!--
    Reference: https://github.com/microsoft/terminal
               /blob/a89c3e2f8527a51b39768980627ed8f7c1ea5f0b
               /src/cascadia/CascadiaPackage/CascadiaPackage.wapproj#L125
  -->
  <!-- 
    For our builds, we're just using VC-LTL to compile the project and delete
    the package dependencies. We don't want to rely on the Microsoft.VCLibs 
    packages. Because it's very difficult for users who do not have access to 
    the store to get our dependency packages, and we want to be robust and 
    deployable everywhere.
  -->
  <!--
    This target removes the FrameworkSdkReferences from before the AppX package
    targets manifest generation happens. This is part of the generic machinery 
    that applies to every AppX. 
  -->
  <Target Name="MileProjectStripAllDependenciesFromPackageFirstManifest" BeforeTargets="_GenerateCurrentProjectAppxManifest">
    <ItemGroup>
      <FrameworkSdkReference Remove="@(FrameworkSdkReference)" />
    </ItemGroup>
  </Target>
  <!--
    This target removes the FrameworkSdkPackages from before the *desktop 
    bridge* manifest generation happens. 
  -->
  <Target Name="MileProjectStripAllDependenciesFromPackageSecondManifest" BeforeTargets="_GenerateDesktopBridgeAppxManifest" DependsOnTargets="_ResolveVCLibDependencies">
    <ItemGroup>
      <FrameworkSdkPackage Remove="@(FrameworkSdkPackage)" />
    </ItemGroup>
  </Target>
  <!-- ## END HACK - Removing Microsoft.VCLibs Packages ## -->
</Project>