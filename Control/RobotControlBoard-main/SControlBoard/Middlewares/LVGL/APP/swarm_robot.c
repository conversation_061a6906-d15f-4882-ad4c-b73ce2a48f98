#include "swarm_robot.h"
#ifndef _WIN32
#include "lv_port_disp.h"
#include "bsp.h"
#include "arm_math.h"
#include "bsp_battery.h"
#include "sensor.h"
#include "robot.h"
#include "bsp_print.h"
#else
#pragma execution_character_set("utf-8") 
#include "lv_drivers/win32drv/win32drv.h"
#include "math.h"
#endif

LV_FONT_DECLARE(num_font_72)
//static lv_group_t *g;

lv_obj_t* t1_label2;
lv_obj_t* table1;
lv_obj_t* chart;
lv_obj_t* label_wave_x;
lv_obj_t* label_wave_y;

void mlv_tile1_init(lv_obj_t* tv);
void mlv_tile2_init(lv_obj_t* tv);
void mlv_tile3_init(lv_obj_t* tv);
void mlv_tile4_init(lv_obj_t* tv);

#define MAX_TILE    3

/**
 * @brief 显示数据刷新定时器
 * @param timer 结构体指针
*/
static void mlv_timer_callback(lv_timer_t* timer)
{
    static uint32_t tick = 0;
    static float value = 0, value1 = 0;
    tick++;
#ifndef _WIN32
    static float i = 0;
    i = i + 0.02f;
    if (tick % 10 == 0)//10hz
    {
		lv_label_set_text_fmt(t1_label2, "%d",GetRobotID());
        /* 电池电压 */
        lv_table_set_cell_value_fmt(table1, 0, 1, "%.2f", Sensor_GetBattery().voltage);      
        /* vx */
        lv_table_set_cell_value_fmt(table1, 1, 1, "%.2f", Sensor_GetOdometry().vx);
        /* vy */
        lv_table_set_cell_value_fmt(table1, 2, 1, "%.2f", Sensor_GetOdometry().vy);
        
        /* vicon */
        if(Sensor_GetStatus().VICON_Status == SENSOR_OK)
        {
            lv_table_set_cell_value(table1, 3, 1, LV_SYMBOL_OK);   
        }
        else 
        {
            lv_table_set_cell_value(table1, 3, 1, LV_SYMBOL_CLOSE);  
        }
        /* IMU */
        lv_table_set_cell_value_fmt(table1, 4, 1, "y:%.2f a:%d", Sensor_GetIMU().yaw,Sensor_GetIMU().accuracy);
    }
    if (tick % 2 == 0)//50hz
    {
		value = GetViconData().PosX;
		value1 = GetViconData().PosY;
        /* 获取位置 */
        lv_label_set_text_fmt(label_wave_x, "x:%.2f", value);
        lv_label_set_text_fmt(label_wave_y, "y:%.2f", value1);
        lv_chart_set_next_value2(chart, lv_chart_get_series_next(chart, NULL), value, value1);
    }   
#else
    static float i = 0;
    i = i + 0.05f;
    //value = abs(100 * sin(i));
    if (tick % 10 == 0)//10hz
    {
        /* 改变表格参数 */
        lv_table_set_cell_value_fmt(table1, 0, 1, "%.3f", i);
        lv_label_set_text(t1_label2, "0");
    }
    if (tick % 2 == 0)//50hz
    {
		value = 3000 * sin(i);
		value1 = 3000 * cos(i);
        /* 获取位置 */
        lv_label_set_text_fmt(label_wave_x, "x:%.2f", value);
        lv_label_set_text_fmt(label_wave_y, "y:%.2f", value1);
        lv_chart_set_next_value2(chart, lv_chart_get_series_next(chart, NULL), value, value1);
    }   
#endif
}
/**
 * @brief 自动播放定时器
 * @param timer 结构体指针
*/
static void mlv_timer1_callback(lv_timer_t* timer)
{
    lv_obj_t* ctv = (lv_obj_t*)timer->user_data;
    static uint32_t tick = 0;
    static uint8_t page = 0;
    tick++;
    if (tick % 10 == 0)
    {
        if (page < MAX_TILE - 1)
        {
            page++;
        }
        else {
            page = 0;
        }
        lv_obj_set_tile_id(ctv, 0, page, LV_ANIM_ON);
    }   
}
/**
 * @brief 表格 画点函数美化
 * @param e 
*/
static void draw_event_cb(lv_event_t* e)
{
    lv_obj_draw_part_dsc_t* dsc = lv_event_get_draw_part_dsc(e);
    if (dsc->part == LV_PART_ITEMS) {
        lv_obj_t* obj = lv_event_get_target(e);
        lv_chart_series_t* ser = lv_chart_get_series_next(obj, NULL);
        uint32_t cnt = lv_chart_get_point_count(obj);
        /*Make older value more transparent*/
        dsc->rect_dsc->bg_opa = (LV_OPA_COVER * dsc->id) / (cnt - 1);

        /*Make smaller values blue, higher values red*/
        lv_coord_t* x_array = lv_chart_get_x_array(obj, ser);
        lv_coord_t* y_array = lv_chart_get_y_array(obj, ser);
        /*dsc->id is the tells drawing order, but we need the ID of the point being drawn.*/
        uint32_t start_point = lv_chart_get_x_start_point(obj, ser);
        uint32_t p_act = (start_point + dsc->id) % cnt; /*Consider start point to get the index of the array*/
        lv_opa_t x_opa = (x_array[p_act] * LV_OPA_50) / 4000;
        lv_opa_t y_opa = (y_array[p_act] * LV_OPA_50) / 4000;

        dsc->rect_dsc->bg_color = lv_color_mix(lv_palette_main(LV_PALETTE_RED),
            lv_palette_main(LV_PALETTE_BLUE),
            x_opa + y_opa);
    }
}
/**
 * @brief 初始化LVGL应用程序
*/
void LV_APPStart(void)
{
//    g = lv_group_create();
    lv_obj_t* tv = lv_tileview_create(lv_scr_act());

    mlv_tile1_init(tv);
    mlv_tile2_init(tv);
    mlv_tile3_init(tv);
    //mlv_tile4_init(tv);

    lv_timer_t* timer = lv_timer_create(mlv_timer_callback, 10, 0);
    lv_timer_t* timer1 = lv_timer_create(mlv_timer1_callback, 1000, tv);
#ifndef _WIN32
    //lv_indev_set_group(lv_indev_get(), g);
#else
    lv_indev_set_group(lv_win32_keypad_device_object, g);
#endif
}

/**
 * @brief 第一个页面 ID标识
 * @param tv 
*/
void mlv_tile1_init(lv_obj_t* tv)
{
    /*Tile1*/
    static lv_style_t style;
    lv_style_init(&style);
    lv_style_set_radius(&style, 5);
    lv_style_set_max_width(&style, lv_pct(30));
    lv_style_set_max_height(&style, lv_pct(15));
    lv_obj_t* obj;
    lv_obj_t* tile1 = lv_tileview_add_tile(tv, 0, 0, LV_DIR_TOP | LV_DIR_BOTTOM);
    obj = lv_obj_create(tile1);
    /* 禁用滚动条 */
    lv_obj_clear_flag(obj, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_add_style(obj, &style, 0);
    lv_obj_align(obj, LV_ALIGN_TOP_LEFT, 15, 10);
    lv_obj_t* label1 = lv_label_create(obj);
    lv_label_set_text(label1, "ID");
    lv_obj_center(label1);

    t1_label2 = lv_label_create(tv);
    lv_obj_set_style_text_font(t1_label2, &num_font_72, 0);
    lv_obj_align(t1_label2, LV_ALIGN_CENTER, 0, 0);
    lv_label_set_text(t1_label2, "");
}

/**
 * @brief 第二个页面 基本状态
 * @param tv
*/
void mlv_tile2_init(lv_obj_t* tv)
{
    /*Tile2*/
    static lv_style_t style;
    lv_style_init(&style);
    lv_style_set_radius(&style, 5);
    lv_style_set_max_width(&style, lv_pct(30));
    lv_style_set_max_height(&style, lv_pct(15));
    lv_obj_t* obj;
    /*Tile2*/
    lv_obj_t* tile2 = lv_tileview_add_tile(tv, 0, 1, LV_DIR_TOP | LV_DIR_BOTTOM);
    obj = lv_obj_create(tile2);
    /* 禁用滚动条 */
    lv_obj_clear_flag(obj, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_add_style(obj, &style, 0);
    lv_obj_align(obj, LV_ALIGN_TOP_LEFT, 15, 10);
    lv_obj_t* label2 = lv_label_create(obj);
    lv_label_set_text(label2, "Status");
    lv_obj_center(label2);

    table1 = lv_table_create(tile2);
    lv_table_set_cell_value(table1, 0, 0, "voltage");
    lv_table_set_cell_value(table1, 1, 0, "vel_x");
    lv_table_set_cell_value(table1, 2, 0, "vel_y");
    lv_table_set_cell_value(table1, 3, 0, "vicon");
    lv_table_set_cell_value(table1, 4, 0, "imu");

    lv_table_set_cell_value(table1, 0, 1, "0");
    lv_table_set_cell_value(table1, 1, 1, "0");
    lv_table_set_cell_value(table1, 2, 1, "0");
    lv_table_set_cell_value(table1, 3, 1, LV_SYMBOL_OK);
    lv_table_set_cell_value(table1, 4, 1, "y:0 a:0");

    lv_obj_add_flag(table1, LV_OBJ_FLAG_SCROLL_ON_FOCUS);
    lv_obj_set_size(table1, lv_pct(90), lv_pct(75));
    lv_table_set_col_width(table1, 0, 100);
    /* 隐藏滚动条显示 */
    lv_obj_set_style_bg_opa(table1, LV_OPA_0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(table1, LV_OPA_0, LV_PART_SCROLLBAR | LV_STATE_SCROLLED);
    lv_obj_align(table1, LV_ALIGN_BOTTOM_MID, 0, -10);
}
/**
 * @brief 第三个页面 图标
 * @param tv
*/
void mlv_tile3_init(lv_obj_t* tv)
{
    static lv_style_t style;
    lv_style_init(&style);
    lv_style_set_radius(&style, 5);
    lv_style_set_max_width(&style, lv_pct(30));
    lv_style_set_max_height(&style, lv_pct(15));
    lv_obj_t* obj;
    /*Tile3*/
    lv_obj_t* tile3 = lv_tileview_add_tile(tv, 0, 2, LV_DIR_TOP | LV_DIR_BOTTOM);
    obj = lv_obj_create(tile3);
    /* 禁用滚动条 */
    lv_obj_clear_flag(obj, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_add_style(obj, &style, 0);
    lv_obj_align(obj, LV_ALIGN_TOP_LEFT, 15, 10);
    lv_obj_t* label3 = lv_label_create(obj);
    lv_label_set_text(label3, "Pos");
    lv_obj_center(label3);

    /* 构建一个波形图 用来显示地图 */
    chart = lv_chart_create(tile3);
    lv_obj_set_size(chart, lv_pct(90), lv_pct(75));
    /* Draw points and lines in 2D 打点模式 */
    lv_chart_set_type(chart, LV_CHART_TYPE_SCATTER);
    /* 设置显示范围 */
    lv_chart_set_range(chart, LV_CHART_AXIS_PRIMARY_X, -4000, 4000);
    lv_chart_set_range(chart, LV_CHART_AXIS_PRIMARY_Y, -4000, 4000);
    lv_obj_align(chart, LV_ALIGN_BOTTOM_MID, 0, -10);
    /* 点个数 */
    lv_obj_add_event_cb(chart, draw_event_cb, LV_EVENT_DRAW_PART_BEGIN, NULL);
    lv_chart_set_point_count(chart, 10);
    lv_obj_set_style_line_width(chart, 0, LV_PART_ITEMS);

    lv_chart_series_t* ser1 = lv_chart_add_series(chart, lv_palette_main(LV_PALETTE_RED), LV_CHART_AXIS_PRIMARY_Y);
    /* 显示参数标签 */
    label_wave_x = lv_label_create(tile3);
    lv_label_set_text(label_wave_x, "");
    lv_obj_align_to(label_wave_x, chart, LV_ALIGN_OUT_TOP_MID, 0, -25);

    label_wave_y = lv_label_create(tile3);
    lv_label_set_text(label_wave_y, "");
    lv_obj_align_to(label_wave_y, chart, LV_ALIGN_OUT_TOP_MID, 0, -10);
}
/**
 * @brief 第四个页面 保留
 * @param tv 
*/
void mlv_tile4_init(lv_obj_t* tv)
{
    static lv_style_t style;
    lv_style_init(&style);
    lv_style_set_radius(&style, 5);
    lv_style_set_max_width(&style, lv_pct(30));
    lv_style_set_max_height(&style, lv_pct(15));
    lv_obj_t* obj;
    /*Tile4*/
    lv_obj_t* tile4 = lv_tileview_add_tile(tv, 0, 3, LV_DIR_TOP | LV_DIR_BOTTOM);

    obj = lv_obj_create(tile4);
    /* 禁用滚动条 */
    lv_obj_clear_flag(obj, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_add_style(obj, &style, 0);
    lv_obj_align(obj, LV_ALIGN_TOP_LEFT, 15, 10);
    lv_obj_t* label4 = lv_label_create(obj);
    lv_label_set_text(label4, "T4");
    lv_obj_center(label4);
}
