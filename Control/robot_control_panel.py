#!/usr/bin/env python3
"""
机器人控制面板

提供图形界面来控制和监控机器人
支持STM32控制板和本地模拟器
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import sys
import os
from typing import Optional

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

class RobotControlPanel:
    """机器人控制面板"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("机器人控制面板")
        self.root.geometry("800x600")
        
        # 控制器
        self.robot = None
        self.controller_type = "none"
        self.connected = False
        
        # 监控线程
        self.monitoring = False
        self.monitor_thread = None
        
        # 创建界面
        self.create_widgets()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 连接控制区域
        self.create_connection_frame(main_frame)
        
        # 控制按钮区域
        self.create_control_frame(main_frame)
        
        # 状态显示区域
        self.create_status_frame(main_frame)
        
        # 日志显示区域
        self.create_log_frame(main_frame)
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
    
    def create_connection_frame(self, parent):
        """创建连接控制框架"""
        conn_frame = ttk.LabelFrame(parent, text="连接控制", padding="5")
        conn_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 控制器类型选择
        ttk.Label(conn_frame, text="控制器类型:").grid(row=0, column=0, sticky=tk.W)
        self.controller_var = tk.StringVar(value="auto")
        controller_combo = ttk.Combobox(conn_frame, textvariable=self.controller_var, 
                                       values=["auto", "stm32", "local"], state="readonly")
        controller_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 10))
        
        # 连接按钮
        self.connect_btn = ttk.Button(conn_frame, text="连接", command=self.connect_robot)
        self.connect_btn.grid(row=0, column=2, padx=(0, 5))
        
        self.disconnect_btn = ttk.Button(conn_frame, text="断开", command=self.disconnect_robot, state="disabled")
        self.disconnect_btn.grid(row=0, column=3)
        
        # 状态指示
        self.status_label = ttk.Label(conn_frame, text="未连接", foreground="red")
        self.status_label.grid(row=0, column=4, padx=(10, 0))
        
        conn_frame.columnconfigure(1, weight=1)
    
    def create_control_frame(self, parent):
        """创建控制按钮框架"""
        control_frame = ttk.LabelFrame(parent, text="运动控制", padding="5")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N), pady=(0, 10))
        
        # 方向控制按钮
        ttk.Button(control_frame, text="⬆️ 前进", command=lambda: self.move_command("forward")).grid(row=0, column=1, padx=2, pady=2)
        ttk.Button(control_frame, text="⬅️ 左转", command=lambda: self.move_command("left")).grid(row=1, column=0, padx=2, pady=2)
        ttk.Button(control_frame, text="🛑 停止", command=lambda: self.move_command("stop")).grid(row=1, column=1, padx=2, pady=2)
        ttk.Button(control_frame, text="➡️ 右转", command=lambda: self.move_command("right")).grid(row=1, column=2, padx=2, pady=2)
        ttk.Button(control_frame, text="⬇️ 后退", command=lambda: self.move_command("backward")).grid(row=2, column=1, padx=2, pady=2)
        
        # 侧移按钮
        ttk.Button(control_frame, text="↖️ 左前", command=lambda: self.move_command("forward_left")).grid(row=0, column=0, padx=2, pady=2)
        ttk.Button(control_frame, text="↗️ 右前", command=lambda: self.move_command("forward_right")).grid(row=0, column=2, padx=2, pady=2)
        ttk.Button(control_frame, text="↙️ 左后", command=lambda: self.move_command("backward_left")).grid(row=2, column=0, padx=2, pady=2)
        ttk.Button(control_frame, text="↘️ 右后", command=lambda: self.move_command("backward_right")).grid(row=2, column=2, padx=2, pady=2)
        
        # 参数控制
        param_frame = ttk.Frame(control_frame)
        param_frame.grid(row=3, column=0, columnspan=3, pady=(10, 0), sticky=(tk.W, tk.E))
        
        ttk.Label(param_frame, text="速度:").grid(row=0, column=0, sticky=tk.W)
        self.speed_var = tk.DoubleVar(value=0.25)
        speed_scale = ttk.Scale(param_frame, from_=0.1, to=0.5, variable=self.speed_var, orient=tk.HORIZONTAL)
        speed_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 10))
        self.speed_label = ttk.Label(param_frame, text="0.25")
        self.speed_label.grid(row=0, column=2)
        
        ttk.Label(param_frame, text="持续时间:").grid(row=1, column=0, sticky=tk.W)
        self.duration_var = tk.DoubleVar(value=1.0)
        duration_scale = ttk.Scale(param_frame, from_=0.1, to=3.0, variable=self.duration_var, orient=tk.HORIZONTAL)
        duration_scale.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(5, 10))
        self.duration_label = ttk.Label(param_frame, text="1.0")
        self.duration_label.grid(row=1, column=2)
        
        # 绑定滑块事件
        speed_scale.configure(command=lambda v: self.speed_label.configure(text=f"{float(v):.2f}"))
        duration_scale.configure(command=lambda v: self.duration_label.configure(text=f"{float(v):.1f}"))
        
        param_frame.columnconfigure(1, weight=1)
    
    def create_status_frame(self, parent):
        """创建状态显示框架"""
        status_frame = ttk.LabelFrame(parent, text="状态监控", padding="5")
        status_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N), pady=(0, 10), padx=(10, 0))
        
        # 监控开关
        self.monitor_var = tk.BooleanVar()
        monitor_check = ttk.Checkbutton(status_frame, text="启用监控", variable=self.monitor_var, command=self.toggle_monitoring)
        monitor_check.grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))
        
        # 状态显示
        self.status_text = scrolledtext.ScrolledText(status_frame, height=15, width=40)
        self.status_text.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        status_frame.columnconfigure(0, weight=1)
        status_frame.rowconfigure(1, weight=1)
    
    def create_log_frame(self, parent):
        """创建日志显示框架"""
        log_frame = ttk.LabelFrame(parent, text="操作日志", padding="5")
        log_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清除日志按钮
        ttk.Button(log_frame, text="清除日志", command=self.clear_log).grid(row=1, column=0, pady=(5, 0))
        
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
    
    def clear_log(self):
        """清除日志"""
        self.log_text.delete(1.0, tk.END)
    
    def connect_robot(self):
        """连接机器人"""
        controller_type = self.controller_var.get()
        
        try:
            if controller_type == "auto":
                from vision_robot_controller import VisionRobotController
                self.robot = VisionRobotController(prefer_hardware=True)
            elif controller_type == "stm32":
                from stm32_robot_controller import auto_connect_robot
                stm32_robot = auto_connect_robot(timeout=3.0)
                if stm32_robot:
                    self.robot = stm32_robot
                    self.controller_type = "stm32"
                else:
                    raise ConnectionError("无法连接到STM32控制板")
            elif controller_type == "local":
                from local_robot_controller import get_controller
                self.robot = get_controller()
                self.controller_type = "local"
            
            if self.robot:
                self.connected = True
                self.connect_btn.configure(state="disabled")
                self.disconnect_btn.configure(state="normal")
                self.status_label.configure(text=f"已连接 ({self.controller_type})", foreground="green")
                self.log_message(f"✅ 成功连接到{self.controller_type}控制器")
            else:
                raise ConnectionError("连接失败")
                
        except Exception as e:
            messagebox.showerror("连接错误", f"连接失败: {e}")
            self.log_message(f"❌ 连接失败: {e}")
    
    def disconnect_robot(self):
        """断开机器人连接"""
        if self.robot:
            if hasattr(self.robot, 'disconnect'):
                self.robot.disconnect()
            self.robot = None
        
        self.connected = False
        self.connect_btn.configure(state="normal")
        self.disconnect_btn.configure(state="disabled")
        self.status_label.configure(text="未连接", foreground="red")
        self.log_message("🔌 已断开连接")
        
        # 停止监控
        if self.monitoring:
            self.monitor_var.set(False)
            self.toggle_monitoring()
    
    def move_command(self, direction):
        """执行移动命令"""
        if not self.connected or not self.robot:
            messagebox.showwarning("警告", "请先连接机器人")
            return
        
        speed = self.speed_var.get()
        duration = self.duration_var.get()
        
        try:
            if direction == "forward":
                if hasattr(self.robot, 'move_forward'):
                    self.robot.move_forward(speed, duration)
                else:
                    self.robot.send_command('move_forward', {'speed': speed, 'duration': duration})
                self.log_message(f"🚗 前进: 速度={speed:.2f}, 时间={duration:.1f}s")
            
            elif direction == "backward":
                if hasattr(self.robot, 'move_backward'):
                    self.robot.move_backward(speed, duration)
                else:
                    self.robot.send_command('move_backward', {'speed': speed, 'duration': duration})
                self.log_message(f"🚗 后退: 速度={speed:.2f}, 时间={duration:.1f}s")
            
            elif direction == "left":
                if hasattr(self.robot, 'turn_left'):
                    self.robot.turn_left(speed*2, duration*0.5)
                else:
                    self.robot.send_command('turn_left', {'angular_speed': speed*2, 'duration': duration*0.5})
                self.log_message(f"🚗 左转: 角速度={speed*2:.2f}, 时间={duration*0.5:.1f}s")
            
            elif direction == "right":
                if hasattr(self.robot, 'turn_right'):
                    self.robot.turn_right(speed*2, duration*0.5)
                else:
                    self.robot.send_command('turn_right', {'angular_speed': speed*2, 'duration': duration*0.5})
                self.log_message(f"🚗 右转: 角速度={speed*2:.2f}, 时间={duration*0.5:.1f}s")
            
            elif direction == "stop":
                if hasattr(self.robot, 'stop_robot'):
                    self.robot.stop_robot()
                elif hasattr(self.robot, 'stop'):
                    self.robot.stop()
                else:
                    self.robot.send_command('stop')
                self.log_message("🛑 停止")
            
            # 其他方向的处理...
            
        except Exception as e:
            messagebox.showerror("控制错误", f"执行命令失败: {e}")
            self.log_message(f"❌ 命令执行失败: {e}")
    
    def toggle_monitoring(self):
        """切换监控状态"""
        if self.monitor_var.get():
            if self.connected and self.robot:
                self.monitoring = True
                self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
                self.monitor_thread.start()
                self.log_message("📊 开始状态监控")
            else:
                self.monitor_var.set(False)
                messagebox.showwarning("警告", "请先连接机器人")
        else:
            self.monitoring = False
            self.log_message("📊 停止状态监控")
    
    def monitor_loop(self):
        """监控循环"""
        while self.monitoring and self.connected:
            try:
                status_info = []
                
                # 获取基本状态
                if hasattr(self.robot, 'get_robot_status'):
                    status = self.robot.get_robot_status()
                    status_info.append("=== 基本状态 ===")
                    for key, value in status.items():
                        status_info.append(f"{key}: {value}")
                
                # 获取传感器数据
                if hasattr(self.robot, 'get_odometry_data'):
                    odometry = self.robot.get_odometry_data()
                    if odometry:
                        status_info.append("\n=== 里程计数据 ===")
                        status_info.append(f"速度: vx={odometry.vx:.3f}, vy={odometry.vy:.3f}, wz={odometry.wz:.3f}")
                        status_info.append(f"位移: x={odometry.dis_x:.3f}, y={odometry.dis_y:.3f}")
                        status_info.append(f"偏航角: {odometry.yaw_all:.3f}")
                
                if hasattr(self.robot, 'get_battery_data'):
                    battery = self.robot.get_battery_data()
                    if battery:
                        status_info.append("\n=== 电池数据 ===")
                        status_info.append(f"电压: {battery.voltage:.2f}V")
                        status_info.append(f"电流: {battery.current:.2f}A")
                        status_info.append(f"电量: {battery.percentage}%")
                        status_info.append(f"温度: {battery.temperature:.1f}°C")
                
                # 更新状态显示
                self.root.after(0, self.update_status_display, "\n".join(status_info))
                
            except Exception as e:
                self.root.after(0, self.log_message, f"❌ 监控错误: {e}")
            
            time.sleep(1.0)
    
    def update_status_display(self, text):
        """更新状态显示"""
        self.status_text.delete(1.0, tk.END)
        self.status_text.insert(1.0, text)
    
    def on_closing(self):
        """关闭窗口时的处理"""
        if self.connected:
            self.disconnect_robot()
        self.root.destroy()
    
    def run(self):
        """运行控制面板"""
        self.root.mainloop()


if __name__ == '__main__':
    try:
        panel = RobotControlPanel()
        panel.run()
    except Exception as e:
        print(f"启动控制面板失败: {e}")
        sys.exit(1)
