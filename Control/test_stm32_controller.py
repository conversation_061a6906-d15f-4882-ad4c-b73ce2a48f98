#!/usr/bin/env python3
"""
STM32机器人控制器测试脚本

测试与STM32控制板的通信和控制功能
"""

import time
import sys
import os
from typing import Optional

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

def test_stm32_connection():
    """测试STM32控制板连接"""
    print("🔍 测试STM32控制板连接...")
    
    try:
        from stm32_robot_controller import find_robot_ports, auto_connect_robot, ControlMode
        
        # 查找可用端口
        ports = find_robot_ports()
        print(f"📡 发现端口: {ports}")
        
        if not ports:
            print("❌ 未发现任何串口设备")
            return None
        
        # 尝试自动连接
        robot = auto_connect_robot(timeout=3.0)
        
        if robot:
            print("✅ 成功连接到STM32控制板")
            return robot
        else:
            print("❌ 无法连接到STM32控制板")
            return None
            
    except ImportError as e:
        print(f"❌ 导入STM32控制器模块失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return None

def test_basic_commands(robot):
    """测试基本控制命令"""
    print("\n🎮 测试基本控制命令...")
    
    try:
        from stm32_robot_controller import ControlMode
        
        # 设置控制模式
        print("📋 设置为速度控制模式...")
        if robot.set_control_mode(ControlMode.MOVE_CONTROL):
            print("✅ 控制模式设置成功")
        else:
            print("❌ 控制模式设置失败")
            return False
        
        time.sleep(0.5)
        
        # 测试基本移动命令
        test_commands = [
            ("前进", lambda: robot.move_forward(0.2, 1.0)),
            ("停止", lambda: robot.stop_robot()),
            ("后退", lambda: robot.move_backward(0.2, 1.0)),
            ("停止", lambda: robot.stop_robot()),
            ("左转", lambda: robot.turn_left(1.0, 0.5)),
            ("停止", lambda: robot.stop_robot()),
            ("右转", lambda: robot.turn_right(1.0, 0.5)),
            ("停止", lambda: robot.stop_robot()),
        ]
        
        for name, command in test_commands:
            print(f"🚗 执行: {name}")
            if command():
                print(f"✅ {name} 命令发送成功")
            else:
                print(f"❌ {name} 命令发送失败")
            time.sleep(0.5)
        
        return True
        
    except Exception as e:
        print(f"❌ 基本命令测试失败: {e}")
        return False

def test_sensor_data(robot):
    """测试传感器数据读取"""
    print("\n📊 测试传感器数据读取...")
    
    try:
        # 等待传感器数据
        print("⏳ 等待里程计数据...")
        if robot.wait_for_data('odometry', 5.0):
            odometry = robot.get_odometry_data()
            print(f"✅ 里程计数据: vx={odometry.vx:.3f}, vy={odometry.vy:.3f}, wz={odometry.wz:.3f}")
            print(f"   位移: x={odometry.dis_x:.3f}, y={odometry.dis_y:.3f}, yaw={odometry.yaw_all:.3f}")
        else:
            print("⚠️ 未收到里程计数据")
        
        print("⏳ 等待IMU数据...")
        if robot.wait_for_data('imu', 5.0):
            imu = robot.get_imu_data()
            print(f"✅ IMU数据: 加速度=({imu.accel_x:.2f}, {imu.accel_y:.2f}, {imu.accel_z:.2f})")
            print(f"   陀螺仪=({imu.gyro_x:.2f}, {imu.gyro_y:.2f}, {imu.gyro_z:.2f})")
            print(f"   温度={imu.temperature:.1f}°C")
        else:
            print("⚠️ 未收到IMU数据")
        
        print("⏳ 等待电池数据...")
        if robot.wait_for_data('battery', 5.0):
            battery = robot.get_battery_data()
            print(f"✅ 电池数据: 电压={battery.voltage:.2f}V, 电流={battery.current:.2f}A")
            print(f"   电量={battery.percentage}%, 温度={battery.temperature:.1f}°C")
        else:
            print("⚠️ 未收到电池数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 传感器数据测试失败: {e}")
        return False

def test_rgb_lights(robot):
    """测试RGB灯带控制"""
    print("\n💡 测试RGB灯带控制...")
    
    try:
        # 测试上灯带
        colors = [
            (255, 0, 0, 5),    # 红色，5个灯珠
            (0, 255, 0, 5),    # 绿色，5个灯珠
            (0, 0, 255, 5),    # 蓝色，5个灯珠
        ]
        
        print("🔴 设置上灯带为彩色...")
        if robot.set_rgb_light(True, colors):
            print("✅ 上灯带设置成功")
        else:
            print("❌ 上灯带设置失败")
        
        time.sleep(2.0)
        
        # 关闭灯带
        print("⚫ 关闭上灯带...")
        if robot.set_rgb_light(True, [(0, 0, 0, 15)]):
            print("✅ 上灯带关闭成功")
        else:
            print("❌ 上灯带关闭失败")
        
        return True
        
    except Exception as e:
        print(f"❌ RGB灯带测试失败: {e}")
        return False

def test_advanced_movements(robot):
    """测试高级移动功能"""
    print("\n🎯 测试高级移动功能...")
    
    try:
        # 测试组合移动
        print("🔄 测试组合移动...")
        
        # 正方形路径
        square_moves = [
            ("前进", lambda: robot.move_robot(0.2, 0.0, 0.0)),
            ("右转", lambda: robot.move_robot(0.0, 0.0, -1.57)),  # 90度
            ("前进", lambda: robot.move_robot(0.2, 0.0, 0.0)),
            ("右转", lambda: robot.move_robot(0.0, 0.0, -1.57)),
            ("前进", lambda: robot.move_robot(0.2, 0.0, 0.0)),
            ("右转", lambda: robot.move_robot(0.0, 0.0, -1.57)),
            ("前进", lambda: robot.move_robot(0.2, 0.0, 0.0)),
            ("右转", lambda: robot.move_robot(0.0, 0.0, -1.57)),
        ]
        
        for name, move in square_moves:
            print(f"📐 执行: {name}")
            move()
            time.sleep(1.0)
            robot.stop_robot()
            time.sleep(0.5)
        
        print("✅ 正方形路径完成")
        
        # 测试侧移（如果支持）
        print("↔️ 测试侧移...")
        robot.move_left(0.2, 1.0)
        time.sleep(0.5)
        robot.move_right(0.2, 1.0)
        time.sleep(0.5)
        
        print("✅ 高级移动测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 高级移动测试失败: {e}")
        return False

def test_status_monitoring(robot):
    """测试状态监控"""
    print("\n📈 测试状态监控...")
    
    try:
        status = robot.get_robot_status()
        print("📊 机器人状态:")
        for key, value in status.items():
            print(f"   {key}: {value}")
        
        # 监控一段时间的数据变化
        print("⏱️ 监控数据变化（10秒）...")
        start_time = time.time()
        
        while time.time() - start_time < 10:
            # 发送一个小的移动命令
            robot.move_robot(0.1, 0.0, 0.0)
            time.sleep(0.5)
            robot.stop_robot()
            
            # 检查里程计数据
            odometry = robot.get_odometry_data()
            if odometry:
                print(f"📍 位置: x={odometry.dis_x:.3f}, y={odometry.dis_y:.3f}, yaw={odometry.yaw_all:.3f}")
            
            time.sleep(1.0)
        
        print("✅ 状态监控测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 状态监控测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 STM32机器人控制器完整测试")
    print("=" * 50)
    
    # 连接测试
    robot = test_stm32_connection()
    if not robot:
        print("\n❌ 无法连接到STM32控制板，测试终止")
        return False
    
    try:
        # 测试结果
        results = {}
        
        # 1. 基本命令测试
        print("\n" + "="*30)
        results['basic_commands'] = test_basic_commands(robot)
        
        # 2. 传感器数据测试
        print("\n" + "="*30)
        results['sensor_data'] = test_sensor_data(robot)
        
        # 3. RGB灯带测试
        print("\n" + "="*30)
        results['rgb_lights'] = test_rgb_lights(robot)
        
        # 4. 高级移动测试
        print("\n" + "="*30)
        results['advanced_movements'] = test_advanced_movements(robot)
        
        # 5. 状态监控测试
        print("\n" + "="*30)
        results['status_monitoring'] = test_status_monitoring(robot)
        
        # 输出测试结果
        print("\n" + "=" * 50)
        print("📊 测试结果汇总:")
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
        
        all_passed = all(results.values())
        if all_passed:
            print("\n🎉 所有测试通过！STM32控制板工作正常。")
        else:
            print("\n⚠️ 部分测试失败，请检查硬件连接和配置。")
        
        return all_passed
        
    finally:
        # 确保停止机器人并断开连接
        print("\n🛑 停止机器人并断开连接...")
        robot.stop_robot()
        robot.disconnect()

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 测试过程中发生未预期的错误: {e}")
        sys.exit(1)
