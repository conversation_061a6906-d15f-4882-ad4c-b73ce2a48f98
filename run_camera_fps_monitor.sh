#!/bin/bash

# Multi-Camera FPS Monitor Launcher
# 多摄像头FPS监控器启动脚本

echo "🎥 Multi-Camera FPS Monitor (No AI Detection)"
echo "============================================="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 is not installed or not in PATH"
    exit 1
fi

# Check if required packages are available
echo "📦 Checking dependencies..."
python3 -c "import cv2, numpy, streamlit" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ Required packages not found. Installing..."
    pip3 install opencv-python numpy streamlit
fi

echo "✅ Dependencies OK"
echo ""

# Function to run the camera FPS monitor
run_camera_monitor() {
    echo "🚀 Starting Multi-Camera FPS Monitor..."
    echo "This application shows pure camera feeds with FPS monitoring"
    echo "No AI detection - just raw camera performance analysis"
    echo ""
    echo "🌐 The web interface will open at: http://localhost:8501"
    echo "Press Ctrl+C to stop"
    echo ""
    
    cd "$(dirname "$0")"
    python3 -m streamlit run streamlit_camera_fps_only.py
}

# Function to test cameras first
test_cameras_quick() {
    echo "🔍 Quick Camera Test..."
    echo "Testing cameras 0, 2, 4, 6 for availability"
    echo ""
    
    python3 -c "
import cv2
import time

cameras_to_test = [0, 2, 4, 6]
working_cameras = []

print('📹 Testing cameras...')
for cam_idx in cameras_to_test:
    try:
        cap = cv2.VideoCapture(cam_idx)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret and frame is not None:
                height, width = frame.shape[:2]
                working_cameras.append(cam_idx)
                print(f'✅ Camera {cam_idx}: Working ({width}x{height})')
            else:
                print(f'❌ Camera {cam_idx}: Cannot read frames')
            cap.release()
        else:
            print(f'❌ Camera {cam_idx}: Cannot open')
    except Exception as e:
        print(f'❌ Camera {cam_idx}: Error - {e}')
    time.sleep(0.1)

print(f'\n📊 Result: {len(working_cameras)}/{len(cameras_to_test)} cameras working')
if working_cameras:
    print(f'✅ Working cameras: {working_cameras}')
    print('🎉 Ready to start FPS monitoring!')
else:
    print('❌ No working cameras found!')
    print('💡 Check connections and try different camera indices')
"
}

# Function to show usage tips
show_tips() {
    echo "💡 Usage Tips for Multi-Camera FPS Monitor"
    echo "=========================================="
    echo ""
    echo "🎯 Purpose:"
    echo "  • Monitor pure camera FPS without AI processing overhead"
    echo "  • Test USB bandwidth limitations with multiple cameras"
    echo "  • Identify optimal camera configurations"
    echo "  • Baseline performance measurement"
    echo ""
    echo "⚙️ Recommended Settings:"
    echo "  • Start with 640x360 resolution for best FPS"
    echo "  • Use camera indices 0,2,4,6 (avoid adjacent numbers)"
    echo "  • Enable FPS overlay for real-time monitoring"
    echo "  • Monitor for at least 30 seconds for stable readings"
    echo ""
    echo "📊 Performance Expectations:"
    echo "  • Single camera: 25-30 FPS"
    echo "  • Two cameras: 20-25 FPS each"
    echo "  • Four cameras: 15-20 FPS each"
    echo "  • Higher resolutions will reduce FPS"
    echo ""
    echo "🔧 Troubleshooting:"
    echo "  • If FPS is low, try lower resolution"
    echo "  • If cameras fail to open, check USB connections"
    echo "  • If only some cameras work, that's normal due to USB bandwidth"
    echo "  • Close other camera applications before testing"
    echo ""
}

# Function to run with specific configuration
run_with_config() {
    echo "🎛️ Quick Configuration Setup"
    echo "============================"
    echo ""
    
    # Ask for number of cameras
    read -p "Number of cameras to test (1-8, default: 4): " num_cameras
    if [ -z "$num_cameras" ]; then
        num_cameras=4
    fi
    
    # Ask for resolution
    echo ""
    echo "Resolution options:"
    echo "1) 640x360 (recommended for multiple cameras)"
    echo "2) 640x480 (standard 4:3)"
    echo "3) 1280x720 (HD, may reduce FPS)"
    echo "4) Custom"
    echo ""
    read -p "Choose resolution (1-4, default: 1): " res_choice
    
    case $res_choice in
        2) resolution="640x480" ;;
        3) resolution="1280x720" ;;
        4) 
            read -p "Enter custom resolution (WIDTHxHEIGHT): " resolution
            ;;
        *) resolution="640x360" ;;
    esac
    
    echo ""
    echo "🎯 Configuration:"
    echo "  • Cameras: $num_cameras"
    echo "  • Resolution: $resolution"
    echo ""
    echo "💡 You can adjust these settings in the web interface after starting"
    echo ""
    
    run_camera_monitor
}

# Main menu
while true; do
    echo ""
    echo "🎯 Choose an option:"
    echo "1) Start Camera FPS Monitor (recommended)"
    echo "2) Test Camera Availability First"
    echo "3) Start with Quick Configuration"
    echo "4) Show Usage Tips"
    echo "5) Exit"
    echo ""
    read -p "Enter your choice (1-5): " choice
    
    case $choice in
        1)
            run_camera_monitor
            ;;
        2)
            test_cameras_quick
            ;;
        3)
            run_with_config
            ;;
        4)
            show_tips
            ;;
        5)
            echo "👋 Goodbye!"
            exit 0
            ;;
        *)
            echo "❌ Invalid choice. Please enter 1-5."
            ;;
    esac
    
    echo ""
    read -p "Press Enter to continue..."
done
