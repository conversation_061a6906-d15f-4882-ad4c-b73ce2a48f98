#!/bin/bash

# Multi-Camera FPS Testing Script
# 多摄像头FPS测试脚本

echo "🎥 Multi-Camera FPS Testing Suite"
echo "================================="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 is not installed or not in PATH"
    exit 1
fi

# Check if required packages are available
echo "📦 Checking dependencies..."
python3 -c "import cv2, numpy" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ Required packages not found. Installing..."
    pip3 install opencv-python numpy
fi

echo "✅ Dependencies OK"
echo ""

# Function to run quick test
run_quick_test() {
    echo "🚀 Running Quick FPS Test..."
    echo "This will test individual cameras and then test 4 cameras simultaneously"
    echo "Duration: ~30 seconds"
    echo ""
    python3 quick_fps_test.py
}

# Function to run detailed test
run_detailed_test() {
    echo "🔬 Running Detailed FPS Monitor..."
    echo "This provides comprehensive monitoring with real-time statistics"
    echo ""
    
    # Default parameters
    CAMERAS="0,2,4,6"
    RESOLUTION="640x360"
    DURATION="60"
    
    # Ask user for parameters
    read -p "Camera indices (default: $CAMERAS): " input_cameras
    if [ ! -z "$input_cameras" ]; then
        CAMERAS="$input_cameras"
    fi
    
    read -p "Resolution (default: $RESOLUTION): " input_resolution
    if [ ! -z "$input_resolution" ]; then
        RESOLUTION="$input_resolution"
    fi
    
    read -p "Duration in seconds (default: $DURATION): " input_duration
    if [ ! -z "$input_duration" ]; then
        DURATION="$input_duration"
    fi
    
    echo ""
    echo "🎯 Starting detailed monitoring with:"
    echo "  • Cameras: $CAMERAS"
    echo "  • Resolution: $RESOLUTION"
    echo "  • Duration: ${DURATION}s"
    echo ""
    
    python3 fps_monitor.py --cameras "$CAMERAS" --resolution "$RESOLUTION" --duration "$DURATION"
}

# Function to run streamlit app with FPS monitoring
run_streamlit_app() {
    echo "🌐 Running Streamlit App with FPS Monitoring..."
    echo "This will start the web interface with enhanced FPS monitoring"
    echo ""

    # Check if streamlit is installed
    python3 -c "import streamlit" 2>/dev/null
    if [ $? -ne 0 ]; then
        echo "📦 Installing Streamlit..."
        pip3 install streamlit
    fi

    echo "🚀 Starting Streamlit app..."
    echo "Open your browser and go to: http://localhost:8501"
    echo "Press Ctrl+C to stop"
    echo ""

    cd "$(dirname "$0")"
    python3 -m streamlit run ultralytics/solutions/streamlit_inference.py
}

# Function to run camera FPS monitor (no AI detection)
run_camera_fps_monitor() {
    echo "📹 Running Camera FPS Monitor (No AI Detection)..."
    echo "This shows pure camera feeds with FPS monitoring - no YOLO processing"
    echo ""

    # Check if streamlit is installed
    python3 -c "import streamlit" 2>/dev/null
    if [ $? -ne 0 ]; then
        echo "📦 Installing Streamlit..."
        pip3 install streamlit
    fi

    echo "🚀 Starting Camera FPS Monitor..."
    echo "Open your browser and go to: http://localhost:8501"
    echo "Press Ctrl+C to stop"
    echo ""

    cd "$(dirname "$0")"
    python3 -m streamlit run streamlit_camera_fps_only.py
}

# Function to run optimized camera monitor
run_optimized_monitor() {
    echo "⚡ Running Optimized Camera FPS Monitor..."
    echo "This uses aggressive optimizations to maximize FPS performance"
    echo ""

    # Check if streamlit is installed
    python3 -c "import streamlit" 2>/dev/null
    if [ $? -ne 0 ]; then
        echo "📦 Installing Streamlit..."
        pip3 install streamlit
    fi

    echo "🚀 Starting Optimized Monitor..."
    echo "Open your browser and go to: http://localhost:8501"
    echo "Press Ctrl+C to stop"
    echo ""

    cd "$(dirname "$0")"
    python3 -m streamlit run streamlit_camera_fps_optimized.py
}

# Function to run FPS optimization analysis
run_fps_optimization() {
    echo "🔧 Running FPS Optimization Analysis..."
    echo "This will analyze your cameras and suggest optimizations"
    echo ""

    python3 fps_optimization_tool.py
}

# Function to run quick FPS boost test
run_quick_fps_boost() {
    echo "⚡ Running Quick FPS Boost Test..."
    echo "This applies ultra-aggressive optimizations for maximum FPS"
    echo ""

    python3 quick_fps_boost.py
}

# Function to run custom optimized test based on user's hardware profile
run_custom_optimized() {
    echo "🎯 Running Custom Optimized Test..."
    echo "This uses settings optimized for YOUR specific hardware profile"
    echo "Based on test results: MJPEG helps, low buffer hurts, low resolution hurts"
    echo ""

    python3 custom_optimized_fps.py
}

# Function to run 640x360 demo
run_640x360_demo() {
    echo "🎬 Running 640x360 Resolution Demo..."
    echo "This will show multiple cameras in a 2x2 grid at 640x360 resolution"
    echo ""

    python3 demo_640x360.py
}

# Function to test resolution support
test_resolutions() {
    echo "🎯 Testing Resolution Support..."
    echo "This will test if cameras support different resolutions including 640x360"
    echo ""

    read -p "Test all resolutions? (y/n, default: n): " test_all
    if [[ "$test_all" =~ ^[Yy]$ ]]; then
        python3 test_640x360_resolution.py --all
    else
        python3 test_640x360_resolution.py
    fi
}

# Function to check camera availability
check_cameras() {
    echo "🔍 Checking Camera Availability..."
    echo "This will scan for available cameras on your system"
    echo ""
    
    python3 -c "
import cv2
import time

print('📹 Scanning for cameras...')
working_cameras = []

for i in range(8):
    try:
        cap = cv2.VideoCapture(i)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret and frame is not None:
                height, width = frame.shape[:2]
                working_cameras.append(i)
                print(f'✅ Camera {i}: Available ({width}x{height})')
            else:
                print(f'❌ Camera {i}: Cannot read frames')
            cap.release()
        else:
            print(f'❌ Camera {i}: Cannot open')
    except Exception as e:
        print(f'❌ Camera {i}: Error - {e}')
    time.sleep(0.1)

print(f'\n📋 Summary: {len(working_cameras)} working cameras found: {working_cameras}')

if len(working_cameras) >= 4:
    print('🎉 You have enough cameras for 4-camera testing!')
elif len(working_cameras) > 0:
    print(f'⚠️  Only {len(working_cameras)} cameras available. You can still test with fewer cameras.')
else:
    print('❌ No working cameras found!')
    print('💡 Troubleshooting:')
    print('  • Check USB connections')
    print('  • Close other camera applications')
    print('  • Try: lsusb | grep -i camera')
"
}

# Main menu
while true; do
    echo ""
    echo "🎯 Choose a test option:"
    echo "1) Quick FPS Test (recommended for first-time users)"
    echo "2) Detailed FPS Monitor (advanced monitoring)"
    echo "3) Camera FPS Monitor (pure camera feeds, no AI detection)"
    echo "4) ⚡ Optimized FPS Monitor (maximum performance)"
    echo "5) 🔧 FPS Optimization Analysis (diagnose issues)"
    echo "6) ⚡ Quick FPS Boost Test (ultra-optimized)"
    echo "7) 🎯 Custom Optimized Test (based on YOUR hardware)"
    echo "8) Streamlit App with FPS Monitoring (web interface with YOLO)"
    echo "9) 640x360 Resolution Demo (visual demo)"
    echo "10) Test Resolution Support (including 640x360)"
    echo "11) Check Camera Availability"
    echo "12) Exit"
    echo ""
    read -p "Enter your choice (1-12): " choice
    
    case $choice in
        1)
            run_quick_test
            ;;
        2)
            run_detailed_test
            ;;
        3)
            run_camera_fps_monitor
            ;;
        4)
            run_optimized_monitor
            ;;
        5)
            run_fps_optimization
            ;;
        6)
            run_quick_fps_boost
            ;;
        7)
            run_custom_optimized
            ;;
        8)
            run_streamlit_app
            ;;
        9)
            run_640x360_demo
            ;;
        10)
            test_resolutions
            ;;
        11)
            check_cameras
            ;;
        12)
            echo "👋 Goodbye!"
            exit 0
            ;;
        *)
            echo "❌ Invalid choice. Please enter 1-12."
            ;;
    esac
    
    echo ""
    read -p "Press Enter to continue..."
done
