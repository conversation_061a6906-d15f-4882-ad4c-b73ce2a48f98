#!/usr/bin/env python3
"""
Demo: 640x360 Resolution with Multiple Cameras
演示：640x360分辨率多摄像头

This script demonstrates the new 640x360 resolution option with multiple cameras.
"""

import cv2
import time
import threading
import numpy as np
from typing import Dict, List


class MultiCameraDemo:
    """Demo class for 640x360 multi-camera setup."""
    
    def __init__(self, camera_indices: List[int] = [0, 2, 4, 6]):
        """Initialize demo with camera indices."""
        self.camera_indices = camera_indices
        self.cameras: Dict[int, cv2.VideoCapture] = {}
        self.camera_frames: Dict[int, np.ndarray] = {}
        self.camera_fps: Dict[int, float] = {}
        self.running = False
        
        # Resolution settings
        self.width = 640
        self.height = 360
        
    def initialize_cameras(self) -> bool:
        """Initialize cameras with 640x360 resolution."""
        print(f"🎥 Initializing cameras with {self.width}x{self.height} resolution...")
        
        successful_cameras = 0
        
        for cam_idx in self.camera_indices:
            try:
                cap = cv2.VideoCapture(cam_idx)
                
                if cap.isOpened():
                    # Set properties
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
                    cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.width)
                    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.height)
                    
                    # Test capture
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        actual_height, actual_width = frame.shape[:2]
                        self.cameras[cam_idx] = cap
                        self.camera_frames[cam_idx] = frame
                        self.camera_fps[cam_idx] = 0.0
                        successful_cameras += 1
                        
                        print(f"  ✅ Camera {cam_idx}: {actual_width}x{actual_height}")
                    else:
                        cap.release()
                        print(f"  ❌ Camera {cam_idx}: Cannot read frames")
                else:
                    print(f"  ❌ Camera {cam_idx}: Cannot open")
                    
            except Exception as e:
                print(f"  ❌ Camera {cam_idx}: Error - {e}")
        
        print(f"📊 {successful_cameras}/{len(self.camera_indices)} cameras initialized")
        return successful_cameras > 0
    
    def capture_thread(self, cam_idx: int):
        """Capture frames from a single camera."""
        cap = self.cameras[cam_idx]
        frame_count = 0
        start_time = time.time()
        
        while self.running and cap.isOpened():
            ret, frame = cap.read()
            if ret:
                self.camera_frames[cam_idx] = frame
                frame_count += 1
                
                # Calculate FPS
                elapsed = time.time() - start_time
                if elapsed >= 1.0:
                    self.camera_fps[cam_idx] = frame_count / elapsed
                    frame_count = 0
                    start_time = time.time()
            
            time.sleep(0.03)  # ~30 FPS
    
    def create_grid_display(self) -> np.ndarray:
        """Create a grid display of all camera feeds."""
        if not self.camera_frames:
            return np.zeros((self.height * 2, self.width * 2, 3), dtype=np.uint8)
        
        # Create 2x2 grid
        grid = np.zeros((self.height * 2, self.width * 2, 3), dtype=np.uint8)
        
        positions = [
            (0, 0),                    # Top-left
            (0, self.width),           # Top-right
            (self.height, 0),          # Bottom-left
            (self.height, self.width)  # Bottom-right
        ]
        
        active_cameras = list(self.cameras.keys())
        
        for i, cam_idx in enumerate(active_cameras[:4]):  # Max 4 cameras in 2x2 grid
            if cam_idx in self.camera_frames:
                frame = self.camera_frames[cam_idx]
                if frame is not None:
                    # Resize frame to exact dimensions if needed
                    if frame.shape[:2] != (self.height, self.width):
                        frame = cv2.resize(frame, (self.width, self.height))
                    
                    # Add FPS text
                    fps = self.camera_fps.get(cam_idx, 0)
                    cv2.putText(frame, f"Cam {cam_idx}: {fps:.1f} FPS", 
                              (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                    
                    # Place in grid
                    y, x = positions[i]
                    grid[y:y+self.height, x:x+self.width] = frame
        
        return grid
    
    def run_demo(self, duration: float = 30.0):
        """Run the demo for specified duration."""
        if not self.initialize_cameras():
            print("❌ No cameras available for demo")
            return
        
        print(f"\n🚀 Starting 640x360 demo for {duration} seconds...")
        print("Press 'q' to quit early")
        
        # Start capture threads
        self.running = True
        threads = []
        
        for cam_idx in self.cameras.keys():
            thread = threading.Thread(target=self.capture_thread, args=(cam_idx,))
            thread.daemon = True
            thread.start()
            threads.append(thread)
        
        start_time = time.time()
        
        try:
            while time.time() - start_time < duration:
                # Create and display grid
                grid = self.create_grid_display()
                
                # Add overall info
                elapsed = time.time() - start_time
                remaining = duration - elapsed
                avg_fps = sum(self.camera_fps.values()) / len(self.camera_fps) if self.camera_fps else 0
                
                info_text = [
                    f"640x360 Multi-Camera Demo",
                    f"Time: {elapsed:.1f}s / {duration:.1f}s",
                    f"Cameras: {len(self.cameras)}",
                    f"Avg FPS: {avg_fps:.1f}"
                ]
                
                for i, text in enumerate(info_text):
                    cv2.putText(grid, text, (10, grid.shape[0] - 100 + i * 25), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                
                cv2.imshow("640x360 Multi-Camera Demo", grid)
                
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
                
                time.sleep(0.03)
        
        except KeyboardInterrupt:
            print("\n🛑 Demo stopped by user")
        
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Clean up resources."""
        print("\n🧹 Cleaning up...")
        self.running = False
        
        for cap in self.cameras.values():
            cap.release()
        
        cv2.destroyAllWindows()
        
        # Final stats
        if self.camera_fps:
            print(f"\n📊 Final FPS Statistics:")
            for cam_idx, fps in self.camera_fps.items():
                print(f"  Camera {cam_idx}: {fps:.1f} FPS")
            
            avg_fps = sum(self.camera_fps.values()) / len(self.camera_fps)
            print(f"  Average: {avg_fps:.1f} FPS")


def main():
    """Main function."""
    print("🎥 640x360 Multi-Camera Demo")
    print("=" * 40)
    print("This demo shows multiple cameras running at 640x360 resolution")
    print("Benefits of 640x360:")
    print("  • 16:9 widescreen aspect ratio")
    print("  • Lower bandwidth usage than 640x480")
    print("  • Better FPS performance")
    print("  • Suitable for streaming and monitoring")
    print("")
    
    # Get camera indices from user
    try:
        camera_input = input("Enter camera indices (default: 0,2,4,6): ").strip()
        if camera_input:
            camera_indices = [int(x.strip()) for x in camera_input.split(",")]
        else:
            camera_indices = [0, 2, 4, 6]
    except ValueError:
        print("❌ Invalid input, using default cameras: 0,2,4,6")
        camera_indices = [0, 2, 4, 6]
    
    # Get duration
    try:
        duration_input = input("Demo duration in seconds (default: 30): ").strip()
        duration = float(duration_input) if duration_input else 30.0
    except ValueError:
        print("❌ Invalid duration, using 30 seconds")
        duration = 30.0
    
    print(f"\n🎯 Starting demo with cameras: {camera_indices}")
    print(f"⏱️  Duration: {duration} seconds")
    
    # Run demo
    demo = MultiCameraDemo(camera_indices)
    demo.run_demo(duration)


if __name__ == "__main__":
    main()
