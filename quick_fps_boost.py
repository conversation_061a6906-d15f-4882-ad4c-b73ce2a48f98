#!/usr/bin/env python3
"""
Quick FPS Boost Test
快速FPS提升测试

Apply aggressive optimizations to maximize camera FPS.
"""

import cv2
import time
import threading
import sys
from typing import Dict, List
import numpy as np


def test_ultra_optimized_cameras(camera_indices: List[int] = [0, 2, 4, 6], duration: float = 15.0):
    """Test cameras with ultra-aggressive optimizations."""
    print("🚀 Ultra-Optimized Camera FPS Test")
    print("=" * 40)
    print(f"Testing cameras: {camera_indices}")
    print(f"Duration: {duration} seconds")
    print("Applying maximum optimizations...")
    print()
    
    cameras = {}
    camera_threads = {}
    camera_fps = {}
    camera_frame_counts = {}
    running = True
    
    # Initialize cameras with ultra optimizations
    print("📹 Initializing cameras with ultra optimizations...")
    for cam_idx in camera_indices:
        try:
            # Try V4L2 backend first (Linux)
            cap = cv2.VideoCapture(cam_idx, cv2.CAP_V4L2)
            if not cap.isOpened():
                cap = cv2.VideoCapture(cam_idx)
            
            if cap.isOpened():
                # Ultra-aggressive settings
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Minimal buffer
                cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))  # MJPEG compression
                
                # Lowest resolution for maximum FPS
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, 320)
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 240)
                
                # Maximum FPS setting
                cap.set(cv2.CAP_PROP_FPS, 60)
                
                # Disable auto features for speed
                cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)  # Manual exposure
                cap.set(cv2.CAP_PROP_AUTOFOCUS, 0)  # Disable autofocus
                cap.set(cv2.CAP_PROP_AUTO_WB, 0)  # Disable auto white balance
                
                # Test capture
                ret, frame = cap.read()
                if ret and frame is not None:
                    actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    actual_fps = cap.get(cv2.CAP_PROP_FPS)
                    
                    cameras[cam_idx] = cap
                    camera_fps[cam_idx] = 0.0
                    camera_frame_counts[cam_idx] = 0
                    
                    print(f"  ✅ Camera {cam_idx}: {actual_width}x{actual_height} @ {actual_fps} FPS")
                else:
                    cap.release()
                    print(f"  ❌ Camera {cam_idx}: Cannot read frames")
            else:
                print(f"  ❌ Camera {cam_idx}: Cannot open")
                
        except Exception as e:
            print(f"  ❌ Camera {cam_idx}: Error - {e}")
    
    if not cameras:
        print("❌ No cameras available!")
        return
    
    print(f"\n🚀 Starting ultra-optimized test with {len(cameras)} cameras...")
    
    def ultra_fast_capture_thread(cam_idx: int, cap: cv2.VideoCapture):
        """Ultra-fast capture thread with minimal overhead."""
        local_count = 0
        start_time = time.time()
        last_fps_update = start_time
        
        while running:
            try:
                ret, frame = cap.read()
                if ret:
                    local_count += 1
                    camera_frame_counts[cam_idx] += 1
                    
                    # Update FPS every 30 frames for minimal overhead
                    if local_count % 30 == 0:
                        current_time = time.time()
                        elapsed = current_time - last_fps_update
                        if elapsed > 0:
                            camera_fps[cam_idx] = 30 / elapsed
                        last_fps_update = current_time
                
                # Minimal delay - aim for maximum capture rate
                time.sleep(0.005)  # 5ms delay = theoretical 200 FPS max
                
            except Exception:
                time.sleep(0.01)
    
    # Start ultra-fast threads
    start_time = time.time()
    for cam_idx, cap in cameras.items():
        thread = threading.Thread(target=ultra_fast_capture_thread, args=(cam_idx, cap))
        thread.daemon = True
        thread.start()
        camera_threads[cam_idx] = thread
    
    # Monitor progress
    try:
        last_report = time.time()
        while time.time() - start_time < duration:
            current_time = time.time()
            
            # Report every 3 seconds
            if current_time - last_report >= 3.0:
                elapsed = current_time - start_time
                print(f"\n⏱️  Progress: {elapsed:.1f}s / {duration:.1f}s")
                
                for cam_idx in sorted(cameras.keys()):
                    fps = camera_fps.get(cam_idx, 0)
                    frames = camera_frame_counts.get(cam_idx, 0)
                    status = "🟢" if fps > 25 else "🟡" if fps > 15 else "🔴"
                    print(f"  {status} Camera {cam_idx}: {fps:6.1f} FPS ({frames:4d} frames)")
                
                last_report = current_time
            
            time.sleep(0.5)
    
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    
    finally:
        running = False
        
        # Wait for threads
        for thread in camera_threads.values():
            thread.join(timeout=0.5)
        
        # Final results
        total_time = time.time() - start_time
        print(f"\n📊 ULTRA-OPTIMIZED RESULTS ({total_time:.1f}s)")
        print("=" * 50)
        
        total_frames = 0
        fps_values = []
        
        for cam_idx in sorted(cameras.keys()):
            frames = camera_frame_counts.get(cam_idx, 0)
            fps = frames / total_time if total_time > 0 else 0
            total_frames += frames
            fps_values.append(fps)
            
            status = "🟢" if fps > 25 else "🟡" if fps > 15 else "🔴"
            print(f"  {status} Camera {cam_idx}: {fps:6.1f} FPS ({frames:5d} frames)")
        
        if fps_values:
            avg_fps = sum(fps_values) / len(fps_values)
            max_fps = max(fps_values)
            min_fps = min(fps_values)
            combined_fps = total_frames / total_time if total_time > 0 else 0
            
            print(f"\n📈 Summary:")
            print(f"  • Average FPS: {avg_fps:.1f}")
            print(f"  • Maximum FPS: {max_fps:.1f}")
            print(f"  • Minimum FPS: {min_fps:.1f}")
            print(f"  • Combined FPS: {combined_fps:.1f}")
            print(f"  • Total Frames: {total_frames:,}")
            
            # Performance assessment
            if avg_fps >= 25:
                print(f"  🎉 EXCELLENT: Ultra-high performance achieved!")
            elif avg_fps >= 20:
                print(f"  👍 VERY GOOD: High performance")
            elif avg_fps >= 15:
                print(f"  ✅ GOOD: Acceptable performance")
            elif avg_fps >= 10:
                print(f"  ⚠️  FAIR: Consider further optimizations")
            else:
                print(f"  ❌ POOR: Significant optimizations needed")
        
        # Cleanup
        for cap in cameras.values():
            cap.release()
        
        cv2.destroyAllWindows()


def test_progressive_optimization():
    """Test progressive optimization steps."""
    print("🔬 Progressive Optimization Test")
    print("=" * 35)
    
    cam_idx = 0  # Test with camera 0
    
    # Check if camera is available
    test_cap = cv2.VideoCapture(cam_idx)
    if not test_cap.isOpened():
        print(f"❌ Camera {cam_idx} not available")
        test_cap.release()
        return
    test_cap.release()
    
    optimizations = [
        {
            'name': 'Baseline',
            'settings': {}
        },
        {
            'name': 'MJPEG Codec',
            'settings': {'fourcc': cv2.VideoWriter_fourcc(*'MJPG')}
        },
        {
            'name': '+ Low Buffer',
            'settings': {
                'fourcc': cv2.VideoWriter_fourcc(*'MJPG'),
                'buffer': 1
            }
        },
        {
            'name': '+ Low Resolution',
            'settings': {
                'fourcc': cv2.VideoWriter_fourcc(*'MJPG'),
                'buffer': 1,
                'width': 320,
                'height': 240
            }
        },
        {
            'name': '+ High FPS Setting',
            'settings': {
                'fourcc': cv2.VideoWriter_fourcc(*'MJPG'),
                'buffer': 1,
                'width': 320,
                'height': 240,
                'fps': 60
            }
        },
        {
            'name': '+ Disable Auto Features',
            'settings': {
                'fourcc': cv2.VideoWriter_fourcc(*'MJPG'),
                'buffer': 1,
                'width': 320,
                'height': 240,
                'fps': 60,
                'auto_exposure': 0.25,
                'autofocus': 0
            }
        }
    ]
    
    results = []
    
    for opt in optimizations:
        print(f"\n🧪 Testing: {opt['name']}")
        
        try:
            cap = cv2.VideoCapture(cam_idx)
            if not cap.isOpened():
                print(f"  ❌ Cannot open camera")
                continue
            
            # Apply settings
            settings = opt['settings']
            if 'fourcc' in settings:
                cap.set(cv2.CAP_PROP_FOURCC, settings['fourcc'])
            if 'buffer' in settings:
                cap.set(cv2.CAP_PROP_BUFFERSIZE, settings['buffer'])
            if 'width' in settings:
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, settings['width'])
            if 'height' in settings:
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, settings['height'])
            if 'fps' in settings:
                cap.set(cv2.CAP_PROP_FPS, settings['fps'])
            if 'auto_exposure' in settings:
                cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, settings['auto_exposure'])
            if 'autofocus' in settings:
                cap.set(cv2.CAP_PROP_AUTOFOCUS, settings['autofocus'])
            
            # Test FPS
            frame_count = 0
            start_time = time.time()
            test_duration = 3.0
            
            while time.time() - start_time < test_duration:
                ret, frame = cap.read()
                if ret:
                    frame_count += 1
                time.sleep(0.005)  # Minimal delay
            
            elapsed = time.time() - start_time
            fps = frame_count / elapsed
            results.append((opt['name'], fps))
            
            print(f"  📊 Result: {fps:.1f} FPS")
            cap.release()
            
        except Exception as e:
            print(f"  ❌ Error: {e}")
            results.append((opt['name'], 0.0))
    
    # Summary
    print(f"\n📈 PROGRESSIVE OPTIMIZATION RESULTS")
    print("=" * 45)
    
    baseline_fps = results[0][1] if results else 0
    
    for name, fps in results:
        if baseline_fps > 0:
            improvement = ((fps - baseline_fps) / baseline_fps) * 100
            print(f"  {name:20s}: {fps:6.1f} FPS ({improvement:+5.1f}%)")
        else:
            print(f"  {name:20s}: {fps:6.1f} FPS")
    
    if results:
        best_result = max(results, key=lambda x: x[1])
        print(f"\n🏆 Best configuration: {best_result[0]} ({best_result[1]:.1f} FPS)")


def main():
    """Main function."""
    print("⚡ Quick FPS Boost Tool")
    print("=" * 25)
    
    choice = input("Choose test:\n1) Ultra-optimized multi-camera test\n2) Progressive optimization analysis\nChoice (1-2): ").strip()
    
    if choice == "2":
        test_progressive_optimization()
    else:
        # Multi-camera test
        try:
            camera_input = input("\nEnter camera indices (default: 0,2): ").strip()
            if camera_input:
                camera_indices = [int(x.strip()) for x in camera_input.split(",")]
            else:
                camera_indices = [0, 2]  # Start with just 2 cameras
        except ValueError:
            print("Invalid input, using default: 0,2")
            camera_indices = [0, 2]
        
        test_ultra_optimized_cameras(camera_indices, 15.0)


if __name__ == "__main__":
    main()
