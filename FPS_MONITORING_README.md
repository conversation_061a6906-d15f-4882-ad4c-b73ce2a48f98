# 四摄像头FPS监控指南 / 4-Camera FPS Monitoring Guide

本指南提供了多种方法来监控四个摄像头同时运行时的FPS性能。

## 🚀 快速开始 / Quick Start

### 方法1: 一键运行脚本 (推荐)
```bash
./run_fps_tests.sh
```

这个脚本提供了一个交互式菜单，包含所有测试选项。

### 方法2: 快速FPS测试
```bash
python3 quick_fps_test.py
```

### 方法3: 详细FPS监控
```bash
python3 fps_monitor.py --cameras 0,2,4,6 --duration 30
```

### 方法4: Streamlit Web界面 (带FPS监控)
```bash
python3 -m streamlit run ultralytics/solutions/streamlit_inference.py
```

## 📊 监控功能说明

### 1. 快速FPS测试 (`quick_fps_test.py`)
- **功能**: 快速测试多个摄像头的FPS性能
- **特点**: 
  - 自动检测可用摄像头
  - 实时FPS报告
  - 性能评估
- **适用**: 首次测试，快速诊断

### 2. 详细FPS监控 (`fps_monitor.py`)
- **功能**: 专业级FPS监控工具
- **特点**:
  - 可配置摄像头索引
  - 可配置分辨率
  - 滚动窗口FPS计算
  - 详细统计报告
- **参数**:
  ```bash
  --cameras 0,2,4,6        # 摄像头索引
  --resolution 640x360     # 分辨率
  --duration 30            # 监控时长(秒)
  --display               # 显示摄像头画面
  ```

### 3. Streamlit Web界面
- **功能**: 带FPS监控的Web界面
- **新增功能**:
  - 每个摄像头的实时FPS显示
  - 整体性能指标
  - 平均FPS计算
  - 处理帧数统计
- **分辨率选项**:
  - 640x360 (新增，16:9宽屏，适合多摄像头)
  - 640x480 (4:3标准)
  - 1280x720 (高清)
  - 1920x1080 (全高清)
  - 自定义分辨率

## 📈 FPS性能指标解读

### 理论最大值
- **单摄像头**: ~30 FPS (代码限制)
- **四摄像头**: 每个约15-25 FPS (受USB带宽限制)

### 性能等级
- 🟢 **优秀**: >20 FPS
- 🟡 **良好**: 15-20 FPS  
- 🟠 **一般**: 10-15 FPS
- 🔴 **较差**: <10 FPS

### 影响因素
1. **USB带宽限制**: 主要瓶颈
2. **CPU处理能力**: 推理计算负载
3. **分辨率设置**: 高分辨率降低FPS
4. **模型复杂度**: YOLO模型大小影响处理速度

## 🔧 优化建议

### 1. 硬件优化
- 使用USB 3.0接口
- 分散摄像头到不同USB控制器
- 使用专用USB Hub

### 2. 软件优化
- 降低分辨率 (640x360 或 640x480)
- 使用轻量模型 (yolo11n)
- 调整缓冲区大小
- 优化线程数量

### 3. 摄像头配置
```python
# 推荐设置
cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)           # 减少延迟
cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))  # 压缩格式
cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)        # 较低分辨率
cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 360)       # 16:9 宽屏比例，更适合多摄像头
```

## 🛠️ 故障排除

### 常见问题

1. **摄像头无法打开**
   ```bash
   # 检查可用摄像头
   ls /dev/video*
   # 或使用脚本检查
   python3 -c "import cv2; [print(f'Camera {i}: {cv2.VideoCapture(i).isOpened()}') for i in range(8)]"
   ```

2. **FPS过低**
   - 检查USB带宽使用情况
   - 降低分辨率
   - 关闭其他摄像头应用
   - 使用更快的存储设备

3. **摄像头冲突**
   - 确保没有其他应用使用摄像头
   - 重启摄像头服务
   - 检查权限设置

### 调试命令
```bash
# 检查USB设备
lsusb | grep -i camera

# 检查视频设备
v4l2-ctl --list-devices

# 检查摄像头格式
v4l2-ctl --device=/dev/video0 --list-formats-ext
```

## 📋 测试报告示例

```
📊 FPS Report (Elapsed: 10.0s)
==================================================
  🟢 Camera 0:   22.1 FPS
  🟢 Camera 2:   21.8 FPS
  🟡 Camera 4:   18.5 FPS
  🟡 Camera 6:   17.2 FPS

  📈 Average Camera FPS:   19.9
  🎯 Overall Processing FPS:   79.6
  📦 Total Frames: 796
```

## 🎯 使用建议

### 首次使用
1. 运行 `./run_fps_tests.sh`
2. 选择 "4) Check Camera Availability"
3. 然后选择 "1) Quick FPS Test"

### 日常监控
- 使用Streamlit界面进行实时监控
- 定期运行详细监控检查性能变化

### 性能调优
- 使用详细监控工具测试不同配置
- 记录最佳设置参数
- 监控长期稳定性

## 📞 技术支持

如果遇到问题，请提供以下信息：
- 系统信息 (`uname -a`)
- Python版本 (`python3 --version`)
- OpenCV版本 (`python3 -c "import cv2; print(cv2.__version__)"`)
- 摄像头型号和连接方式
- 完整的错误日志
