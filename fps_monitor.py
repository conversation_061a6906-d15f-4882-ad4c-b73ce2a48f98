#!/usr/bin/env python3
"""
FPS Monitor for Multi-Camera Setup
监控多摄像头设置的FPS性能

This script provides comprehensive FPS monitoring for multiple cameras,
including individual camera FPS, overall system FPS, and resource usage.
"""

import cv2
import time
import threading
import argparse
import sys
from typing import Dict, List, Optional
import numpy as np
from collections import deque


class FPSMonitor:
    """FPS monitoring class for multi-camera setups."""
    
    def __init__(self, camera_indices: List[int], resolution: tuple = (640, 360)):
        """
        Initialize FPS monitor.
        
        Args:
            camera_indices: List of camera indices to monitor
            resolution: Camera resolution as (width, height)
        """
        self.camera_indices = camera_indices
        self.resolution = resolution
        self.cameras: Dict[int, cv2.VideoCapture] = {}
        self.camera_threads: Dict[int, threading.Thread] = {}
        self.camera_frames: Dict[int, np.ndarray] = {}
        self.camera_fps: Dict[int, float] = {}
        self.camera_frame_times: Dict[int, deque] = {}
        self.running = False
        
        # Statistics
        self.start_time = None
        self.total_frames = 0
        self.failed_cameras = []
        
    def initialize_cameras(self) -> bool:
        """Initialize all cameras with specified resolution."""
        print(f"🎥 Initializing {len(self.camera_indices)} cameras...")
        successful_cameras = 0
        
        for cam_idx in self.camera_indices:
            try:
                print(f"  📹 Initializing camera {cam_idx}...")
                cap = cv2.VideoCapture(cam_idx)
                
                if cap.isOpened():
                    # Set buffer size to reduce latency
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    
                    # Force MJPG format to reduce bandwidth
                    cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
                    
                    # Set resolution
                    cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.resolution[0])
                    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.resolution[1])
                    
                    # Test frame capture
                    ret, test_frame = cap.read()
                    if ret and test_frame is not None:
                        actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        
                        self.cameras[cam_idx] = cap
                        self.camera_fps[cam_idx] = 0.0
                        self.camera_frame_times[cam_idx] = deque(maxlen=30)  # Keep last 30 frame times
                        successful_cameras += 1
                        
                        print(f"    ✅ Camera {cam_idx}: {actual_width}x{actual_height}")
                    else:
                        cap.release()
                        self.failed_cameras.append(cam_idx)
                        print(f"    ❌ Camera {cam_idx}: Cannot read frames")
                else:
                    self.failed_cameras.append(cam_idx)
                    print(f"    ❌ Camera {cam_idx}: Cannot open")
                    
                time.sleep(0.2)  # Delay between camera initializations
                
            except Exception as e:
                self.failed_cameras.append(cam_idx)
                print(f"    ❌ Camera {cam_idx}: Error - {e}")
        
        print(f"📊 Summary: {successful_cameras}/{len(self.camera_indices)} cameras initialized successfully")
        if self.failed_cameras:
            print(f"❌ Failed cameras: {self.failed_cameras}")
            
        return successful_cameras > 0
    
    def camera_capture_thread(self, cam_idx: int):
        """Thread function for capturing frames from a single camera."""
        cap = self.cameras[cam_idx]
        consecutive_failures = 0
        max_failures = 10
        
        while self.running and cap.isOpened():
            try:
                ret, frame = cap.read()
                if ret and frame is not None:
                    self.camera_frames[cam_idx] = frame.copy()
                    
                    # Update FPS calculation
                    current_time = time.time()
                    self.camera_frame_times[cam_idx].append(current_time)
                    
                    # Calculate FPS using rolling window
                    if len(self.camera_frame_times[cam_idx]) > 1:
                        time_diff = (self.camera_frame_times[cam_idx][-1] - 
                                   self.camera_frame_times[cam_idx][0])
                        if time_diff > 0:
                            self.camera_fps[cam_idx] = (len(self.camera_frame_times[cam_idx]) - 1) / time_diff
                    
                    consecutive_failures = 0
                    self.total_frames += 1
                else:
                    consecutive_failures += 1
                    if consecutive_failures >= max_failures:
                        print(f"❌ Camera {cam_idx}: Too many consecutive failures, stopping")
                        break
                
                time.sleep(0.03)  # ~30 FPS max
                
            except Exception as e:
                consecutive_failures += 1
                print(f"❌ Camera {cam_idx}: Error in capture thread - {e}")
                if consecutive_failures >= max_failures:
                    break
                time.sleep(0.1)
    
    def start_monitoring(self, duration: Optional[float] = None, display_frames: bool = False):
        """
        Start FPS monitoring.
        
        Args:
            duration: Monitoring duration in seconds (None for infinite)
            display_frames: Whether to display camera frames
        """
        if not self.cameras:
            print("❌ No cameras available for monitoring")
            return
        
        print(f"\n🚀 Starting FPS monitoring...")
        print(f"📊 Monitoring {len(self.cameras)} cameras")
        if duration:
            print(f"⏱️  Duration: {duration} seconds")
        print("Press Ctrl+C to stop\n")
        
        self.running = True
        self.start_time = time.time()
        
        # Start capture threads
        for cam_idx in self.cameras.keys():
            thread = threading.Thread(target=self.camera_capture_thread, args=(cam_idx,))
            thread.daemon = True
            thread.start()
            self.camera_threads[cam_idx] = thread
        
        try:
            last_report_time = time.time()
            report_interval = 2.0  # Report every 2 seconds
            
            while self.running:
                current_time = time.time()
                
                # Check duration
                if duration and (current_time - self.start_time) >= duration:
                    break
                
                # Display frames if requested
                if display_frames:
                    for cam_idx, frame in self.camera_frames.items():
                        if frame is not None:
                            cv2.imshow(f'Camera {cam_idx}', frame)
                    
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        break
                
                # Print periodic reports
                if current_time - last_report_time >= report_interval:
                    self.print_fps_report()
                    last_report_time = current_time
                
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
        
        finally:
            self.stop_monitoring()
    
    def print_fps_report(self):
        """Print current FPS statistics."""
        elapsed_time = time.time() - self.start_time
        overall_fps = self.total_frames / elapsed_time if elapsed_time > 0 else 0
        
        print(f"\n📊 FPS Report (Elapsed: {elapsed_time:.1f}s)")
        print("=" * 50)
        
        for cam_idx in sorted(self.cameras.keys()):
            fps = self.camera_fps.get(cam_idx, 0)
            status = "🟢" if fps > 15 else "🟡" if fps > 5 else "🔴"
            print(f"  {status} Camera {cam_idx}: {fps:6.1f} FPS")
        
        avg_fps = sum(self.camera_fps.values()) / len(self.camera_fps) if self.camera_fps else 0
        print(f"\n  📈 Average Camera FPS: {avg_fps:6.1f}")
        print(f"  🎯 Overall Processing FPS: {overall_fps:6.1f}")
        print(f"  📦 Total Frames: {self.total_frames}")
        
        if self.failed_cameras:
            print(f"  ❌ Failed Cameras: {self.failed_cameras}")
    
    def stop_monitoring(self):
        """Stop monitoring and cleanup resources."""
        print("\n🧹 Cleaning up...")
        self.running = False
        
        # Wait for threads to finish
        for thread in self.camera_threads.values():
            if thread.is_alive():
                thread.join(timeout=1.0)
        
        # Release cameras
        for cap in self.cameras.values():
            cap.release()
        
        cv2.destroyAllWindows()
        
        # Final report
        if self.start_time:
            self.print_final_report()
    
    def print_final_report(self):
        """Print final monitoring statistics."""
        total_time = time.time() - self.start_time
        overall_fps = self.total_frames / total_time if total_time > 0 else 0
        
        print("\n" + "=" * 60)
        print("📋 FINAL MONITORING REPORT")
        print("=" * 60)
        print(f"⏱️  Total Duration: {total_time:.1f} seconds")
        print(f"📦 Total Frames Processed: {self.total_frames}")
        print(f"🎯 Overall Average FPS: {overall_fps:.1f}")
        print(f"📹 Active Cameras: {len(self.cameras)}")
        
        if self.camera_fps:
            max_fps = max(self.camera_fps.values())
            min_fps = min(self.camera_fps.values())
            avg_fps = sum(self.camera_fps.values()) / len(self.camera_fps)
            
            print(f"\n📊 Camera FPS Statistics:")
            print(f"  • Maximum: {max_fps:.1f} FPS")
            print(f"  • Minimum: {min_fps:.1f} FPS")
            print(f"  • Average: {avg_fps:.1f} FPS")
        
        if self.failed_cameras:
            print(f"\n❌ Failed Cameras: {self.failed_cameras}")
        
        print("=" * 60)


def main():
    """Main function for command-line usage."""
    parser = argparse.ArgumentParser(description="Multi-Camera FPS Monitor")
    parser.add_argument(
        "--cameras", 
        type=str, 
        default="0,2,4,6",
        help="Comma-separated camera indices (default: 0,2,4,6)"
    )
    parser.add_argument(
        "--resolution",
        type=str,
        default="640x360",
        help="Camera resolution as WIDTHxHEIGHT (default: 640x360)"
    )
    parser.add_argument(
        "--duration",
        type=float,
        default=30.0,
        help="Monitoring duration in seconds (default: 30)"
    )
    parser.add_argument(
        "--display",
        action="store_true",
        help="Display camera frames (press 'q' to quit)"
    )
    
    args = parser.parse_args()
    
    # Parse camera indices
    try:
        camera_indices = [int(x.strip()) for x in args.cameras.split(",")]
    except ValueError:
        print("❌ Invalid camera indices format")
        sys.exit(1)
    
    # Parse resolution
    try:
        width, height = map(int, args.resolution.split("x"))
        resolution = (width, height)
    except ValueError:
        print("❌ Invalid resolution format")
        sys.exit(1)
    
    print("🎥 Multi-Camera FPS Monitor")
    print(f"📹 Cameras: {camera_indices}")
    print(f"📐 Resolution: {resolution[0]}x{resolution[1]}")
    print(f"⏱️  Duration: {args.duration} seconds")
    
    # Create and run monitor
    monitor = FPSMonitor(camera_indices, resolution)
    
    if monitor.initialize_cameras():
        monitor.start_monitoring(duration=args.duration, display_frames=args.display)
    else:
        print("❌ Failed to initialize any cameras")
        sys.exit(1)


if __name__ == "__main__":
    main()
