#!/usr/bin/env python3
"""
LED灯光模式配置文件
5种颜色 × 3种模式 = 15种组合，每种模式总次数都是5
"""

# LED颜色常量
class LEDColors:
    """LED颜色常量定义（使用0x3f亮度）"""
    RED = 0x3f0000      # 红色
    BLUE = 0x00003f     # 蓝色
    GREEN = 0x003f00    # 绿色
    YELLOW = 0x3f3f00   # 黄色
    WHITE = 0x3f3f3f    # 白色
    BLACK = 0x000000    # 黑色（关闭）

# 统一的计数模式
class CountPatterns:
    """统一的计数模式"""
    FLASH = [12, 6]     # 闪烁模式：亮12次，灭6次
    EQUAL = [12, 12]    # 均等模式：亮12次，灭12次
    ALWAYS_ON = [24] # 常亮模式

# 预设LED模式 - 5种颜色 × 3种模式 = 15种组合
LED_PATTERNS = {
    # 红色 - 3种模式
    "red_flash": {
        "name": "红色闪烁",
        "colors": [LEDColors.RED, LEDColors.BLACK],
        "counts": CountPatterns.FLASH,
        "description": "红色闪烁"
    },

    "red_equal": {
        "name": "红色均等",
        "colors": [LEDColors.RED, LEDColors.BLACK],
        "counts": CountPatterns.EQUAL,
        "description": "红色均等闪烁"
    },

    "red_on": {
        "name": "红色常亮",
        "colors": [LEDColors.RED],
        "counts": CountPatterns.ALWAYS_ON,
        "description": "红色常亮"
    },

    # 蓝色 - 3种模式
    "blue_flash": {
        "name": "蓝色闪烁",
        "colors": [LEDColors.BLUE, LEDColors.BLACK],
        "counts": CountPatterns.FLASH,
        "description": "蓝色闪烁"
    },

    "blue_equal": {
        "name": "蓝色均等",
        "colors": [LEDColors.BLUE, LEDColors.BLACK],
        "counts": CountPatterns.EQUAL,
        "description": "蓝色均等闪烁"
    },

    "blue_on": {
        "name": "蓝色常亮",
        "colors": [LEDColors.BLUE],
        "counts": CountPatterns.ALWAYS_ON,
        "description": "蓝色常亮"
    },

    # 绿色 - 3种模式
    "green_flash": {
        "name": "绿色闪烁",
        "colors": [LEDColors.GREEN, LEDColors.BLACK],
        "counts": CountPatterns.FLASH,
        "description": "绿色闪烁"
    },

    "green_equal": {
        "name": "绿色均等",
        "colors": [LEDColors.GREEN, LEDColors.BLACK],
        "counts": CountPatterns.EQUAL,
        "description": "绿色均等闪烁"
    },

    "green_on": {
        "name": "绿色常亮",
        "colors": [LEDColors.GREEN],
        "counts": CountPatterns.ALWAYS_ON,
        "description": "绿色常亮"
    },

    # 黄色 - 3种模式
    "yellow_flash": {
        "name": "黄色闪烁",
        "colors": [LEDColors.YELLOW, LEDColors.BLACK],
        "counts": CountPatterns.FLASH,
        "description": "黄色闪烁"
    },

    "yellow_equal": {
        "name": "黄色均等",
        "colors": [LEDColors.YELLOW, LEDColors.BLACK],
        "counts": CountPatterns.EQUAL,
        "description": "黄色均等闪烁"
    },

    "yellow_on": {
        "name": "黄色常亮",
        "colors": [LEDColors.YELLOW],
        "counts": CountPatterns.ALWAYS_ON,
        "description": "黄色常亮"
    },

    # 白色 - 3种模式
    "white_flash": {
        "name": "白色闪烁",
        "colors": [LEDColors.WHITE, LEDColors.BLACK],
        "counts": CountPatterns.FLASH,
        "description": "白色闪烁"
    },

    "white_equal": {
        "name": "白色均等",
        "colors": [LEDColors.WHITE, LEDColors.BLACK],
        "counts": CountPatterns.EQUAL,
        "description": "白色均等闪烁"
    },

    "white_on": {
        "name": "白色常亮",
        "colors": [LEDColors.WHITE],
        "counts": CountPatterns.ALWAYS_ON,
        "description": "白色常亮"
    },

    # 关闭模式
    "off": {
        "name": "关闭",
        "colors": [LEDColors.BLACK],
        "counts": CountPatterns.ALWAYS_ON,
        "description": "关闭所有LED"
    }
}

def get_pattern(pattern_name):
    """
    获取指定的LED模式
    
    Args:
        pattern_name: 模式名称
        
    Returns:
        dict: 包含colors和counts的字典，如果模式不存在则返回None
    """
    if pattern_name in LED_PATTERNS:
        pattern = LED_PATTERNS[pattern_name]
        return {
            'colors': pattern['colors'],
            'counts': pattern['counts']
        }
    else:
        print(f"错误: 未找到模式 '{pattern_name}'")
        return None

def list_patterns():
    """列出所有可用的LED模式"""
    print("可用的LED模式:")
    print("=" * 60)
    for name, pattern in LED_PATTERNS.items():
        colors_str = " -> ".join([f"0x{c:06X}" for c in pattern['colors']])
        print(f"{name:15} | {pattern['name']:12} | {pattern['description']}")
        print(f"{'':15} | 颜色序列: {colors_str}")
        print(f"{'':15} | 计数序列: {pattern['counts']}")
        print("-" * 60)

def create_custom_pattern(name, colors, counts, description="自定义模式"):
    """
    创建自定义LED模式
    
    Args:
        name: 模式名称
        colors: 颜色列表
        counts: 计数列表  
        description: 模式描述
    """
    LED_PATTERNS[name] = {
        "name": name,
        "colors": colors,
        "counts": counts,
        "description": description
    }
    print(f"已创建自定义模式: {name}")

if __name__ == "__main__":
    # 显示所有可用模式
    list_patterns()
    
    # 示例：获取特定模式
    print("\n示例用法:")
    pattern = get_pattern("red_flash")
    if pattern:
        print(f"红色闪烁模式: {pattern}")
    
    # 示例：创建自定义模式
    create_custom_pattern(
        name="my_pattern",
        colors=[LEDColors.PURPLE, LEDColors.CYAN, LEDColors.BLACK],
        counts=[5, 5, 3],
        description="紫青交替闪烁"
    )
