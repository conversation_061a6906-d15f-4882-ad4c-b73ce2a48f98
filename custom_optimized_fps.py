#!/usr/bin/env python3
"""
Custom Optimized FPS Monitor
基于测试结果的定制优化FPS监控器

Based on your test results:
- Baseline: 28.0 FPS (best)
- MJPEG helps slightly
- Low buffer hurts performance (-48%)
- Lower resolution hurts performance (-24%)
"""

import cv2
import time
import threading
from typing import Dict, List
import numpy as np


def test_custom_optimized_cameras(camera_indices: List[int] = [0, 2, 4, 6], duration: float = 20.0):
    """Test cameras with custom optimizations based on your hardware profile."""
    print("🎯 Custom Optimized Camera Test")
    print("Based on your hardware profile:")
    print("  ✅ MJPEG codec helps")
    print("  ❌ Low buffer hurts (-48%)")
    print("  ❌ Low resolution hurts (-24%)")
    print("=" * 50)
    
    cameras = {}
    camera_threads = {}
    camera_fps = {}
    camera_frame_counts = {}
    running = True
    
    # Initialize cameras with YOUR optimal settings
    print("📹 Initializing cameras with YOUR optimal settings...")
    for cam_idx in camera_indices:
        try:
            cap = cv2.VideoCapture(cam_idx)
            
            if cap.isOpened():
                # YOUR optimal settings based on test results
                cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))  # +0.4% improvement
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 3)  # Higher buffer (not 1) since low buffer hurt you
                
                # Keep native resolution since lower resolution hurt performance
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)  # Standard resolution
                
                # Don't force high FPS since it hurt your performance
                # Let camera use its optimal FPS
                
                # Test capture
                ret, frame = cap.read()
                if ret and frame is not None:
                    actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    actual_fps = cap.get(cv2.CAP_PROP_FPS)
                    
                    cameras[cam_idx] = cap
                    camera_fps[cam_idx] = 0.0
                    camera_frame_counts[cam_idx] = 0
                    
                    print(f"  ✅ Camera {cam_idx}: {actual_width}x{actual_height} @ {actual_fps} FPS")
                else:
                    cap.release()
                    print(f"  ❌ Camera {cam_idx}: Cannot read frames")
            else:
                print(f"  ❌ Camera {cam_idx}: Cannot open")
                
        except Exception as e:
            print(f"  ❌ Camera {cam_idx}: Error - {e}")
    
    if not cameras:
        print("❌ No cameras available!")
        return
    
    print(f"\n🚀 Starting test with {len(cameras)} cameras using YOUR optimal settings...")
    
    def optimized_capture_thread(cam_idx: int, cap: cv2.VideoCapture):
        """Capture thread optimized for your hardware."""
        local_count = 0
        start_time = time.time()
        last_fps_update = start_time
        
        while running:
            try:
                ret, frame = cap.read()
                if ret:
                    local_count += 1
                    camera_frame_counts[cam_idx] += 1
                    
                    # Update FPS every 20 frames
                    if local_count % 20 == 0:
                        current_time = time.time()
                        elapsed = current_time - last_fps_update
                        if elapsed > 0:
                            camera_fps[cam_idx] = 20 / elapsed
                        last_fps_update = current_time
                
                # Moderate delay - not too aggressive since that hurt your performance
                time.sleep(0.02)  # 20ms = ~50 FPS theoretical max
                
            except Exception:
                time.sleep(0.05)
    
    # Start threads
    start_time = time.time()
    for cam_idx, cap in cameras.items():
        thread = threading.Thread(target=optimized_capture_thread, args=(cam_idx, cap))
        thread.daemon = True
        thread.start()
        camera_threads[cam_idx] = thread
    
    # Monitor progress
    try:
        last_report = time.time()
        while time.time() - start_time < duration:
            current_time = time.time()
            
            # Report every 4 seconds
            if current_time - last_report >= 4.0:
                elapsed = current_time - start_time
                print(f"\n⏱️  Progress: {elapsed:.1f}s / {duration:.1f}s")
                
                for cam_idx in sorted(cameras.keys()):
                    fps = camera_fps.get(cam_idx, 0)
                    frames = camera_frame_counts.get(cam_idx, 0)
                    
                    # Compare to your baseline
                    if fps >= 25:
                        status = "🟢 EXCELLENT"
                    elif fps >= 20:
                        status = "🟡 GOOD"
                    elif fps >= 15:
                        status = "🟠 FAIR"
                    else:
                        status = "🔴 POOR"
                    
                    baseline_comparison = ""
                    if fps > 0:
                        vs_baseline = ((fps - 28.0) / 28.0) * 100
                        baseline_comparison = f" (vs baseline: {vs_baseline:+.1f}%)"
                    
                    print(f"  {status} Camera {cam_idx}: {fps:6.1f} FPS{baseline_comparison}")
                
                last_report = current_time
            
            time.sleep(1.0)
    
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    
    finally:
        running = False
        
        # Wait for threads
        for thread in camera_threads.values():
            thread.join(timeout=1.0)
        
        # Final results
        total_time = time.time() - start_time
        print(f"\n📊 CUSTOM OPTIMIZED RESULTS ({total_time:.1f}s)")
        print("=" * 60)
        
        total_frames = 0
        fps_values = []
        
        for cam_idx in sorted(cameras.keys()):
            frames = camera_frame_counts.get(cam_idx, 0)
            fps = frames / total_time if total_time > 0 else 0
            total_frames += frames
            fps_values.append(fps)
            
            # Compare to your baseline (28.0 FPS)
            vs_baseline = ((fps - 28.0) / 28.0) * 100 if fps > 0 else -100
            
            if fps >= 25:
                status = "🟢"
            elif fps >= 20:
                status = "🟡"
            elif fps >= 15:
                status = "🟠"
            else:
                status = "🔴"
            
            print(f"  {status} Camera {cam_idx}: {fps:6.1f} FPS (vs baseline: {vs_baseline:+5.1f}%)")
        
        if fps_values:
            avg_fps = sum(fps_values) / len(fps_values)
            max_fps = max(fps_values)
            min_fps = min(fps_values)
            combined_fps = total_frames / total_time if total_time > 0 else 0
            
            # Overall comparison to baseline
            avg_vs_baseline = ((avg_fps - 28.0) / 28.0) * 100
            
            print(f"\n📈 Multi-Camera Summary:")
            print(f"  • Average FPS: {avg_fps:.1f} (vs single cam baseline: {avg_vs_baseline:+.1f}%)")
            print(f"  • Maximum FPS: {max_fps:.1f}")
            print(f"  • Minimum FPS: {min_fps:.1f}")
            print(f"  • Combined FPS: {combined_fps:.1f}")
            print(f"  • Total Frames: {total_frames:,}")
            
            # Performance assessment
            if avg_fps >= 22:
                print(f"  🎉 EXCELLENT: Multi-camera performance maintained!")
                print(f"     Your hardware handles multiple cameras very well")
            elif avg_fps >= 18:
                print(f"  👍 VERY GOOD: Good multi-camera performance")
                print(f"     Reasonable degradation from single camera")
            elif avg_fps >= 15:
                print(f"  ✅ GOOD: Acceptable multi-camera performance")
                print(f"     Consider reducing camera count for higher FPS")
            elif avg_fps >= 12:
                print(f"  ⚠️  FAIR: Multi-camera causing significant impact")
                print(f"     USB bandwidth may be the limiting factor")
            else:
                print(f"  ❌ POOR: Multi-camera performance severely impacted")
                print(f"     Try with only 2 cameras")
            
            # Specific recommendations based on results
            print(f"\n💡 Recommendations based on YOUR hardware:")
            if avg_fps < 20:
                print(f"  • Try with only 2 cameras (0,2) instead of {len(cameras)}")
                print(f"  • Your single camera gets 28 FPS, so 2 cameras might get ~20-25 FPS")
            if min_fps < avg_fps * 0.8:
                print(f"  • Some cameras perform worse - check USB connections")
                print(f"  • Consider using only the best performing cameras")
            
            print(f"  • Keep MJPEG codec (it helped you)")
            print(f"  • Keep buffer size 3+ (low buffer hurt your performance)")
            print(f"  • Keep 640x480 resolution (lower resolution hurt you)")
        
        # Cleanup
        for cap in cameras.values():
            cap.release()
        
        cv2.destroyAllWindows()


def test_buffer_size_optimization(cam_idx: int = 0):
    """Test different buffer sizes to find optimal for your hardware."""
    print(f"🔬 Buffer Size Optimization for Camera {cam_idx}")
    print("Based on your results, low buffer hurt performance (-48%)")
    print("Let's find your optimal buffer size...")
    print("=" * 50)
    
    buffer_sizes = [1, 2, 3, 4, 5, 6, 8, 10]
    results = {}
    
    for buffer_size in buffer_sizes:
        print(f"\n📊 Testing buffer size: {buffer_size}")
        
        try:
            cap = cv2.VideoCapture(cam_idx)
            if not cap.isOpened():
                print(f"  ❌ Cannot open camera {cam_idx}")
                continue
            
            # Apply your optimal settings
            cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
            cap.set(cv2.CAP_PROP_BUFFERSIZE, buffer_size)
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            
            # Test FPS
            frame_count = 0
            start_time = time.time()
            test_duration = 4.0
            
            while time.time() - start_time < test_duration:
                ret, frame = cap.read()
                if ret:
                    frame_count += 1
                time.sleep(0.02)  # Moderate delay
            
            elapsed = time.time() - start_time
            fps = frame_count / elapsed
            results[buffer_size] = fps
            
            # Compare to baseline
            vs_baseline = ((fps - 28.0) / 28.0) * 100
            print(f"  📈 Buffer {buffer_size}: {fps:.1f} FPS ({vs_baseline:+.1f}% vs baseline)")
            
            cap.release()
            
        except Exception as e:
            print(f"  ❌ Buffer {buffer_size}: Error - {e}")
            results[buffer_size] = 0.0
    
    # Find optimal buffer size
    if results:
        best_buffer = max(results.items(), key=lambda x: x[1])
        print(f"\n🏆 OPTIMAL BUFFER SIZE: {best_buffer[0]} ({best_buffer[1]:.1f} FPS)")
        
        print(f"\n📊 Buffer Size Results:")
        for buffer_size, fps in results.items():
            vs_baseline = ((fps - 28.0) / 28.0) * 100
            star = "⭐" if buffer_size == best_buffer[0] else "  "
            print(f"  {star} Buffer {buffer_size:2d}: {fps:5.1f} FPS ({vs_baseline:+5.1f}%)")


def main():
    """Main function."""
    print("🎯 Custom Optimization Based on Your Test Results")
    print("=" * 55)
    print("Your hardware profile:")
    print("  • Baseline performance: 28.0 FPS (excellent!)")
    print("  • MJPEG codec: slight improvement")
    print("  • Low buffer: major performance hit (-48%)")
    print("  • Low resolution: performance hit (-24%)")
    print("")
    
    choice = input("Choose test:\n1) Multi-camera test with your optimal settings\n2) Find your optimal buffer size\nChoice (1-2): ").strip()
    
    if choice == "2":
        test_buffer_size_optimization()
    else:
        # Multi-camera test
        try:
            camera_input = input("\nEnter camera indices (default: 0,2,4,6): ").strip()
            if camera_input:
                camera_indices = [int(x.strip()) for x in camera_input.split(",")]
            else:
                camera_indices = [0, 2, 4, 6]
        except ValueError:
            print("Invalid input, using default: 0,2,4,6")
            camera_indices = [0, 2, 4, 6]
        
        test_custom_optimized_cameras(camera_indices, 20.0)


if __name__ == "__main__":
    main()
